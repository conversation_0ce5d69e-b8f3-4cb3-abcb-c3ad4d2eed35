@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .test-container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .test-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-content {
            font-size: 1.2em;
            line-height: 1.6;
            text-align: center;
        }
        .success-icon {
            font-size: 4em;
            color: #2ecc71;
            text-align: center;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .info-box {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .timestamp {
            font-size: 0.9em;
            opacity: 0.8;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="success-icon">✅</div>
        <h1 class="test-title">菜单功能测试成功！</h1>
        
        <div class="test-content">
            <p>恭喜！如果您能看到这个页面，说明现代化菜单的页面跳转功能已经正常工作了。</p>
            
            <div class="info-box">
                <h3>🎯 测试结果</h3>
                <p>✅ 菜单点击事件正常</p>
                <p>✅ 标签页创建成功</p>
                <p>✅ 页面加载正常</p>
                <p>✅ iframe嵌入正常</p>
            </div>
            
            <div class="info-box">
                <h3>🚀 功能特性</h3>
                <p>• 现代化的菜单设计</p>
                <p>• 流畅的动画效果</p>
                <p>• 智能的标签页管理</p>
                <p>• 完整的响应式支持</p>
            </div>
            
            <div class="info-box">
                <h3>📱 使用说明</h3>
                <p>1. 点击左侧菜单项可以打开对应页面</p>
                <p>2. 重复点击相同菜单会切换到已打开的标签页</p>
                <p>3. 可以通过标签页右上角的 ✕ 关闭页面</p>
                <p>4. 支持多个页面同时打开</p>
            </div>
        </div>
        
        <div class="timestamp">
            页面加载时间: <span id="loadTime"></span>
        </div>
    </div>

    <script>
        // 显示加载时间
        document.getElementById('loadTime').textContent = new Date().toLocaleString();
        
        // 向父窗口发送消息，确认页面加载成功
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({
                type: 'pageLoaded',
                title: '测试页面',
                timestamp: new Date().toISOString()
            }, '*');
        }
        
        console.log('测试页面加载成功！');
    </script>
</body>
</html>
