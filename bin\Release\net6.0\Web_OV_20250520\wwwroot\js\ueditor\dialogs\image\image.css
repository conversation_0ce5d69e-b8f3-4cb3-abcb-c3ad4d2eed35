@charset "utf-8";
/* dialog样式 */
.wrapper {
    zoom: 1;
    width: 630px;
    *width: 626px;
    height: 380px;
    margin: 0 auto;
    padding: 10px;
    position: relative;
    font-family: sans-serif;
}

/*tab样式框大小*/
.tabhead {
    float:left;
}
.tabbody {
    width: 100%;
    height: 346px;
    position: relative;
    clear: both;
}

.tabbody .panel {
    position: absolute;
    width: 0;
    height: 0;
    background: #fff;
    overflow: hidden;
    display: none;
}

.tabbody .panel.focus {
    width: 100%;
    height: 346px;
    display: block;
}

/* 图片对齐方式 */
.alignBar{
    float:right;
    margin-top: 5px;
    position: relative;
}

.alignBar .algnLabel{
    float:left;
    height: 20px;
    line-height: 20px;
}

.alignBar #alignIcon{
    zoom:1;
    _display: inline;
    display: inline-block;
    position: relative;
}
.alignBar #alignIcon span{
    float: left;
    cursor: pointer;
    display: block;
    width: 19px;
    height: 17px;
    margin-right: 3px;
    margin-left: 3px;
    background-image: url(./images/alignicon.jpg);
}
.alignBar #alignIcon .none-align{
    background-position: 0 -18px;
}
.alignBar #alignIcon .left-align{
    background-position: -20px -18px;
}
.alignBar #alignIcon .right-align{
    background-position: -40px -18px;
}
.alignBar #alignIcon .center-align{
    background-position: -60px -18px;
}
.alignBar #alignIcon .none-align.focus{
    background-position: 0 0;
}
.alignBar #alignIcon .left-align.focus{
    background-position: -20px 0;
}
.alignBar #alignIcon .right-align.focus{
    background-position: -40px 0;
}
.alignBar #alignIcon .center-align.focus{
    background-position: -60px 0;
}




/* 远程图片样式 */
#remote {
    z-index: 200;
}

#remote .top{
    width: 100%;
    margin-top: 25px;
}
#remote .left{
    display: block;
    float: left;
    width: 300px;
    height:10px;
}
#remote .right{
    display: block;
    float: right;
    width: 300px;
    height:10px;
}
#remote .row{
    margin-left: 20px;
    clear: both;
    height: 40px;
}

#remote .row label{
    text-align: center;
    width: 50px;
    zoom:1;
    _display: inline;
    display:inline-block;
    vertical-align: middle;
}
#remote .row label.algnLabel{
    float: left;

}

#remote input.text{
    width: 150px;
    padding: 3px 6px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
#remote input.text:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}
#remote #url{
    width: 500px;
    margin-bottom: 2px;
}
#remote #width,
#remote #height{
    width: 20px;
    margin-left: 2px;
    margin-right: 2px;
}
#remote #border,
#remote #vhSpace,
#remote #title{
    width: 180px;
    margin-right: 5px;
}
#remote #lock{
}
#remote #lockicon{
    zoom: 1;
    _display:inline;
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url("../../themes/default/images/lock.gif") -13px -13px no-repeat;
    vertical-align: middle;
}
#remote #preview{
    clear: both;
    width: 260px;
    height: 240px;
    z-index: 9999;
    margin-top: 10px;
    background-color: #eee;
    overflow: hidden;
}

/* 上传图片 */
.tabbody #upload.panel {
    width: 0;
    height: 0;
    overflow: hidden;
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
    background: #fff;
    display: block;
}

.tabbody #upload.panel.focus {
    width: 100%;
    height: 346px;
    display: block;
    clip: auto;
}

#upload .queueList {
    margin: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
}

#upload p {
    margin: 0;
}

.element-invisible {
    width: 0 !important;
    height: 0 !important;
    border: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
}

#upload .placeholder {
    margin: 10px;
    border: 2px dashed #e6e6e6;
    *border: 0px dashed #e6e6e6;
    height: 172px;
    padding-top: 150px;
    text-align: center;
    background: url(./images/image.png) center 70px no-repeat;
    color: #cccccc;
    font-size: 18px;
    position: relative;
    top:0;
    *top: 10px;
}

#upload .placeholder .webuploader-pick {
    font-size: 18px;
    background: #00b7ee;
    border-radius: 3px;
    line-height: 44px;
    padding: 0 30px;
    *width: 120px;
    color: #fff;
    display: inline-block;
    margin: 0 auto 20px auto;
    cursor: pointer;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

#upload .placeholder .webuploader-pick-hover {
    background: #00a2d4;
}


#filePickerContainer {
    text-align: center;
}

#upload .placeholder .flashTip {
    color: #666666;
    font-size: 12px;
    position: absolute;
    width: 100%;
    text-align: center;
    bottom: 20px;
}

#upload .placeholder .flashTip a {
    color: #0785d1;
    text-decoration: none;
}

#upload .placeholder .flashTip a:hover {
    text-decoration: underline;
}

#upload .placeholder.webuploader-dnd-over {
    border-color: #999999;
}

#upload .filelist {
    list-style: none;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    height: 300px;
}

#upload .filelist:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    overflow: hidden;
    clear: both;
    position: relative;
}

#upload .filelist li {
    width: 113px;
    height: 113px;
    background: url(./images/bg.png);
    text-align: center;
    margin: 9px 0 0 9px;
    *margin: 6px 0 0 6px;
    position: relative;
    display: block;
    float: left;
    overflow: hidden;
    font-size: 12px;
}

#upload .filelist li p.log {
    position: relative;
    top: -45px;
}

#upload .filelist li p.title {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    top: 5px;
    text-indent: 5px;
    text-align: left;
}

#upload .filelist li p.progress {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    height: 8px;
    overflow: hidden;
    z-index: 50;
    margin: 0;
    border-radius: 0;
    background: none;
    -webkit-box-shadow: 0 0 0;
}

#upload .filelist li p.progress span {
    display: none;
    overflow: hidden;
    width: 0;
    height: 100%;
    background: #1483d8 url(./images/progress.png) repeat-x;

    -webit-transition: width 200ms linear;
    -moz-transition: width 200ms linear;
    -o-transition: width 200ms linear;
    -ms-transition: width 200ms linear;
    transition: width 200ms linear;

    -webkit-animation: progressmove 2s linear infinite;
    -moz-animation: progressmove 2s linear infinite;
    -o-animation: progressmove 2s linear infinite;
    -ms-animation: progressmove 2s linear infinite;
    animation: progressmove 2s linear infinite;

    -webkit-transform: translateZ(0);
}

@-webkit-keyframes progressmove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 17px 0;
    }
}

@-moz-keyframes progressmove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 17px 0;
    }
}

@keyframes progressmove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 17px 0;
    }
}

#upload .filelist li p.imgWrap {
    position: relative;
    z-index: 2;
    line-height: 113px;
    vertical-align: middle;
    overflow: hidden;
    width: 113px;
    height: 113px;

    -webkit-transform-origin: 50% 50%;
    -moz-transform-origin: 50% 50%;
    -o-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%;

    -webit-transition: 200ms ease-out;
    -moz-transition: 200ms ease-out;
    -o-transition: 200ms ease-out;
    -ms-transition: 200ms ease-out;
    transition: 200ms ease-out;
}

#upload .filelist li img {
    width: 100%;
}

#upload .filelist li p.error {
    background: #f43838;
    color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    height: 28px;
    line-height: 28px;
    width: 100%;
    z-index: 100;
    display:none;
}

#upload .filelist li .success {
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 40px;
    width: 100%;
    z-index: 200;
    background: url(./images/success.png) no-repeat right bottom;
    background: url(./images/success.gif) no-repeat right bottom \9;
}

#upload .filelist li.filePickerBlock {
    width: 113px;
    height: 113px;
    background: url(./images/image.png) no-repeat center 12px;
    border: 1px solid #eeeeee;
    border-radius: 0;
}
#upload .filelist li.filePickerBlock div.webuploader-pick  {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    opacity: 0;
    background: none;
    font-size: 0;
}

#upload .filelist div.file-panel {
    position: absolute;
    height: 0;
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#80000000', endColorstr='#80000000') \0;
    background: rgba(0, 0, 0, 0.5);
    width: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: 300;
}

#upload .filelist div.file-panel span {
    width: 24px;
    height: 24px;
    display: inline;
    float: right;
    text-indent: -9999px;
    overflow: hidden;
    background: url(./images/icons.png) no-repeat;
    background: url(./images/icons.gif) no-repeat \9;
    margin: 5px 1px 1px;
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#upload .filelist div.file-panel span.rotateLeft {
    display:none;
    background-position: 0 -24px;
}

#upload .filelist div.file-panel span.rotateLeft:hover {
    background-position: 0 0;
}

#upload .filelist div.file-panel span.rotateRight {
    display:none;
    background-position: -24px -24px;
}

#upload .filelist div.file-panel span.rotateRight:hover {
    background-position: -24px 0;
}

#upload .filelist div.file-panel span.cancel {
    background-position: -48px -24px;
}

#upload .filelist div.file-panel span.cancel:hover {
    background-position: -48px 0;
}

#upload .statusBar {
    height: 45px;
    border-bottom: 1px solid #dadada;
    margin: 0 10px;
    padding: 0;
    line-height: 45px;
    vertical-align: middle;
    position: relative;
}

#upload .statusBar .progress {
    border: 1px solid #1483d8;
    width: 198px;
    background: #fff;
    height: 18px;
    position: absolute;
    top: 12px;
    display: none;
    text-align: center;
    line-height: 18px;
    color: #6dbfff;
    margin: 0 10px 0 0;
}
#upload .statusBar .progress span.percentage {
    width: 0;
    height: 100%;
    left: 0;
    top: 0;
    background: #1483d8;
    position: absolute;
}
#upload .statusBar .progress span.text {
    position: relative;
    z-index: 10;
}

#upload .statusBar .info {
    display: inline-block;
    font-size: 14px;
    color: #666666;
}

#upload .statusBar .btns {
    position: absolute;
    top: 7px;
    right: 0;
    line-height: 30px;
}

#filePickerBtn {
    display: inline-block;
    float: left;
}
#upload .statusBar .btns .webuploader-pick,
#upload .statusBar .btns .uploadBtn,
#upload .statusBar .btns .uploadBtn.state-uploading,
#upload .statusBar .btns .uploadBtn.state-paused {
    background: #ffffff;
    border: 1px solid #cfcfcf;
    color: #565656;
    padding: 0 18px;
    display: inline-block;
    border-radius: 3px;
    margin-left: 10px;
    cursor: pointer;
    font-size: 14px;
    float: left;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
#upload .statusBar .btns .webuploader-pick-hover,
#upload .statusBar .btns .uploadBtn:hover,
#upload .statusBar .btns .uploadBtn.state-uploading:hover,
#upload .statusBar .btns .uploadBtn.state-paused:hover {
    background: #f0f0f0;
}

#upload .statusBar .btns .uploadBtn,
#upload .statusBar .btns .uploadBtn.state-paused{
    background: #00b7ee;
    color: #fff;
    border-color: transparent;
}
#upload .statusBar .btns .uploadBtn:hover,
#upload .statusBar .btns .uploadBtn.state-paused:hover{
    background: #00a2d4;
}

#upload .statusBar .btns .uploadBtn.disabled {
    pointer-events: none;
    filter:alpha(opacity=60);
    -moz-opacity:0.6;
    -khtml-opacity: 0.6;
    opacity: 0.6;
}



/* 图片管理样式 */
#online {
    width: 100%;
    height: 336px;
    padding: 10px 0 0 0;
}
#online #imageList{
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
}
#online ul {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0;
}
#online li {
    float: left;
    display: block;
    list-style: none;
    padding: 0;
    width: 113px;
    height: 113px;
    margin: 0 0 9px 9px;
    *margin: 0 0 6px 6px;
    background-color: #eee;
    overflow: hidden;
    cursor: pointer;
    position: relative;
}
#online li.clearFloat {
    float: none;
    clear: both;
    display: block;
    width:0;
    height:0;
    margin: 0;
    padding: 0;
}
#online li img {
    cursor: pointer;
}
#online li .icon {
    cursor: pointer;
    width: 113px;
    height: 113px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    border: 0;
    background-repeat: no-repeat;
}
#online li .icon:hover {
    width: 107px;
    height: 107px;
    border: 3px solid #1094fa;
}
#online li.selected .icon {
    background-image: url(images/success.png);
    background-image: url(images/success.gif)\9;
    background-position: 75px 75px;
}
#online li.selected .icon:hover {
    width: 107px;
    height: 107px;
    border: 3px solid #1094fa;
    background-position: 72px 72px;
}


/* 图片搜索样式 */
#search .searchBar {
    width: 100%;
    height: 30px;
    margin: 10px 0 5px 0;
    padding: 0;
}

#search input.text{
    width: 150px;
    padding: 3px 6px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
#search input.text:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6);
}
#search input.searchTxt {
    margin-left:5px;
    padding-left: 5px;
    background: #FFF;
    width: 300px;
    *width: 260px;
    height: 21px;
    line-height: 21px;
    float: left;
    dislay: block;
}

#search .searchType {
    width: 65px;
    height: 28px;
    padding:0;
    line-height: 28px;
    border: 1px solid #d7d7d7;
    border-radius: 0;
    vertical-align: top;
    margin-left: 5px;
    float: left;
    dislay: block;
}

#search #searchBtn,
#search #searchReset {
    display: inline-block;
    margin-bottom: 0;
    margin-right: 5px;
    padding: 4px 10px;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    font-size: 14px;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: top;
    float: right;
}

#search #searchBtn {
    color: white;
    border-color: #285e8e;
    background-color: #3b97d7;
}
#search #searchReset {
    color: #333;
    border-color: #ccc;
    background-color: #fff;
}
#search #searchBtn:hover {
    background-color: #3276b1;
}
#search #searchReset:hover {
    background-color: #eee;
}

#search .msg {
    margin-left: 5px;
}

#search .searchList{
    width: 100%;
    height: 300px;
    overflow: hidden;
    clear: both;
}
#search .searchList ul{
    margin:0;
    padding:0;
    list-style:none;
    clear: both;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    zoom: 1;
    position: relative;
}

#search .searchList li {
    list-style:none;
    float: left;
    display: block;
    width: 115px;
    margin: 5px 10px 5px 20px;
    *margin: 5px 10px 5px 15px;
    padding:0;
    font-size: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .3);
    -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, .3);
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, .3);
    position: relative;
    vertical-align: top;
    text-align: center;
    overflow: hidden;
    cursor: pointer;
    filter: alpha(Opacity=100);
    -moz-opacity: 1;
    opacity: 1;
    border: 2px solid #eee;
}

#search .searchList li.selected {
    filter: alpha(Opacity=40);
    -moz-opacity: 0.4;
    opacity: 0.4;
    border: 2px solid #00a0e9;
}

#search .searchList li p {
    background-color: #eee;
    margin: 0;
    padding: 0;
    position: relative;
    width:100%;
    height:115px;
    overflow: hidden;
}

#search .searchList li p img {
    cursor: pointer;
    border: 0;
}

#search .searchList li a {
    color: #999;
    border-top: 1px solid #F2F2F2;
    background: #FAFAFA;
    text-align: center;
    display: block;
    padding: 0 5px;
    width: 105px;
    height:32px;
    line-height:32px;
    white-space:nowrap;
    text-overflow:ellipsis;
    text-decoration: none;
    overflow: hidden;
    word-break: break-all;
}

#search .searchList a:hover {
    text-decoration: underline;
    color: #333;
}
#search .searchList .clearFloat{
    clear: both;
}