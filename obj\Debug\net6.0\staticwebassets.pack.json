{"Files": [{"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\AIKO FR.xls", "PackagePath": "staticwebassets\\Report\\AIKO FR.xls"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\BOL Format.xlsx", "PackagePath": "staticwebassets\\Report\\BOL Format.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\BOL Format_TYDUS.xlsx", "PackagePath": "staticwebassets\\Report\\BOL Format_TYDUS.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\Delivery Order.xls", "PackagePath": "staticwebassets\\Report\\Delivery Order.xls"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\Delivery Order.xlsx", "PackagePath": "staticwebassets\\Report\\Delivery Order.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\FRSystosolar.xls", "PackagePath": "staticwebassets\\Report\\FRSystosolar.xls"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\FRSystosolar_merge.xls", "PackagePath": "staticwebassets\\Report\\FRSystosolar_merge.xls"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\HIH_AD_CVD_Report.xls", "PackagePath": "staticwebassets\\Report\\HIH_AD_CVD_Report.xls"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\Inbound Notice-SUNOVA.xlsx", "PackagePath": "staticwebassets\\Report\\Inbound Notice-SUNOVA.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\Outbound Notice-SUNOVA.xlsx", "PackagePath": "staticwebassets\\Report\\Outbound Notice-SUNOVA.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\RMA DELIVERY REPORT.xlsx", "PackagePath": "staticwebassets\\Report\\RMA DELIVERY REPORT.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\Report\\ReleaseOrder.xlsx", "PackagePath": "staticwebassets\\Report\\ReleaseOrder.xlsx"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\app\\app.js", "PackagePath": "staticwebassets\\app\\app.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\bundle\\myApp.js", "PackagePath": "staticwebassets\\bundle\\myApp.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\bundle\\myApp.min.js", "PackagePath": "staticwebassets\\bundle\\myApp.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\bootstrap-nav.css", "PackagePath": "staticwebassets\\css\\bootstrap-nav.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\btnStyle.css", "PackagePath": "staticwebassets\\css\\btnStyle.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\custom.css", "PackagePath": "staticwebassets\\css\\custom.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\formpreview.css", "PackagePath": "staticwebassets\\css\\formpreview.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\images.css", "PackagePath": "staticwebassets\\css\\images.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\login.css", "PackagePath": "staticwebassets\\css\\login.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\main.css", "PackagePath": "staticwebassets\\css\\main.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\metroStyle\\img\\line_conn.png", "PackagePath": "staticwebassets\\css\\metroStyle\\img\\line_conn.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\metroStyle\\img\\loading.gif", "PackagePath": "staticwebassets\\css\\metroStyle\\img\\loading.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\metroStyle\\img\\metro.gif", "PackagePath": "staticwebassets\\css\\metroStyle\\img\\metro.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\metroStyle\\img\\metro.png", "PackagePath": "staticwebassets\\css\\metroStyle\\img\\metro.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\metroStyle\\metroStyle.css", "PackagePath": "staticwebassets\\css\\metroStyle\\metroStyle.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\metronic.css", "PackagePath": "staticwebassets\\css\\metronic.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\css\\treetable.css", "PackagePath": "staticwebassets\\css\\treetable.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\backG.jpg", "PackagePath": "staticwebassets\\images\\hih\\backG.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hihBack4.jpg", "PackagePath": "staticwebassets\\images\\hih\\hihBack4.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hihBack5.jpg", "PackagePath": "staticwebassets\\images\\hih\\hihBack5.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hihBack6.jpg", "PackagePath": "staticwebassets\\images\\hih\\hihBack6.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hihLogo.png", "PackagePath": "staticwebassets\\images\\hih\\hihLogo.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hih_log.png", "PackagePath": "staticwebassets\\images\\hih\\hih_log.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hihback2.jpg", "PackagePath": "staticwebassets\\images\\hih\\hihback2.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\hihback3.jpg", "PackagePath": "staticwebassets\\images\\hih\\hihback3.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\jt.jpeg", "PackagePath": "staticwebassets\\images\\hih\\jt.jpeg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\透明LOGO.gif", "PackagePath": "staticwebassets\\images\\hih\\透明LOGO.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\hih\\透明hih_LoGo2.gif", "PackagePath": "staticwebassets\\images\\hih\\透明hih_LoGo2.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\backSun.jpg", "PackagePath": "staticwebassets\\images\\login\\backSun.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\buttonbg.png", "PackagePath": "staticwebassets\\images\\login\\buttonbg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\cloud.png", "PackagePath": "staticwebassets\\images\\login\\cloud.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\hand.png", "PackagePath": "staticwebassets\\images\\login\\hand.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\left_hand.png", "PackagePath": "staticwebassets\\images\\login\\left_hand.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\light.png", "PackagePath": "staticwebassets\\images\\login\\light.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\loginbg1.png", "PackagePath": "staticwebassets\\images\\login\\loginbg1.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\loginbg2.png", "PackagePath": "staticwebassets\\images\\login\\loginbg2.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\loginbg3.png", "PackagePath": "staticwebassets\\images\\login\\loginbg3.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\logininfo.png", "PackagePath": "staticwebassets\\images\\login\\logininfo.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\loginpassword.png", "PackagePath": "staticwebassets\\images\\login\\loginpassword.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\loginsj.png", "PackagePath": "staticwebassets\\images\\login\\loginsj.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\loginuser.png", "PackagePath": "staticwebassets\\images\\login\\loginuser.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\logo.png", "PackagePath": "staticwebassets\\images\\login\\logo.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\right_hand.png", "PackagePath": "staticwebassets\\images\\login\\right_hand.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\images\\login\\tou.png", "PackagePath": "staticwebassets\\images\\login\\tou.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\Ali\\aliyun-oss-sdk.min.js", "PackagePath": "staticwebassets\\js\\Ali\\aliyun-oss-sdk.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\bodyTab.js", "PackagePath": "staticwebassets\\js\\bodyTab.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\bootstrap.js", "PackagePath": "staticwebassets\\js\\bootstrap.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\cookie.js", "PackagePath": "staticwebassets\\js\\cookie.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\custom.js", "PackagePath": "staticwebassets\\js\\custom.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\droptree.js", "PackagePath": "staticwebassets\\js\\droptree.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree.js", "PackagePath": "staticwebassets\\js\\dtree.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\dtree.css", "PackagePath": "staticwebassets\\js\\dtree\\dtree.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\font\\dtreefont.css", "PackagePath": "staticwebassets\\js\\dtree\\font\\dtreefont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\font\\dtreefont.eot", "PackagePath": "staticwebassets\\js\\dtree\\font\\dtreefont.eot"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\font\\dtreefont.svg", "PackagePath": "staticwebassets\\js\\dtree\\font\\dtreefont.svg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\font\\dtreefont.ttf", "PackagePath": "staticwebassets\\js\\dtree\\font\\dtreefont.ttf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\font\\dtreefont.woff", "PackagePath": "staticwebassets\\js\\dtree\\font\\dtreefont.woff"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\dtree\\font\\icons.json", "PackagePath": "staticwebassets\\js\\dtree\\font\\icons.json"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow-ui\\img\\wallbg.png", "PackagePath": "staticwebassets\\js\\flow-ui\\img\\wallbg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\GooFlow.css", "PackagePath": "staticwebassets\\js\\flow\\GooFlow.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\GooFlow.export.js", "PackagePath": "staticwebassets\\js\\flow\\GooFlow.export.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\GooFlow.print.js", "PackagePath": "staticwebassets\\js\\flow\\GooFlow.print.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\GooFunc.js", "PackagePath": "staticwebassets\\js\\flow\\GooFunc.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\config.js", "PackagePath": "staticwebassets\\js\\flow\\config.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\demo.css", "PackagePath": "staticwebassets\\js\\flow\\fonts\\demo.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\demo_fontclass.html", "PackagePath": "staticwebassets\\js\\flow\\fonts\\demo_fontclass.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\demo_symbol.html", "PackagePath": "staticwebassets\\js\\flow\\fonts\\demo_symbol.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\demo_unicode.html", "PackagePath": "staticwebassets\\js\\flow\\fonts\\demo_unicode.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\iconflow.eot", "PackagePath": "staticwebassets\\js\\flow\\fonts\\iconflow.eot"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\iconflow.svg", "PackagePath": "staticwebassets\\js\\flow\\fonts\\iconflow.svg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\iconflow.ttf", "PackagePath": "staticwebassets\\js\\flow\\fonts\\iconflow.ttf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\iconflow.woff", "PackagePath": "staticwebassets\\js\\flow\\fonts\\iconflow.woff"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\iconfont.css", "PackagePath": "staticwebassets\\js\\flow\\fonts\\iconfont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\fonts\\iconfont.js", "PackagePath": "staticwebassets\\js\\flow\\fonts\\iconfont.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\gooflow.js", "PackagePath": "staticwebassets\\js\\flow\\gooflow.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flow\\img\\gooflow_icon.png", "PackagePath": "staticwebassets\\js\\flow\\img\\gooflow_icon.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\flowlayout.js", "PackagePath": "staticwebassets\\js\\flowlayout.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\formSelects\\formSelects.css", "PackagePath": "staticwebassets\\js\\formSelects\\formSelects.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\formSelects\\formSelects.js", "PackagePath": "staticwebassets\\js\\formSelects\\formSelects.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\iconPicker.js", "PackagePath": "staticwebassets\\js\\iconPicker.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\index.js", "PackagePath": "staticwebassets\\js\\index.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\leftNav.js", "PackagePath": "staticwebassets\\js\\leftNav.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\modernNav.js", "PackagePath": "staticwebassets\\js\\modernNav.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\openauth.js", "PackagePath": "staticwebassets\\js\\openauth.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\slimscroll.js", "PackagePath": "staticwebassets\\js\\slimscroll.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\anchor\\anchor.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\anchor\\anchor.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\attachment.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\attachment.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\attachment.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\attachment.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\attachment.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\attachment.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_chm.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_chm.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_default.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_default.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_doc.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_doc.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_exe.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_exe.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_jpg.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_jpg.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_mp3.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_mp3.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_mv.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_mv.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_pdf.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_pdf.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_ppt.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_ppt.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_psd.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_psd.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_rar.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_rar.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_txt.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_txt.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_xls.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\fileTypeImages\\icon_xls.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\alignicon.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\alignicon.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\alignicon.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\alignicon.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\file-icons.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\file-icons.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\file-icons.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\file-icons.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\icons.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\icons.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\icons.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\icons.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\image.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\image.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\progress.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\progress.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\success.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\success.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\attachment\\images\\success.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\attachment\\images\\success.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\background\\background.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\background\\background.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\background\\background.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\background\\background.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\background\\background.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\background\\background.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\background\\images\\bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\background\\images\\bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\background\\images\\success.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\background\\images\\success.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\chart.config.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\chart.config.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\charts.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\charts.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\charts.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\charts.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\charts.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\charts.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\images\\charts0.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\images\\charts0.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\images\\charts1.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\images\\charts1.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\images\\charts2.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\images\\charts2.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\images\\charts3.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\images\\charts3.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\images\\charts4.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\images\\charts4.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\charts\\images\\charts5.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\charts\\images\\charts5.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\emotion.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\emotion.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\emotion.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\emotion.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\emotion.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\emotion.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\0.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\0.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\bface.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\bface.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\cface.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\cface.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\fface.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\fface.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\jxface2.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\jxface2.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\neweditor-tab-bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\neweditor-tab-bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\tface.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\tface.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\wface.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\wface.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\emotion\\images\\yface.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\emotion\\images\\yface.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\gmap\\gmap.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\gmap\\gmap.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\help\\help.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\help\\help.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\help\\help.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\help\\help.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\help\\help.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\help\\help.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\image.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\image.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\image.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\image.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\image.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\image.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\alignicon.jpg", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\alignicon.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\icons.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\icons.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\icons.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\icons.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\image.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\image.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\progress.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\progress.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\success.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\success.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\image\\images\\success.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\image\\images\\success.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\insertframe\\insertframe.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\insertframe\\insertframe.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\internal.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\internal.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\link\\link.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\link\\link.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\map\\map.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\map\\map.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\map\\show.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\map\\show.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\music\\music.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\music\\music.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\music\\music.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\music\\music.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\music\\music.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\music\\music.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\preview\\preview.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\preview\\preview.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\addimg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\addimg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\brush.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\brush.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\delimg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\delimg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\delimgH.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\delimgH.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\empty.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\empty.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\emptyH.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\emptyH.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\eraser.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\eraser.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\redo.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\redo.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\redoH.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\redoH.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\scale.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\scale.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\scaleH.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\scaleH.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\size.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\size.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\undo.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\undo.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\images\\undoH.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\images\\undoH.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\scrawl.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\scrawl.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\scrawl.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\scrawl.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\scrawl\\scrawl.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\scrawl\\scrawl.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\searchreplace\\searchreplace.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\searchreplace\\searchreplace.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\searchreplace\\searchreplace.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\searchreplace\\searchreplace.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\snapscreen\\snapscreen.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\snapscreen\\snapscreen.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\spechars\\spechars.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\spechars\\spechars.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\spechars\\spechars.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\spechars\\spechars.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\table\\dragicon.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\table\\dragicon.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\table\\edittable.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\table\\edittable.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\table\\edittable.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\table\\edittable.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\table\\edittable.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\table\\edittable.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\table\\edittd.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\table\\edittd.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\table\\edittip.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\table\\edittip.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\config.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\config.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\images\\bg.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\images\\bg.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\images\\pre0.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\images\\pre0.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\images\\pre1.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\images\\pre1.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\images\\pre2.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\images\\pre2.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\images\\pre3.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\images\\pre3.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\images\\pre4.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\images\\pre4.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\template.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\template.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\template.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\template.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\template\\template.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\template\\template.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\center_focus.jpg", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\center_focus.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\file-icons.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\file-icons.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\file-icons.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\file-icons.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\icons.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\icons.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\icons.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\icons.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\image.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\image.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\left_focus.jpg", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\left_focus.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\none_focus.jpg", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\none_focus.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\progress.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\progress.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\right_focus.jpg", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\right_focus.jpg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\success.gif", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\success.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\images\\success.png", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\images\\success.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\video.css", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\video.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\video.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\video.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\video\\video.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\video\\video.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\webapp\\webapp.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\webapp\\webapp.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\wordimage\\fClipboard_ueditor.swf", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\wordimage\\fClipboard_ueditor.swf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\wordimage\\imageUploader.swf", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\wordimage\\imageUploader.swf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\wordimage\\tangram.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\wordimage\\tangram.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\wordimage\\wordimage.html", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\wordimage\\wordimage.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\dialogs\\wordimage\\wordimage.js", "PackagePath": "staticwebassets\\js\\ueditor\\dialogs\\wordimage\\wordimage.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\Readme.txt", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\Readme.txt"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap-ie.js", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap-ie.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-ie6.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-ie6.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-ie6.min.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-ie6.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-responsive.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-responsive.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-responsive.min.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap-responsive.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\bootstrap.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\css\\ie.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\css\\ie.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\img\\glyphicons-halflings-white.png", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\img\\glyphicons-halflings-white.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\img\\glyphicons-halflings.png", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\img\\glyphicons-halflings.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\js\\bootstrap.js", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\js\\bootstrap.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\bootstrap\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\bootstrap\\js\\bootstrap.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\checkbox.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\checkbox.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\checkboxs.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\checkboxs.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\error.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\error.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\images\\leipi_formdesign.png", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\images\\leipi_formdesign.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\images\\progressbar.gif", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\images\\progressbar.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\images\\qrcode.gif", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\images\\qrcode.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\images\\template\\bg.gif", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\images\\template\\bg.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\images\\template\\pre0.png", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\images\\template\\pre0.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\images\\template\\pre1.png", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\images\\template\\pre1.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\jquery-1.7.2.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\jquery-1.7.2.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\leipi.formdesign.v4.js", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\leipi.formdesign.v4.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\leipi.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\leipi.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\leipi.style.css", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\leipi.style.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\listctrl.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\listctrl.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\macros.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\macros.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\more.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\more.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\progressbar.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\progressbar.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\qrcode.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\qrcode.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\radio.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\radio.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\radios.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\radios.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\select.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\select.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\template.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\template.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\text.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\text.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\textarea.html", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\textarea.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\formdesign\\textfield.html.ajax.bak", "PackagePath": "staticwebassets\\js\\ueditor\\formdesign\\textfield.html.ajax.bak"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\index.html", "PackagePath": "staticwebassets\\js\\ueditor\\index.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\en.js", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\en.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\addimage.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\addimage.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\alldeletebtnhoverskin.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\alldeletebtnhoverskin.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\alldeletebtnupskin.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\alldeletebtnupskin.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\background.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\background.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\button.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\button.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\copy.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\copy.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\deletedisable.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\deletedisable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\deleteenable.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\deleteenable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\listbackground.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\listbackground.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\localimage.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\localimage.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\music.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\music.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\rotateleftdisable.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\rotateleftdisable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\rotateleftenable.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\rotateleftenable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\rotaterightdisable.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\rotaterightdisable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\rotaterightenable.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\rotaterightenable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\en\\images\\upload.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\en\\images\\upload.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\zh-cn\\images\\copy.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\zh-cn\\images\\copy.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\zh-cn\\images\\localimage.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\zh-cn\\images\\localimage.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\zh-cn\\images\\music.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\zh-cn\\images\\music.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\zh-cn\\images\\upload.png", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\zh-cn\\images\\upload.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\lang\\zh-cn\\zh-cn.js", "PackagePath": "staticwebassets\\js\\ueditor\\lang\\zh-cn\\zh-cn.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\css\\ueditor.css", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\css\\ueditor.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\css\\ueditor.min.css", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\css\\ueditor.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\dialogbase.css", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\dialogbase.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\anchor.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\anchor.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\arrow.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\arrow.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\arrow_down.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\arrow_down.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\arrow_up.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\arrow_up.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\button-bg.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\button-bg.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\cancelbutton.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\cancelbutton.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\charts.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\charts.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\cursor_h.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\cursor_h.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\cursor_h.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\cursor_h.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\cursor_v.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\cursor_v.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\cursor_v.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\cursor_v.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\dialog-title-bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\dialog-title-bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\filescan.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\filescan.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\highlighted.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\highlighted.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\icons-all.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\icons-all.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\icons.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\icons.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\icons.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\icons.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\loaderror.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\loaderror.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\loading.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\loading.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\lock.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\lock.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\neweditor-tab-bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\neweditor-tab-bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\pagebreak.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\pagebreak.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\scale.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\scale.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\sortable.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\sortable.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\spacer.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\spacer.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\sparator_v.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\sparator_v.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\table-cell-align.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\table-cell-align.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\tangram-colorpicker.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\tangram-colorpicker.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\toolbar_bg.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\toolbar_bg.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\unhighlighted.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\unhighlighted.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\upload.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\upload.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\videologo.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\videologo.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\word.gif", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\word.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\default\\images\\wordpaste.png", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\default\\images\\wordpaste.png"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\themes\\iframe.css", "PackagePath": "staticwebassets\\js\\ueditor\\themes\\iframe.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\codemirror\\codemirror.css", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\codemirror\\codemirror.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\codemirror\\codemirror.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\codemirror\\codemirror.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\Uploader.swf", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\Uploader.swf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.css", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.custom.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.custom.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.custom.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.custom.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.flashonly.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.flashonly.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.flashonly.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.flashonly.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.html5only.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.html5only.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.html5only.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.html5only.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.withoutimage.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.withoutimage.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\webuploader\\webuploader.withoutimage.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\webuploader\\webuploader.withoutimage.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\zeroclipboard\\ZeroClipboard.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\zeroclipboard\\ZeroClipboard.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\zeroclipboard\\ZeroClipboard.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\zeroclipboard\\ZeroClipboard.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\third-party\\zeroclipboard\\ZeroClipboard.swf", "PackagePath": "staticwebassets\\js\\ueditor\\third-party\\zeroclipboard\\ZeroClipboard.swf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\ueditor.all.js", "PackagePath": "staticwebassets\\js\\ueditor\\ueditor.all.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\ueditor.all.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\ueditor.all.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\ueditor.config.js", "PackagePath": "staticwebassets\\js\\ueditor\\ueditor.config.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\ueditor.parse.js", "PackagePath": "staticwebassets\\js\\ueditor\\ueditor.parse.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ueditor\\ueditor.parse.min.js", "PackagePath": "staticwebassets\\js\\ueditor\\ueditor.parse.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\utils.js", "PackagePath": "staticwebassets\\js\\utils.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\vue.js", "PackagePath": "staticwebassets\\js\\vue.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\vue2.6.10.js", "PackagePath": "staticwebassets\\js\\vue2.6.10.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\xlsx.full.min.js", "PackagePath": "staticwebassets\\js\\xlsx.full.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\js\\ztree.js", "PackagePath": "staticwebassets\\js\\ztree.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\css\\layui.css", "PackagePath": "staticwebassets\\layui\\css\\layui.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\font\\iconfont.eot", "PackagePath": "staticwebassets\\layui\\font\\iconfont.eot"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\font\\iconfont.svg", "PackagePath": "staticwebassets\\layui\\font\\iconfont.svg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\font\\iconfont.ttf", "PackagePath": "staticwebassets\\layui\\font\\iconfont.ttf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\font\\iconfont.woff", "PackagePath": "staticwebassets\\layui\\font\\iconfont.woff"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\font\\iconfont.woff2", "PackagePath": "staticwebassets\\layui\\font\\iconfont.woff2"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\0.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\0.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\1.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\1.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\10.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\10.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\11.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\11.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\12.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\12.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\13.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\13.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\14.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\14.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\15.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\15.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\16.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\16.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\17.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\17.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\18.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\18.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\19.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\19.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\2.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\2.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\20.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\20.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\21.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\21.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\22.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\22.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\23.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\23.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\24.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\24.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\25.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\25.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\26.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\26.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\27.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\27.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\28.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\28.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\29.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\29.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\3.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\3.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\30.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\30.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\31.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\31.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\32.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\32.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\33.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\33.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\34.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\34.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\35.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\35.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\36.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\36.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\37.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\37.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\38.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\38.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\39.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\39.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\4.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\4.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\40.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\40.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\41.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\41.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\42.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\42.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\43.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\43.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\44.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\44.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\45.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\45.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\46.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\46.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\47.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\47.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\48.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\48.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\49.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\49.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\5.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\5.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\50.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\50.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\51.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\51.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\52.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\52.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\53.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\53.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\54.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\54.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\55.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\55.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\56.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\56.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\57.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\57.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\58.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\58.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\59.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\59.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\6.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\6.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\60.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\60.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\61.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\61.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\62.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\62.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\63.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\63.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\64.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\64.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\65.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\65.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\66.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\66.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\67.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\67.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\68.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\68.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\69.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\69.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\7.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\7.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\70.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\70.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\71.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\71.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\8.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\8.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\images\\face\\9.gif", "PackagePath": "staticwebassets\\layui\\images\\face\\9.gif"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\layui\\layui.js", "PackagePath": "staticwebassets\\layui\\layui.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ExcelJs\\exceljs.min.js", "PackagePath": "staticwebassets\\lib\\ExcelJs\\exceljs.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\dist\\ag-grid-community.js", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\dist\\ag-grid-community.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\dist\\ag-grid-community.min.js", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\dist\\ag-grid-community.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\dist\\ag-grid-community.min.noStyle.js", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\dist\\ag-grid-community.min.noStyle.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\dist\\ag-grid-community.noStyle.js", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\dist\\ag-grid-community.noStyle.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\_css-content.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\_css-content.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\_icon-font-codes.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\_icon-font-codes.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\_index.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\_index.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\_shared.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\_shared.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-grid-no-native-widgets.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-grid-no-native-widgets.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-grid-no-native-widgets.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-grid-no-native-widgets.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-grid.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-grid.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-grid.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-grid.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-alpine-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-alpine-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-alpine-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-alpine-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-alpine.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-alpine.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-alpine.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-alpine.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-balham-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-balham-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-balham-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-balham-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-balham.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-balham.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-balham.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-balham.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-material-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-material-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-material-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-material-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-material.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-material.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-material.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-material.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-quartz-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-quartz-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-quartz-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-quartz-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-quartz.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-quartz.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\ag-theme-quartz.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\ag-theme-quartz.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridAlpineFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridAlpineFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridAlpineFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridAlpineFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridBalhamFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridBalhamFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridBalhamFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridBalhamFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridClassicFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridClassicFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridClassicFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridClassicFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridMaterialFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridMaterialFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridMaterialFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridMaterialFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridQuartzFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridQuartzFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-community\\styles\\agGridQuartzFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-community\\styles\\agGridQuartzFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.js", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.min.js", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.min.noStyle.js", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.min.noStyle.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.noStyle.js", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\dist\\ag-grid-enterprise.noStyle.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\_css-content.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\_css-content.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\_icon-font-codes.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\_icon-font-codes.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\_index.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\_index.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\_shared.scss", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\_shared.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-grid-no-native-widgets.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-grid-no-native-widgets.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-grid-no-native-widgets.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-grid-no-native-widgets.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-grid.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-grid.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-grid.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-grid.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-alpine.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-balham.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-material-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-material-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-material-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-material-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-material.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-material.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-material.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-material.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz-no-font.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz-no-font.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz-no-font.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz-no-font.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\ag-theme-quartz.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridAlpineFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridAlpineFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridAlpineFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridAlpineFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridBalhamFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridBalhamFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridBalhamFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridBalhamFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridClassicFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridClassicFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridClassicFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridClassicFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridMaterialFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridMaterialFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridMaterialFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridMaterialFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridQuartzFont.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridQuartzFont.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ag-grid-enterprise\\styles\\agGridQuartzFont.min.css", "PackagePath": "staticwebassets\\lib\\ag-grid-enterprise\\styles\\agGridQuartzFont.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\angular\\angular.js", "PackagePath": "staticwebassets\\lib\\angular\\angular.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\angular\\angular.min.js", "PackagePath": "staticwebassets\\lib\\angular\\angular.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\anjular.js", "PackagePath": "staticwebassets\\lib\\anjular.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\anjular.min.js", "PackagePath": "staticwebassets\\lib\\anjular.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootStrap-select\\bootstrap-select.css", "PackagePath": "staticwebassets\\lib\\bootStrap-select\\bootstrap-select.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootStrap-select\\bootstrap-select.js", "PackagePath": "staticwebassets\\lib\\bootStrap-select\\bootstrap-select.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootStrap-select\\bootstrap-select.min.js", "PackagePath": "staticwebassets\\lib\\bootStrap-select\\bootstrap-select.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\css\\bootstrap-ie78.css.css", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\css\\bootstrap-ie78.css.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\css\\bootstrap.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\css\\bootstrap.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.css", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.json", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.json"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.scss", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\bootstrap-icons.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\fonts\\bootstrap-icons.woff", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\fonts\\bootstrap-icons.woff"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\fonts\\bootstrap-icons.woff2", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\fonts\\bootstrap-icons.woff2"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.eot", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.eot"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.svg", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.svg"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.ttf", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.ttf"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.woff", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.woff"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.woff2", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\fonts\\glyphicons-halflings-regular.woff2"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\js\\bootstrap-treeview.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\js\\bootstrap-treeview.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\js\\bootstrap.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\js\\bootstrap.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-3.4.1-dist\\js\\vaildation.js", "PackagePath": "staticwebassets\\lib\\bootstrap-3.4.1-dist\\js\\vaildation.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-datetimepicker\\bootstrap-datetimepicker.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap-datetimepicker\\bootstrap-datetimepicker.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\bootstrap-datetimepicker\\bootstrap-datetimepicker.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap-datetimepicker\\bootstrap-datetimepicker.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\custome\\myAlert.js", "PackagePath": "staticwebassets\\lib\\custome\\myAlert.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\dateRangepicker\\css\\daterangepicker.css", "PackagePath": "staticwebassets\\lib\\dateRangepicker\\css\\daterangepicker.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\dateRangepicker\\css\\daterangepicker.min.css.map", "PackagePath": "staticwebassets\\lib\\dateRangepicker\\css\\daterangepicker.min.css.map"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\dateRangepicker\\js\\daterangepicker.js", "PackagePath": "staticwebassets\\lib\\dateRangepicker\\js\\daterangepicker.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\dateRangepicker\\js\\moment-timezone-with-data.min.js", "PackagePath": "staticwebassets\\lib\\dateRangepicker\\js\\moment-timezone-with-data.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\dateRangepicker\\js\\moment.js", "PackagePath": "staticwebassets\\lib\\dateRangepicker\\js\\moment.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker.css.map", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker.css.map"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker.min.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker.standalone.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker.standalone.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker.standalone.css.map", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker.standalone.css.map"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker.standalone.min.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker.standalone.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker3.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker3.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker3.css.map", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker3.css.map"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker3.min.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker3.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker3.standalone.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker3.standalone.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker3.standalone.css.map", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker3.standalone.css.map"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\css\\bootstrap-datepicker3.standalone.min.css", "PackagePath": "staticwebassets\\lib\\datepicker\\css\\bootstrap-datepicker3.standalone.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\js\\bootstrap-datepicker.js", "PackagePath": "staticwebassets\\lib\\datepicker\\js\\bootstrap-datepicker.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\js\\bootstrap-datepicker.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\js\\bootstrap-datepicker.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker-en-CA.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker-en-CA.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ar-DZ.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ar-DZ.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ar-tn.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ar-tn.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ar.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ar.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.az.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.az.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.bg.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.bg.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.bm.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.bm.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.bn.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.bn.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.br.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.br.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.bs.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.bs.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ca.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ca.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.cs.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.cs.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.cy.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.cy.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.da.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.da.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.de.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.de.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.el.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.el.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-AU.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-AU.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-CA.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-CA.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-GB.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-GB.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-IE.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-IE.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-NZ.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-NZ.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-US.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-US.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.en-ZA.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.en-ZA.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.eo.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.eo.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.es.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.es.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.et.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.et.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.eu.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.eu.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.fa.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.fa.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.fi.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.fi.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.fo.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.fo.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.fr-CH.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.fr-CH.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.fr.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.fr.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.gl.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.gl.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.he.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.he.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.hi.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.hi.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.hr.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.hr.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.hu.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.hu.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.hy.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.hy.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.id.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.id.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.is.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.is.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.it-CH.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.it-CH.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.it.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.it.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ja.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ja.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ka.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ka.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.kh.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.kh.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.kk.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.kk.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.km.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.km.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ko.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ko.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.kr.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.kr.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.lt.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.lt.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.lv.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.lv.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.me.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.me.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.mk.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.mk.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.mn.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.mn.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.mr.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.mr.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ms.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ms.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.nl-BE.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.nl-BE.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.nl.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.nl.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.no.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.no.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.oc.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.oc.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.pl.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.pl.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.pt-BR.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.pt-BR.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.pt.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.pt.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ro.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ro.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.rs-latin.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.rs-latin.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.rs.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.rs.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ru.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ru.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.si.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.si.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sk.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sk.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sl.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sl.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sq.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sq.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sr-latin.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sr-latin.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sr.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sr.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sv.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sv.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.sw.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.sw.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.ta.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.ta.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.tg.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.tg.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.th.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.th.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.tk.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.tk.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.tr.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.tr.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.uk.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.uk.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.uz-cyrl.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.uz-cyrl.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.uz-latn.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.uz-latn.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.vi.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.vi.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.zh-CN.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.zh-CN.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\datepicker\\locales\\bootstrap-datepicker.zh-TW.min.js", "PackagePath": "staticwebassets\\lib\\datepicker\\locales\\bootstrap-datepicker.zh-TW.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\fontawesome\\font-awesome.css", "PackagePath": "staticwebassets\\lib\\fontawesome\\font-awesome.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\fontawesome\\font-awesome.min.css", "PackagePath": "staticwebassets\\lib\\fontawesome\\font-awesome.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\fonts\\fontawesome-webfont.woff2", "PackagePath": "staticwebassets\\lib\\fonts\\fontawesome-webfont.woff2"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\jquery\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\jquery.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\--sweet-alert.min.js", "PackagePath": "staticwebassets\\lib\\sweetalert\\--sweet-alert.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\ie9.css", "PackagePath": "staticwebassets\\lib\\sweetalert\\ie9.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\minimizer.bat", "PackagePath": "staticwebassets\\lib\\sweetalert\\minimizer.bat"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.all.js", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.all.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.all.min.js", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.all.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.css", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.html", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.html"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.js", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.min.css", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.min.css"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.min.js", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\sweetalert\\sweet-alert.scss", "PackagePath": "staticwebassets\\lib\\sweetalert\\sweet-alert.scss"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ui-bootstrap-tpls-2.5.0.js", "PackagePath": "staticwebassets\\lib\\ui-bootstrap-tpls-2.5.0.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\lib\\ui-bootstrap-tpls-2.5.0.min.js", "PackagePath": "staticwebassets\\lib\\ui-bootstrap-tpls-2.5.0.min.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\userJs\\errorcauses.js", "PackagePath": "staticwebassets\\userJs\\errorcauses.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\userJs\\login.js", "PackagePath": "staticwebassets\\userJs\\login.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\userJs\\nodeInfo.js", "PackagePath": "staticwebassets\\userJs\\nodeInfo.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\userJs\\openjobs.js", "PackagePath": "staticwebassets\\userJs\\openjobs.js"}, {"Id": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\wwwroot\\userJs\\preview.js", "PackagePath": "staticwebassets\\userJs\\preview.js"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.OpenAuth.Customer.Mvc.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.OpenAuth.Customer.Mvc.props", "PackagePath": "build\\OpenAuth.Customer.Mvc.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.OpenAuth.Customer.Mvc.props", "PackagePath": "buildMultiTargeting\\OpenAuth.Customer.Mvc.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.OpenAuth.Customer.Mvc.props", "PackagePath": "buildTransitive\\OpenAuth.Customer.Mvc.props"}], "ElementsToRemove": []}