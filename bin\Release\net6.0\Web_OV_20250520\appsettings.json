{
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DataProtection": "temp-keys/",
  "ConnectionStrings": {
    //"USWMSDBContext": "Data Source=.;Initial Catalog=HYCSSD;User=sa;Password=******",
    //"OpenAuthDBContext": "Data Source=.;Initial Catalog=OpenAuthDB;User=sa;Password=******",
    //"CRMDBContext": "Data Source=.;Initial Catalog=CRM;User=sa;Password=******"

    "OpenAuthDBContext": "Data Source=rm-bp1h7krm5v4f3zh4tmo.sqlserver.rds.aliyuncs.com,3433;Initial Catalog=hih_framework;Persist Security Info=True;User ID=jshih;Password=*********",
    "USWMSDBContext": "Data Source=rm-bp1h7krm5v4f3zh4tmo.sqlserver.rds.aliyuncs.com,3433;Initial Catalog=uswms;Persist Security Info=True;User ID=jshih;Password=*********",
    "CRMDBContext": "Data Source=rm-bp1h7krm5v4f3zh4tmo.sqlserver.rds.aliyuncs.com,3433;Initial Catalog=CRM;Persist Security Info=True;User ID=jshih;Password=*********"


    //"jsDB": "Data Source=rm-bp1h7krm5v4f3zh4t.sqlserver.rds.aliyuncs.com,3433;Initial Catalog=hih_framework;Persist Security Info=True;User ID=jshih;Password=*********"


  },
  "AppSetting": {
    "IdentityServerUrl": "", //IdentityServer服务器地址。如果为空，则不启用OAuth认证
    "Version": "v1.0.8", //如果为demo，则可以防止post提交
    "DbTypes": {
      "OpenAuthDBContext": "SqlServer",
      "USWMSDBContext": "SqlServer",
      "CRMDBContext": "SqlServer"

      //数据库类型：SqlServer、MySql、Oracle、PostgreSQL
    },
    //"RedisConf": "redistest.cq-p.com.cn:8001,password=share_redis@123",  //redis配置
    "HttpHost": "http://*:1802" //启动绑定地址及端口
  },
  "EPPlus": {
    "ExcelPackage": {
      "LicenseContext": "NonCommercial",
      "Compatibility": {
        "IsWorksheets1Based": "true"
      }
    }
  }
}