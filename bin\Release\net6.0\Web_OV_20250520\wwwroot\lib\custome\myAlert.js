﻿var myAlert = myAlert || {};
(function ($) {
    if (!Swal || !$) {
        console.warn('SweetAlert2 or jQuery is not loaded.');
        return;
    }

    /* */
    myAlert.libs = myAlert.libs || {};
    myAlert.libs.sweetAlert = {
        config: {
            'default': {
                allowEnterKey: true,
                  position: 'center',
                showConfirmButton: true,
                allowOutsideClick: false  // 点击外部区域关闭弹窗
            },
            info: {
                icon: 'info',
                showConfirmButton: true,
                allowOutsideClick: false
            },
            success: {
                icon: 'success',
                showConfirmButton: true,
                allowOutsideClick: false
            },
            warn: {
                icon: 'warning',
                showConfirmButton: true,
                allowOutsideClick: false
            },
            error: {
                icon: 'error',
                showConfirmButton: true,
                allowOutsideClick: false
            },
            confirm: {
                icon: 'question',
                /*title: 'Are you sure?',*/
                showCancelButton: true,
                cancelButtonText: 'Cancel',
                confirmButtonColor: "#DD6B55",
                confirmButtonText: 'Yes'
            }
        }
    };

    /* 信息*/
    var showMessage = function (type, message, title) {
        if (!title) {
            title = message;
            message = undefined;
        }

        var opts = $.extend(
            {},
            myAlert.libs.sweetAlert.config.default,
            myAlert.libs.sweetAlert.config[type]
        );

        if (type === 'info' || type === 'success') {
            opts = $.extend(opts, { timer: 5000 });
        }

        opts = $.extend(opts, {
            title: title,
            text: message,
            html: message ? isHtml(message) : undefined
        });

        return $.Deferred(function ($dfd) {
            Swal.fire(opts).then(function (result) {
                $dfd.resolve(result);
            });
        });
    };

    var isHtml = function (input) {
        return /<[a-z]+\d?(\s+[\w-]+=("[^"]*"|'[^']*'))*\s*\/?>|&#?\w+;/i.test(input);
    };

    myAlert.message = {
        info: function (message, title) {
            return showMessage('info', message, title);
        },
        success: function (message, title) {
            return showMessage('success', message, title);
        },
        warn: function (message, title) {
            return showMessage('warn', message, title);
        },
        error: function (message, title) {
            return showMessage('error', message, title);
        },
        confirm: function (message, titleOrCallback, callback) {
            var userOpts = {
                /*text: message,*/
                title: message
            };

            if ($.isFunction(titleOrCallback)) {
                callback = titleOrCallback;
            } else if (titleOrCallback) {
                userOpts.title = titleOrCallback;
            }

            var opts = $.extend(
                {},
                myAlert.libs.sweetAlert.config.default,
                myAlert.libs.sweetAlert.config.confirm,
                userOpts
            );

            return $.Deferred(function ($dfd) {
                Swal.fire(opts).then(function (result) {
                    setTimeout(function () { callback && callback(result.isConfirmed); }, 300);
                    $dfd.resolve();
                });
            });
        }
    };

})(jQuery);