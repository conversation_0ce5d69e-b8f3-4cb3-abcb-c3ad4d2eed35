﻿/* CSS Document */
*{font-size:9pt;border:0;margin:0;padding:0;}
body{font-family:'΢���ź�'; margin:0 auto;min-width:980px;}
ul{display:block;margin:0;padding:0;list-style:none;}
li{display:block;margin:0;padding:0;list-style: none;}
imgs{border:0;}
dl,dt,dd,span{margin:0;padding:0;display:block;}
a,a:focus{text-decoration:none;color:#000;outline:none;blr:expression(this.onFocus=this.blur());}
a:hover{color:#00a4ac;text-decoration:none;}
table{border-collapse:collapse;border-spacing: 0;}
cite{font-style:normal;}
h2{font-weight:normal;}

/*cloud*/
#mainBody {width:100%;height:100%;position:absolute;z-index:-1;}
.cloud {position:absolute;top:0px;left:0px;width:100%;height:100%;background:url(/images/login/cloud.png) no-repeat;z-index:1;opacity:0.5;}
#cloud2 {z-index:2;}


/*login*/
.logintop{height:47px; position:absolute; top:0; background:url(/images/login/loginbg1.png) repeat-x;z-index:100; width:100%;}
.logintop span{color:#fff; line-height:47px; background:url(/images/login/loginsj.png) no-repeat 21px 18px; text-indent:44px; color:#afc5d2; float:left;}
.logintop ul{float:right; padding-right:30px;}
.logintop ul li{float:left; margin-left:20px; line-height:47px;}
.logintop ul li a{color:#afc5d2;}
.logintop ul li a:hover{color:#fff;}
.loginbody{background:url(/images/login/loginbg3.png) no-repeat left left; width:100%; height:585px; overflow:hidden; position:absolute; top:47px;}
.systemlogo{background:url(/images/login/logo.png) no-repeat center;width:100%; height:70px; margin-top:40px;}
.loginbox{width:692px; height:336px; background:url(/images/login/logininfo.png) no-repeat; margin-top:30px;}
.loginbox ul{margin-top:88px; margin-left:285px;}
.loginbox ul li{margin-bottom:25px;}
.loginbox ul li label{color:#687f92; padding-left:25px;}
.loginbox ul li label a{color:#687f92;}
.loginbox ul li label a:hover{color:#3d96c9;}
.loginbox ul li label input{margin-right:5px;}
.loginuser{width:299px; height:48px; background:url(/images/login/loginuser.png) no-repeat; border:none; line-height:48px; padding-left:44px; font-size:14px; font-weight:bold; color:#90a2bc;}
.loginpwd{width:299px; height:48px; background:url(/images/login/loginpassword.png) no-repeat; border:none;line-height:48px; padding-left:44px; font-size:14px; color:#90a2bc;}
.loginbtn{width:111px;height:35px; background:url(/images/login/buttonbg.png) repeat-x; font-size:14px; font-weight:bold; color:#fff;cursor:pointer;line-height:35px;}
.loginbm{height:50px; line-height:50px; text-align:center; background:url(/images/login/loginbg2.png) repeat-x;position:fixed;_position:absolute;*position:absolute;bottom:0;width:100%; color:#0b3a58;}
.loginbm a{font-weight:bold;color:#0b3a58;}
.loginbm a:hover{color:#fff;}

.main {background-color:#1c77ac; background-image:url(/images/login/light.png); background-repeat:no-repeat; background-position:center top; overflow:hidden;}
.logintop span{
    font-family: "Helvetica Neue","Hiragino Sans GB","Microsoft YaHei","\9ED1\4F53",Arial,sans-serif;color:#DDD;font-size: 12px;
}
.ipt:focus{
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6)
}
.tou{
    background: url("/images/login/tou.png") no-repeat;
    width: 97px;
    height: 92px;
    position: absolute;
    top: -87px;
    left: 140px;
}
.left_hand{
    background: url("/images/login/left_hand.png") no-repeat;
    width: 32px;
    height: 37px;
    position: absolute;
    top: -38px;
    left: 150px;
}
.right_hand{
    background: url("/images/login/right_hand.png") no-repeat;
    width: 32px;
    height: 37px;
    position: absolute;
    top: -38px;
    right: -64px;
}
.initial_left_hand{
    background: url("/images/login/hand.png") no-repeat;
    width: 30px;
    height: 20px;
    position: absolute;
    top: -12px;
    left: 100px;
}
.initial_right_hand{
    background: url("/images/login/hand.png") no-repeat;
    width: 30px;
    height: 20px;
    position: absolute;
    top: -12px;
    right: -112px;
}