<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpenAuth.Repository</name>
    </assembly>
    <members>
        <member name="M:OpenAuth.Repository.BaseRepository`2.Find(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据过滤条件，获取记录
            </summary>
            <param name="exp">The exp.</param>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.FirstOrDefault(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查找单个，且不被上下文所跟踪
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.Find(System.Int32,System.Int32,System.String,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            得到分页记录
            </summary>
            <param name="pageindex">The pageindex.</param>
            <param name="pagesize">The pagesize.</param>
            <param name="orderby">排序，格式如："Id"/"Id descending"</param>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.Count(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据过滤条件获取记录数
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.BatchAdd(`0[])">
            <summary>
            批量添加
            </summary>
            <param name="entities">The entities.</param>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.Update(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,`0}})">
            <summary>
            实现按需要只更新部分更新
            <para>如：Update(u =>u.Id==1,u =>new User{Name="ok"});</para>
            </summary>
            <param name="where">The where.</param>
            <param name="entity">The entity.</param>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.FromSql(System.String,System.Object[])">
            <summary>
            使用SQL脚本查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.Query(System.String,System.Object[])">
            <summary>
            使用SQL脚本查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.ExecuteSqlRawAsync(System.String)">
            <summary>
            异步执行sql
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.UpdateAsync(`0)">
            <summary>
            异步更新
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.DeleteAsync(`0)">
            <summary>
            异步删除
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.SaveAsync">
            <summary>
            异步保存
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            根据过滤条件获取记录数
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.BaseRepository`2.FirstOrDefaultAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            查找单个，且不被上下文所跟踪
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Core.BaseEntity.KeyIsNull">
            <summary>
            判断主键是否为空，常用做判定操作是【添加】还是【编辑】
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Core.BaseEntity.GenerateDefaultKeyVal">
            <summary>
            创建默认的主键值
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Core.IntAutoGenEntity">
            <summary>
            数据库Id为numberic且为数据库自动生成的数据实体使用该基类，用法同Entity
            <para>该场景通常为SqlServer的自动增长类型和Oracle自带的Sequence</para>
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Core.LongEntity">
            <summary>
            数据库Id为numberic类型的数据实体使用该基类，用法同Entity
            数据库Id字段为numberic(16,0)或以上长度的整型，采用雪花算法生成Id。
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Core.LongEntity.GenerateDefaultKeyVal">
            <summary>
            采用雪花算法计算Id
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Core.StringEntity">
            <summary>
            主键为字符串的实体基类，为系统默认的实体类型
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Core.StringEntity.KeyIsNull">
            <summary>
            判断主键是否为空，常用做判定操作是【添加】还是【编辑】
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Core.StringEntity.GenerateDefaultKeyVal">
            <summary>
            创建默认的主键值
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Core.TreeEntity">
            <summary>
            树状结构实体
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Core.TreeEntity.CascadeId">
            <summary>
            节点语义ID
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Core.TreeEntity.Name">
            <summary>
            功能模块名称
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Core.TreeEntity.ParentId">
            <summary>
            父节点流水号
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Core.TreeEntity.ParentName">
            <summary>
            父节点名称
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.CusUserBasic">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.DemoRMB">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.DemoRMB.Id">
            <summary>
            用户登录帐号
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.DemoRMB.Currency">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.DemoRMB.ChinaName">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.DemoRMB2">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.DemoRMB2.Id">
            <summary>
            用户登录帐号
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.DemoRMB2.Currency">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.DemoRMB2.ChinaName">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.UserBasic">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.UserBasic.user_name">
            <summary>
            用户登录帐号
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.UserBasic.real_name">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.UserBasic.password">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.UserBasic.department_id">
            <summary>
            性别
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Domain.UserBasic.GenerateDefaultKeyVal">
            <summary>
            创建默认的主键值
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.ViewUserRole">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Domain.ViewUserRole.GenerateDefaultKeyVal">
            <summary>
            创建默认的主键值
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.SysOrg">
            <summary>
            组织表
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.HotKey">
            <summary>
            热键
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.IsLeaf">
            <summary>
            是否叶子节点
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.IsAutoExpand">
            <summary>
            是否自动展开
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.IconName">
            <summary>
            节点图标文件名称
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.Status">
            <summary>
            当前状态
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.BizCode">
            <summary>
            业务对照码
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.CustomCode">
            <summary>
            自定义扩展码
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.CreateId">
            <summary>
            创建人ID
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.SortNo">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.TypeName">
            <summary>
            分类名称
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.TypeId">
            <summary>
            分类ID
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.SysOrg.ChairmanId">
            <summary>
            负责人ID
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.Invbound">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.InvboundNotice">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.Inventory">
            <summary>
            库存表
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.Inventory.WarehouseAgent">
            <summary>
            仓库代理
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.Order">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.Order.Id">
            <summary>
            用户登录帐号
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.Order.RecipientCompany">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.Domain.Order.RecipientPostalCode">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.OrderChange">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.OrderCustom">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.Outbound">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.OutboundNotice">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.QueryColumns">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.SearchTable">
            <summary>
            用户基本信息表
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Domain.TSWFlashReport">
            <summary>
            TSW_FlashReport_Detail
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.FirstOrDefault(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            返回一个单独的实体，如果记录多于1个则取第一个
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.Any(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            判断指定条件的记录是否存在
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.Update(`0)">
            <summary>
            更新一个实体的所有属性
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.Update(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,`0}})">
            <summary>
            实现按需要只更新部分更新
            <para>如：Update(u =>u.Id==1,u =>new User{Name="ok"});</para>
            </summary>
            <param name="where">更新条件</param>
            <param name="entity">更新后的实体</param>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.Delete(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}})">
            <summary>
            批量删除
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.FromSql(System.String,System.Object[])">
            <summary>
            使用SQL脚本查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IRepository`2.Query(System.String,System.Object[])">
            <summary>
            使用SQL脚本查询
            </summary>
            <returns></returns>
        </member>
        <member name="T:OpenAuth.Repository.Interface.IUnitWork`1">
            <summary>
            工作单元接口
            使用详见：http://doc.openauth.net.cn/core/unitwork.html
            <para> 适合在一下情况使用:</para>
            <para>1 在同一事务中进行多表操作</para>
            <para>2 需要多表联合查询</para>
            <para>因为架构采用的是EF访问数据库，暂时可以不用考虑采用传统Unit Work的注册机制</para>
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.ExecuteWithTransaction(System.Action)">
            <summary>
            EF默认情况下，每调用一次SaveChanges()都会执行一个单独的事务
            本接口实现在一个事务中可以多次执行SaveChanges()方法
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.ExecuteWithTransactionAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            ExecuteWithTransaction方法的异步方式
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.GetDbContext">
            <summary>
            返回DbContext,用于多线程等极端情况
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.FirstOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            返回一个单独的实体，如果记录多于1个则取第一个
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.Any``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            判断指定条件的记录是否存在
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.Add``1(``0)">
            <summary>
            新增对象，如果Id为空，则会自动创建默认Id
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.BatchAdd``1(``0[])">
            <summary>
            批量新增对象，如果对象Id为空，则会自动创建默认Id
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.Update``1(``0)">
            <summary>
            更新一个实体的所有属性
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.Update``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            实现按需要只更新部分更新
            <para>如：Update&lt;User&gt;(u =>u.Id==1,u =>new User{Name="ok"})</para>
            <para>该方法内部自动调用了SaveChanges()，需要ExecuteWithTransaction配合才能实现事务控制</para>
            </summary>
            <param name="where">更新条件</param>
            <param name="entity">更新后的实体</param>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.Delete``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            批量删除
            <para>该方法内部自动调用了SaveChanges()，需要ExecuteWithTransaction配合才能实现事务控制</para>
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.ExecuteSql(System.String)">
            <summary>
            该方法不支持EF自带的事务,需要ExecuteWithTransaction配合才能实现事务控制,详见：http://doc.openauth.net.cn/core/unitwork.html
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.FromSql``1(System.String,System.Object[])">
            <summary>
            使用SQL脚本查询
            </summary>
            <typeparam name="T"> T为数据库实体</typeparam>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.Query``1(System.String,System.Object[])">
            <summary>
            使用SQL脚本查询
            </summary>
            <typeparam name="T"> T为非数据库实体，需要在DbContext中增加对应的DbQuery</typeparam>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.ExecProcedure``1(System.String,System.Data.Common.DbParameter[])">
            <summary>
            执行存储过程
            </summary>
            <param name="procName">存储过程名称</param>
            <param name="sqlParams">存储过程参数</param>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.QueryFromSql``1(System.String)">
            <summary>
            执行SQL脚本，返回List
            </summary>
            <typeparam name="T"></typeparam>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.Interface.IUnitWork`1.QueryFromSql2``1(System.String)">
            <summary>
            执行SQL脚本，返回DataTable
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.QueryObj.QueryStringObj">
            <summary>
            针对只返回字符串类型的数值。查询SQL必需使用Value作为返回字段
            因为string没有构造函数，不能作为DbSet/DbQuery泛型的参数
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.QueryObj.SysTableColumn">
            <summary>
            系统表列信息
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.ColumnName">
            <summary>
            列名
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.Comment">
            <summary>
            列注释
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.ColumnType">
            <summary>
            类型，已转为.net类型
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.MaxLength">
            <summary>
            最大长度
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.IsNull">
            <summary>
            是否可空
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.IsDisplay">
            <summary>
            是否显示
            </summary>
        </member>
        <member name="P:OpenAuth.Repository.QueryObj.SysTableColumn.IsKey">
            <summary>
            是否主键
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.SqlHelper.ExecuteSQLByWork(Microsoft.Data.SqlClient.SqlConnection,System.String,Microsoft.Data.SqlClient.SqlTransaction,Microsoft.Data.SqlClient.SqlParameter[])">
            <summary>
            支持事务
            </summary>
            <param name="sql"></param>
            <param name="conStr"></param>
            <param name="transaction"></param>
            <param name="sqlParms"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="T:OpenAuth.Repository.SqlSugarRepository`1">
            <summary>
            SqlSugar仓储
            <para>具体用法参考：https://www.donet5.com/Home/Doc?typeId=1228</para>
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Test.TestBase">
            <summary>
            Repository测试基类
            测试用于测试DbContext、UnitWork、Repository，如果需要测试业务逻辑，请使用OpenAuth.App里面的单元测试
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.Test.TestBase.GetService">
            <summary>
            测试框架默认只注入了缓存Cache，配置Option；
            如果在测试的过程中需要模拟登录用户，cookie等信息，需要重写该方法，可以参考TestFlow的写法
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Test.TestTransaction">
            <summary>
            测试事务
            </summary>
        </member>
        <member name="T:OpenAuth.Repository.Test.TestUnitWork">
            <summary>
            测试UnitWork
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.ExecuteWithTransaction(System.Action)">
            <summary>
            EF默认情况下，每调用一次SaveChanges()都会执行一个单独的事务
            本接口实现在一个事务中可以多次执行SaveChanges()方法
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.ExecuteWithTransactionAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            ExecuteWithTransaction方法的异步方式
            EF默认情况下，每调用一次SaveChanges()都会执行一个单独的事务
            本接口实现在一个事务中可以多次执行SaveChanges()方法
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.GetDbContext">
            <summary>
            返回DbContext,用于多线程等极端情况
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.Find``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            根据过滤条件，获取记录
            </summary>
            <param name="exp">The exp.</param>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.FirstOrDefault``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            查找单个
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.Find``1(System.Int32,System.Int32,System.String,System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            得到分页记录
            </summary>
            <param name="pageindex">The pageindex.</param>
            <param name="pagesize">The pagesize.</param>
            <param name="orderby">排序，格式如："Id"/"Id descending"</param>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.Count``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            根据过滤条件获取记录数
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.Add``1(``0)">
            <summary>
            新增对象，如果Id为空，则会自动创建默认Id
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.BatchAdd``1(``0[])">
            <summary>
            批量新增对象，如果对象Id为空，则会自动创建默认Id
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.Update``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,``0}})">
            <summary>
            实现按需要只更新部分更新
            <para>如：Update&lt;User&gt;(u =>u.Id==1,u =>new User{Name="ok"})</para>
            <para>该方法内部自动调用了SaveChanges()，需要ExecuteWithTransaction配合才能实现事务控制</para>
            </summary>
            <param name="where">更新条件</param>
            <param name="entity">更新后的实体</param>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.Delete``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            批量删除
            <para>该方法内部自动调用了SaveChanges()，需要ExecuteWithTransaction配合才能实现事务控制</para>
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.ExecProcedure``1(System.String,System.Data.Common.DbParameter[])">
            <summary>
            执行存储过程
            </summary>
            <param name="procName">存储过程名称</param>
            <param name="sqlParams">存储过程参数</param>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.QueryFromSql``1(System.String)">
            <summary>
            查询数据库
            </summary>
            <param name="sql">数据库查询语句</param>
            <returns>数据List</returns>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.ExecuteSqlRawAsync(System.String)">
            <summary>
            异步执行sql
            </summary>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.SaveAsync">
            <summary>
            异步保存
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.CountAsync``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            根据过滤条件获取记录数
            </summary>
        </member>
        <member name="M:OpenAuth.Repository.UnitWork`1.FirstOrDefaultAsync``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            查找单个，且不被上下文所跟踪
            </summary>
        </member>
    </members>
</doc>
