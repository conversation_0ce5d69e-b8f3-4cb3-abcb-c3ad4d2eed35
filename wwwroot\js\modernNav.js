/*
 * Modern Bootstrap Navigation
 * 现代化Bootstrap导航菜单
 */

// 现代化导航菜单生成函数
function modernNavBar(strData) {
    var data;
    if (typeof (strData) == "string") {
        data = JSON.parse(strData);
    } else {
        data = strData;
    }

    var navHtml = '<ul class="modern-nav">';
    
    for (var i = 0; i < data.length; i++) {
        var item = data[i];
        var hasChildren = item.Children && item.Children.length > 0;
        
        navHtml += '<li class="modern-nav-item">';
        
        if (hasChildren) {
            // 有子菜单的项目
            navHtml += '<a href="javascript:;" class="modern-nav-link" data-toggle="submenu">';
            
            // 图标
            if (item.Item.IconName) {
                navHtml += '<i class="modern-nav-icon fa ' + getBootstrapIcon(item.Item.IconName) + '"></i>';
            } else {
                navHtml += '<i class="modern-nav-icon fa fa-folder"></i>';
            }
            
            // 文本
            navHtml += '<span class="modern-nav-text">' + item.Item.Name + '</span>';
            
            // 展开箭头
            navHtml += '<i class="modern-nav-toggle fa fa-chevron-down"></i>';
            navHtml += '</a>';
            
            // 子菜单
            navHtml += '<ul class="modern-submenu">';
            for (var j = 0; j < item.Children.length; j++) {
                var child = item.Children[j];
                navHtml += '<li class="modern-submenu-item">';
                
                if (child.target == "_blank") {
                    navHtml += '<a href="javascript:;" class="modern-submenu-link" data-url="' + child.Item.Url + '" target="' + child.target + '">';
                } else {
                    navHtml += '<a href="javascript:;" class="modern-submenu-link" data-buttons="' + JSON.stringify(child.Item.Elements) + '" data-url="' + child.Item.Url + '">';
                }
                
                // 子菜单图标
                if (child.Item.IconName) {
                    navHtml += '<i class="fa ' + getBootstrapIcon(child.Item.IconName) + '" style="margin-right: 8px;"></i>';
                }
                
                navHtml += '<span>' + child.Item.Name + '</span></a>';
                
                // 三级菜单
                if (child.Children && child.Children.length > 0) {
                    navHtml += '<ul class="modern-submenu" style="margin-left: 20px;">';
                    for (var k = 0; k < child.Children.length; k++) {
                        var grandChild = child.Children[k];
                        navHtml += '<li class="modern-submenu-item">';
                        
                        if (grandChild.target == "_blank") {
                            navHtml += '<a href="javascript:;" class="modern-submenu-link" data-url="' + grandChild.Item.Url + '" target="' + grandChild.target + '">';
                        } else {
                            navHtml += '<a href="javascript:;" class="modern-submenu-link" data-url="' + grandChild.Item.Url + '">';
                        }
                        
                        if (grandChild.Item.IconName) {
                            navHtml += '<i class="fa ' + getBootstrapIcon(grandChild.Item.IconName) + '" style="margin-right: 8px;"></i>';
                        }
                        
                        navHtml += '<span>' + grandChild.Item.Name + '</span></a></li>';
                    }
                    navHtml += '</ul>';
                }
                
                navHtml += '</li>';
            }
            navHtml += '</ul>';
            
        } else {
            // 没有子菜单的项目
            if (item.target == "_blank") {
                navHtml += '<a href="javascript:;" class="modern-nav-link" data-url="' + item.Item.Url + '" target="' + item.target + '">';
            } else {
                navHtml += '<a href="javascript:;" class="modern-nav-link" data-url="' + item.Item.Url + '">';
            }
            
            // 图标
            if (item.Item.IconName) {
                navHtml += '<i class="modern-nav-icon fa ' + getBootstrapIcon(item.Item.IconName) + '"></i>';
            } else {
                navHtml += '<i class="modern-nav-icon fa fa-file"></i>';
            }
            
            // 文本
            navHtml += '<span class="modern-nav-text">' + item.Item.Name + '</span></a>';
        }
        
        navHtml += '</li>';
    }
    
    navHtml += '</ul>';
    return navHtml;
}

// LayUI图标到Bootstrap图标的映射
function getBootstrapIcon(layuiIcon) {
    var iconMap = {
        'layui-icon-app': 'fa-th-large',
        'layui-icon-home': 'fa-home',
        'layui-icon-user': 'fa-user',
        'layui-icon-set': 'fa-cog',
        'layui-icon-file': 'fa-file',
        'layui-icon-chart': 'fa-bar-chart',
        'layui-icon-table': 'fa-table',
        'layui-icon-form': 'fa-edit',
        'layui-icon-list': 'fa-list',
        'layui-icon-search': 'fa-search',
        'layui-icon-add-1': 'fa-plus',
        'layui-icon-edit': 'fa-pencil',
        'layui-icon-delete': 'fa-trash',
        'layui-icon-refresh': 'fa-refresh',
        'layui-icon-download': 'fa-download',
        'layui-icon-upload': 'fa-upload',
        'layui-icon-print': 'fa-print',
        'layui-icon-export': 'fa-external-link',
        'layui-icon-import': 'fa-sign-in',
        'layui-icon-save': 'fa-save',
        'layui-icon-close': 'fa-times',
        'layui-icon-ok': 'fa-check',
        'layui-icon-help': 'fa-question-circle',
        'layui-icon-notice': 'fa-bell',
        'layui-icon-email': 'fa-envelope',
        'layui-icon-phone': 'fa-phone',
        'layui-icon-location': 'fa-map-marker',
        'layui-icon-time': 'fa-clock-o',
        'layui-icon-date': 'fa-calendar',
        'layui-icon-dollar': 'fa-dollar',
        'layui-icon-rmb': 'fa-yen',
        'layui-icon-star': 'fa-star',
        'layui-icon-heart': 'fa-heart',
        'layui-icon-fire': 'fa-fire',
        'layui-icon-gift': 'fa-gift',
        'layui-icon-diamond': 'fa-diamond',
        'layui-icon-key': 'fa-key',
        'layui-icon-lock': 'fa-lock',
        'layui-icon-unlock': 'fa-unlock',
        'layui-icon-shield': 'fa-shield',
        'layui-icon-camera': 'fa-camera',
        'layui-icon-picture': 'fa-picture-o',
        'layui-icon-video': 'fa-video-camera',
        'layui-icon-music': 'fa-music',
        'layui-icon-headset': 'fa-headphones',
        'layui-icon-game': 'fa-gamepad',
        'layui-icon-cart': 'fa-shopping-cart',
        'layui-icon-shop': 'fa-shopping-bag',
        'layui-icon-goods': 'fa-cube',
        'layui-icon-order': 'fa-clipboard',
        'layui-icon-money': 'fa-money',
        'layui-icon-account': 'fa-credit-card',
        'layui-icon-report': 'fa-line-chart',
        'layui-icon-analysis': 'fa-pie-chart',
        'layui-icon-statistics': 'fa-area-chart',
        'layui-icon-database': 'fa-database',
        'layui-icon-server': 'fa-server',
        'layui-icon-cloud': 'fa-cloud',
        'layui-icon-wifi': 'fa-wifi',
        'layui-icon-bluetooth': 'fa-bluetooth',
        'layui-icon-usb': 'fa-usb',
        'layui-icon-battery': 'fa-battery-full',
        'layui-icon-signal': 'fa-signal',
        'layui-icon-computer': 'fa-desktop',
        'layui-icon-laptop': 'fa-laptop',
        'layui-icon-tablet': 'fa-tablet',
        'layui-icon-mobile': 'fa-mobile',
        'layui-icon-watch': 'fa-clock-o',
        'layui-icon-tv': 'fa-tv',
        'layui-icon-radio': 'fa-volume-up',
        'layui-icon-speaker': 'fa-volume-down',
        'layui-icon-microphone': 'fa-microphone',
        'layui-icon-website': 'fa-globe',
        'layui-icon-link': 'fa-link',
        'layui-icon-unlink': 'fa-unlink',
        'layui-icon-share': 'fa-share',
        'layui-icon-more': 'fa-ellipsis-h',
        'layui-icon-more-vertical': 'fa-ellipsis-v',
        'layui-icon-flag': 'fa-flag',
        'layui-icon-fonts': 'fa-font',
        'layui-icon-theme': 'fa-paint-brush',
        'layui-icon-engine': 'fa-cogs',
        'layui-icon-console': 'fa-terminal',
        'layui-icon-auz': 'fa-shield',
        'layui-icon-spread-left': 'fa-angle-double-left',
        'layui-icon-shrink-right': 'fa-angle-double-right',
        'layui-icon-snowflake': 'fa-snowflake-o',
        'layui-icon-tips': 'fa-lightbulb-o',
        'layui-icon-note': 'fa-sticky-note',
        'layui-icon-senior': 'fa-star-o',
        'layui-icon-template': 'fa-file-text',
        'layui-icon-praise': 'fa-thumbs-up',
        'layui-icon-tread': 'fa-thumbs-down',
        'layui-icon-male': 'fa-male',
        'layui-icon-female': 'fa-female',
        'layui-icon-survey': 'fa-question',
        'layui-icon-tree': 'fa-sitemap',
        'layui-icon-prev': 'fa-chevron-left',
        'layui-icon-next': 'fa-chevron-right',
        'layui-icon-up': 'fa-chevron-up',
        'layui-icon-down': 'fa-chevron-down',
        'layui-icon-left': 'fa-arrow-left',
        'layui-icon-right': 'fa-arrow-right',
        'layui-icon-circle': 'fa-circle',
        'layui-icon-dot': 'fa-circle',
        'layui-icon-loading': 'fa-spinner',
        'layui-icon-loading-1': 'fa-refresh fa-spin',
        'layui-icon-addition': 'fa-plus-circle',
        'layui-icon-subtraction': 'fa-minus-circle',
        'layui-icon-release': 'fa-play-circle',
        'layui-icon-about': 'fa-info-circle',
        'layui-icon-question': 'fa-question-circle',
        'layui-icon-pause': 'fa-pause-circle',
        'layui-icon-stop': 'fa-stop-circle',
        'layui-icon-step': 'fa-step-forward',
        'layui-icon-play': 'fa-play',
        'layui-icon-prev-circle': 'fa-step-backward',
        'layui-icon-next-circle': 'fa-step-forward',
        'layui-icon-record': 'fa-circle',
        'layui-icon-volume': 'fa-volume-up',
        'layui-icon-mute': 'fa-volume-off'
    };
    
    return iconMap[layuiIcon] || 'fa-circle-o';
}

// 导航菜单搜索功能
function initNavSearch() {
    $('#navSearch').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();
        var navItems = $('.modern-nav-item, .modern-submenu-item');
        
        if (searchTerm === '') {
            navItems.show();
            $('.modern-submenu').removeClass('show');
        } else {
            navItems.hide();
            
            navItems.each(function() {
                var text = $(this).find('.modern-nav-text, span').text().toLowerCase();
                if (text.indexOf(searchTerm) !== -1) {
                    $(this).show();
                    // 如果是子菜单项，也显示父菜单
                    if ($(this).hasClass('modern-submenu-item')) {
                        $(this).closest('.modern-submenu').addClass('show').prev('.modern-nav-link').parent().show();
                    }
                }
            });
        }
    });
}

// 初始化现代化导航
function initModernNav() {
    // 侧边栏切换功能
    $('#sidebarToggle').on('click', function() {
        $('#modernSidebar').toggleClass('collapsed');
        $('#mainContent').toggleClass('sidebar-collapsed');
    });
    
    // 子菜单展开/收缩
    $(document).on('click', '[data-toggle="submenu"]', function(e) {
        e.preventDefault();
        var $this = $(this);
        var $submenu = $this.next('.modern-submenu');
        var $toggle = $this.find('.modern-nav-toggle');
        
        $submenu.toggleClass('show');
        $toggle.toggleClass('rotated');
    });
    
    // 菜单项点击事件
    $(document).on('click', '.modern-nav-link[data-url], .modern-submenu-link[data-url]', function(e) {
        if (!$(this).attr('data-toggle')) {
            // 移除其他活动状态
            $('.modern-nav-link, .modern-submenu-link').removeClass('active');
            // 添加当前活动状态
            $(this).addClass('active');
        }
    });
    
    // 移动端菜单切换
    $(window).on('resize', function() {
        if ($(window).width() <= 768) {
            $('#modernSidebar').removeClass('collapsed');
            $('#mainContent').removeClass('sidebar-collapsed');
        }
    });
    
    // 移动端点击遮罩关闭菜单
    $(document).on('click', function(e) {
        if ($(window).width() <= 768 && !$(e.target).closest('#modernSidebar, #sidebarToggle').length) {
            $('#modernSidebar').removeClass('mobile-show');
        }
    });
    
    // 移动端菜单按钮
    $('#sidebarToggle').on('click', function() {
        if ($(window).width() <= 768) {
            $('#modernSidebar').toggleClass('mobile-show');
        }
    });
    
    // 初始化搜索功能
    initNavSearch();
}

// 页面加载完成后初始化
$(document).ready(function() {
    initModernNav();
});
