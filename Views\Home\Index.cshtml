
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>OWMS-OV</title>
    <meta name="Keywords" content="OWMS-OV">
    <meta name="Description" content="OWMS-OV">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="//at.alicdn.com/t/font_tnyc012u2rlwstt9.css" media="all" />
    <link rel="stylesheet" href="/lib/bootstrap-3.4.1-dist/css/bootstrap.min.css" media="all" />
    <link rel="stylesheet" href="/lib/fontawesome/font-awesome.min.css" media="all" />
    <link rel="stylesheet" href="/css/bootstrap-nav.css" media="all" />
    <link rel="stylesheet" href="/css/main.css" media="all" />
    <link rel="shortcut  icon" type="image/x-icon" href="/images/hih/透明logo.gif" media="screen" />

    <style>
        #csCode {
            font-style: normal; /* 取消斜体样式 */
            font-size: 1.5em; /* 增加字体大小 */
            font-weight: bold; /* 加粗字体 */
            color: #000; /* 设置字体颜色（黑色） */
            background-color: #ffd700; /* 设置背景色为金色 */
            padding: 5px 10px; /* 内边距 */
            border-radius: 12px; /* 边角圆润 */
            border: 2px solid #ffcc00; /* 设置边框颜色为稍暗的金色 */
        }

        .layui-nav-child.adjust-right {
            left: auto;
            right: 0px;
        }
    </style>


</head>
<body class="main_body">
    <div class="layui-layout layui-layout-admin">
        <!-- 顶部 -->
        <div class="modern-header">
            <div class="modern-header-content">
                <div class="modern-header-left">
                    <!-- 显示/隐藏菜单 -->
                    <button type="button" class="modern-sidebar-toggle" id="sidebarToggle">
                        <i class="fa fa-bars"></i>
                    </button>
                    <a href="#" class="modern-logo">OWMS-OV</a>
                    <span class="modern-version">Version: @ViewBag.Version</span>
                </div>
                <!-- 搜索 -->
                @*  <div class="layui-form component">
                <select name="modules" lay-verify="required" lay-search="">
                <option value="">直接选择或搜索选择</option>
                </select>
                <i class="layui-icon">&#xe615;</i>
                </div> *@
                <!-- 天气信息 -->
                @* <div class="weather" pc>
                <div id="tp-weather-widget"></div>
                <script>(function (T, h, i, n, k, P, a, g, e) { g = function () { P = h.createElement(i); a = h.getElementsByTagName(i)[0]; P.src = k; P.charset = "utf-8"; P.async = 1; a.parentNode.insertBefore(P, a) }; T["ThinkPageWeatherWidgetObject"] = n; T[n] || (T[n] = function () { (T[n].q = T[n].q || []).push(arguments) }); T[n].l = +new Date(); if (T.attachEvent) { T.attachEvent("onload", g) } else { T.addEventListener("load", g, false) } }(window, document, "script", "tpwidget", "//widget.seniverse.com/widget/chameleon.js"))</script>
                <script>
                tpwidget("init", {
                "flavor": "slim",
                "location": "WX4FBXXFKE4F",
                "geolocation": "enabled",
                "language": "zh-chs",
                "unit": "c",
                "theme": "chameleon",
                "container": "tp-weather-widget",
                "bubble": "disabled",
                "alarmType": "badge",
                "color": "#FFFFFF",
                "uid": "U9EC08A15F",
                "hash": "039da28f5581f4bcb5c799fb4cdfb673"
                });
                tpwidget("show");</script>
                </div> *@
                <div class="modern-header-right">
                    <!-- 客户代码显示 -->
                    <div class="modern-customer-code">
                        <span>Customer Code: </span>
                        <span class="modern-code-value" id="csCode"></span>
                    </div>

                    <!-- 用户菜单 -->
                    <div class="modern-user-menu">
                        <div class="modern-user-info" id="modernUserInfo">
                            <i class="fa fa-user-circle"></i>
                            <span id="usernametop">用户名</span>
                            <i class="fa fa-chevron-down"></i>
                        </div>
                        <div class="modern-user-dropdown" id="modernUserDropdown">
                            <a href="javascript:;" class="modern-dropdown-item changePasswordLink">
                                <i class="fa fa-key"></i>
                                <span>Change Password</span>
                            </a>
                            <a href="/Login/Logout" class="modern-dropdown-item signOut">
                                <i class="fa fa-sign-out"></i>
                                <span>Login Out</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 左侧导航 -->
        <div class="modern-sidebar" id="modernSidebar">
            <!-- 用户信息区域 -->
            <div class="modern-nav-user">
                <div class="modern-nav-user-name" id="username">用户名</div>
                <div class="modern-nav-user-role">OWMS-OV System</div>
            </div>

            <!-- 搜索框 -->
            <div class="modern-nav-search">
                <input type="text" placeholder="搜索菜单..." id="navSearch">
            </div>

            <!-- 导航菜单容器 -->
            <div class="navBar" id="modernNavBar"></div>
        </div>
        <!-- 右侧内容 -->
        <div class="modern-main-content" id="mainContent">
            <div class="modern-tabs-container" id="modernTabsContainer">
                <!-- 标签页标题栏 -->
                <div class="modern-tabs-header">
                    <ul class="modern-tabs-nav" id="modernTabsNav">
                        <li class="modern-tab-item active" data-tab-id="home">
                            <i class="fa fa-home"></i>
                            <span>首页</span>
                        </li>
                    </ul>

                    <!-- 标签页操作菜单 -->
                    <div class="modern-tabs-actions">
                        <div class="modern-action-dropdown">
                            <button class="modern-action-btn" id="modernActionBtn">
                                <i class="fa fa-cog"></i>
                                <span>页面操作</span>
                                <i class="fa fa-chevron-down"></i>
                            </button>
                            <div class="modern-action-menu" id="modernActionMenu">
                                <a href="javascript:;" class="modern-action-item refresh refreshThis">
                                    <i class="fa fa-refresh"></i>
                                    <span>刷新当前</span>
                                </a>
                                <a href="javascript:;" class="modern-action-item closePageOther">
                                    <i class="fa fa-times-circle"></i>
                                    <span>关闭其他</span>
                                </a>
                                <a href="javascript:;" class="modern-action-item closePageAll">
                                    <i class="fa fa-times"></i>
                                    <span>关闭全部</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 标签页内容区域 -->
                <div class="modern-tabs-content">
                    <div class="modern-tab-pane active" data-tab-id="home">
                        <iframe src="/Home/Main" class="modern-iframe"></iframe>
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部 -->
        @* <div class="layui-footer footer">
        <p>©版权所有 @@2024 - OWMS-OV 版权所有®
        </div> *@

    </div>

    <!-- 移动导航 -->
    <div class="site-tree-mobile layui-hide"><i class="layui-icon">&#xe602;</i></div>
    <div class="site-mobile-shade"></div>

    <script type="text/javascript" src="/lib/jquery/jquery.min.js"></script>
    <script type="text/javascript" src="/layui/layui.js"></script>
    <script type="text/javascript" src="/lib/bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="/js/leftNav.js?v2.8.11"></script>
    <script type="text/javascript" src="/js/modernNav.js?v1.0.0"></script>
    <script type="text/javascript" src="/js/index.js?v2.0.0"></script>
</body>
</html>