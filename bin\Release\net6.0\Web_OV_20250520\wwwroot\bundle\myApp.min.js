var appModule=angular.module("myApp",["ui.bootstrap"]);(function(){appModule.controller("compoment.selectModalCtrl",["$scope","$uibModalInstance","selectModalOptions",function(n,t,i){var r=this,u,e,f;r.selectItem=i.retProperty;class o{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){const n=this.params.api.getModel().getRowCount();this.eGui.innerHTML=`<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${n}</span>`}}e={columnDefs:i.columns,rowData:[],rowSelection:i.isCheckSingle==!0?"single":"multiple",onGridReady:function(n){u=n.api},statusBar:{statusPanels:[{statusPanel:o,align:"left"},]}};r.specialFilter=function(){return{...i.filterParams,filterText:r.filterText}};f=function(){$.ajax({url:i.url,type:"post",data:r.specialFilter(),success:function(n){u.setGridOption("rowData",n.data)},error:function(n){n.responseText&&myAlert.message.error(n.responseText)}})};r.gridReflesh=function(){f()};r.confirm=function(){const n=u.getSelectedRows();if(n.length==0){myAlert.message.warn("please select one item");return}i.callback(n);t.close(n)};r.cancel=function(){t.dismiss()};r.init=function(){r.title=i.title;new agGrid.Grid(document.getElementById("agGrid"),e);f()}}]);appModule.factory("selectModal",["$uibModal",function(n){function t(t){n.open({templateUrl:"/Component/SelectModal",controller:"compoment.selectModalCtrl as vm",backdrop:"static",size:t.size||"lg",resolve:{selectModalOptions:function(){return t}}})}return{open:t}}])})();appModule.directive("selectPicker",["$timeout",function(n){return{restrict:"A",link:function(t,i,r){t.$watch(function(){return $(i).find("option").length},function(u){if(u>0){var f=$(i).find("option").eq(0).val();t[r.ngModel]!==f&&(t[r.ngModel]=f);n(function(){$(i).selectpicker("refresh")})}});t.$watch(r.ngDisabled,function(){n(function(){$(i).selectpicker("refresh")})});t.$watch(r.ngModel,function(){n(function(){$(i).selectpicker("refresh")})})}}}]),function(){appModule.directive("busyIf",[function(){return{restrict:"A",scope:{busyIf:"="},link:function(n,t){n.$watch("busyIf",function(){n.busyIf?abp.ui.setBusy($(t)):abp.ui.clearBusy($(t))})}}}])}(),function(){appModule.directive("buttonBusy",function(){return{restrict:"A",scope:{buttonBusy:"="},link:function(n,t,i){var s=!1,f=$(t),u=f.find("span"),e=null,r=f.find("i"),o=null;n.$watch("buttonBusy",function(){if(n.buttonBusy)f.attr("disabled","disabled"),r.length&&(o=r.attr("class"),r.removeClass(),r.addClass("fa fa-spin fa-spinner")),i.busyText&&u.length&&(e=u.html(),u.html(i.busyText)),s=!0;else{if(!s)return;f.removeAttr("disabled");r.length&&o&&(r.removeClass(),r.addClass(o));u.length&&e&&u.html(e)}})}}})}();appModule.directive("modalInert",function(n){return{restrict:"A",link:function(t,i){t.$on("modalOpen",function(){n(function(){i.attr("inert","")})});t.$on("modalClose",function(){n(function(){i.removeAttr("inert")})})}}});appModule.directive("modalMovable",["$document",function(n){return{restrict:"AC",link:function(t,i){function c(n){f=n.pageY-h;u=n.pageX-s;r.left+u<-r.width/2?u=-r.left-r.width/2:r.left+u>window.innerWidth-r.width/2&&(u=window.innerWidth-r.width/2-r.left);r.top+f<0?f=-r.top:r.top+f>window.innerHeight-o&&(f=window.innerHeight-o-r.top);e.css({top:f+"px",left:u+"px"})}function l(){n.unbind("mousemove",c);n.unbind("mouseup",l)}var s=0,h=0,u=0,f=0;setTimeout(function(){$(".modal-header").css({cursor:"move"})},1e3);var e=i.parent(),r=null,o=0;e.css({position:"relative"});e.on("mousedown",function(t){if(t.target&&t.target.className&&t.target.className=="modal-title"){r==null&&(r=e[0].getBoundingClientRect(),o=$(".modal-header")[0].offsetHeight);t.preventDefault();s=t.pageX-u;h=t.pageY-f;n.on("mousemove",c);n.on("mouseup",l)}})}}}]);appModule.directive("stringToNumber",function(){return{require:"ngModel",link:function(n,t,i,r){r.$parsers.push(function(n){return n===""?null:parseFloat(n,10)});r.$formatters.push(function(n){return n===null||n===undefined?"":""+n})}}});angular.module("myApp").controller("basic.files.filesManage",["$scope","$uibModal","$http","$uibModalInstance","$timeout","$http","options",function(n,t,i,r,u,i,f){function w(){return Date.now().toString(36)+Math.random().toString(36).slice(2,11)}function ht(){var t=document.createElement("input");t.type="file";t.style.display="none";t.multiple=!0;t.addEventListener("change",async function(){var f,o,r,u,y;if(t.files.length>0){if(e.upLoadTitle=="Uploading..."){f=Array.from(t.files);k.concat(f);o=f.map(n=>{var t=new FormData;return t.append("file",n),t.append("upLoadPath",e.upLoadPath),{fileName:n.name,fileType:n.type,fileSize:(n.size/1024).toFixed(2)+" KB",formData:t,file:n,upLoadPath:e.upLoadPath,progress:0,id:w()}});s.push(...o);c.applyTransaction({add:o});return}for(s={},k=Array.from(t.files),s=k.map(n=>{var t=new FormData;return t.append("file",n),t.append("upLoadPath",e.upLoadPath),{fileName:n.name,fileType:n.type,fileSize:(n.size/1024).toFixed(2)+" KB",formData:t,file:n,upLoadPath:e.upLoadPath,progress:0,id:w()}}),c.applyTransaction({add:s}),e.upLoadTitle="Uploading...",r=0;r<s.length;r++){if(e.isCancel)break;if(u=await i.post("/Files/CheckFolder",{DirName:s[r].upLoadPath,BucketName:e.bucketName}),u=u.data,u.code=="200"){var l=s[r].upLoadPath+s[r].fileName,p=s[r].file,a=localStorage.getItem("stsCredentials");let n=a?JSON.parse(a):null;(!n||ct(n))&&(y=await i.post("/Files/GetSTS",{fileName:l,bucketName:e.bucketName}),n=y.data.data,localStorage.setItem("stsCredentials",JSON.stringify(n)));try{const t=new OSS({region:"oss-cn-beijing",accessKeyId:n.accessKeyId,accessKeySecret:n.accessKeySecret,stsToken:n.securityToken,bucket:e.bucketName}),i={partSize:7340032,progress:n=>{e.isCancel&&t.cancel(),tt(s[r].id,Math.round(n*100))}},u=await t.multipartUpload(l,p,i);v();h.push(s[r]);localStorage.setItem("upLoadGridData",JSON.stringify(h))}catch(t){e.title="Upload failed";myAlert.message.error("Failed to upload file: "+t.message)}}}e.upLoadTitle="Upload completed";n.$apply()}});t.click()}function ct(n){const t=new Date(n.expiration);return Date.now()>t.getTime()}function tt(n,t){c.getRowNode(n).data.progress=t;c.refreshCells({force:!0})}async function lt(){var n=y.getSelectedRows();if(n.length===0){myAlert.message.info("Pelease select File！");return}myAlert.message.confirm("Are you sure you want to delete this file?",async function(t){var r,u,f;if(t){for(r=!0,u=0;u<n.length;u++){const t=await i.post("/Files/DeleteFile",{filePath:n[u].filePath,BucketName:e.bucketName});if(t.data.code!="200"&&(myAlert.message.error(t.data.msg),r=!1),!r)break}r&&(myAlert.message.success("Delete successfully！"),f=$("#treeview7").treeview("getSelected"),p=f[0].nodeId,v())}})}function at(n){var t=n.defaultItems;if(t!=null){const r=[];return e.isReName&&r.push({name:"Rename",action:function(){var t=n.node,i;t?(i=t.data,d(i)):myAlert.message.error("Please select data！")}}),e.isDownLoad&&r.push({name:"Download",action:function(){var t=n.node,i;t?(i=t.data,ii(i)):myAlert.message.error("Please select data！")}}),e.isShare&&r.push({name:"Share",action:function(){var t=n.node,i;t?(i=t.data,nt(i)):myAlert.message.error("Please select data！")}}),e.isDelete&&r.push({name:"Delete",action:function(){var t=n.node;t?myAlert.message.confirm("Are you sure you want to delete this file?",function(n){if(n){var r=t.data;const n=i.post("/Files/DeleteFile",{filePath:r.filePath,bucketName:e.bucketName});n.then(function(n){n.data.code!="200"?myAlert.message.error(n.data.msg):(myAlert.message.success(n.data.msg),v())})}}):myAlert.message.error("Please select data！")}}),[...r,...t]}return null}function vt(){var n=t.open({templateUrl:"/Files/NewOrEditName",controller:"basic.files.newOrEditName as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{filePath:e.upLoadPath,bucketName:e.bucketName}}}});n.result.then(function(){v()},function(){})}function et(n){const t=n.value||0,i=`width: ${t}%;`;return`
        <div class="progress-container" style="display: flex; justify-content: center; align-items: center; height: 100%;">
            <div class="progress" style="width: 100%;margin: auto;">
                <div class="progress-bar" role="progressbar" style="${i}" aria-valuenow="${t}" aria-valuemin="0" aria-valuemax="100">${t}%</div>
            </div>
        </div>
    `}async function ti(){var i,t;if(e.downloadTitle="Downloading",n.$apply(),i=y.getSelectedRows(),i.length==0){myAlert.message.error("Please select data！");return}for(a=i.map(n=>({fileName:n.fileName,filePath:n.filePath,fileSize:n.fileSize,progress:0,id:w()})),l.applyTransaction({add:a}),t=0;t<a.length;t++){if(await ot(a[t].filePath,a[t].fileSize,a[t].id,a[t].fileName),e.isCancel)break;o.push(a[t])}localStorage.setItem("downLoadGridData",JSON.stringify(o));e.downloadTitle="Download completed";n.$apply()}async function ii(t){var i=[{fileName:t.fileName,filePath:t.filePath,fileSize:t.fileSize,progress:0,id:w()}],r;if(l.applyTransaction({add:i}),e.downloadTitle=="Downloading"){a.push(i[0]);return}for(e.downloadTitle="Downloading",n.$apply(),r=0;r<i.length;r++){if(await ot(i[r].filePath,i[r].fileSize,i[r].id,i[r].fileName),e.isCancel)break;o.push(i[r])}localStorage.setItem("downLoadGridData",JSON.stringify(o));e.downloadTitle="Download completed";n.$apply()}async function ot(n,t,i,r){const s=5242880;let u=0,o=t;const h=[];while(u<o){const r=Math.min(u+s-1,o-1),t=await fetch(`/Files/DownLoadFile`,{method:"POST",headers:{Range:`bytes=${u}-${r}`,"Content-Type":"application/json"},body:JSON.stringify({BucketName:e.bucketName,FilePath:n})});if(!t.ok){console.error(`HTTP error! status: ${t.status}`);break}const f=await t.blob();if(h.push(f),e.isCancel)break;l.getRowNode(i).data.progress=(Math.min(u+s,o)/o*100).toFixed(2);l.refreshCells({force:!0});u+=s}if(!e.isCancel){const a=new Blob(h),c=URL.createObjectURL(a),f=document.createElement("a");f.href=c;f.download=r;document.body.appendChild(f);f.click();document.body.removeChild(f);URL.revokeObjectURL(c)}}var e=this,g,b,d,it,y,p,rt,v,ut,ft,c;n.$on("$viewContentLoaded",function(){App.initAjax()});e.upLoadTitle="Upload Progress";e.isCancel=!1;e.isUpLoad=f.isUpLoad;e.isDelete=f.isDelete;e.isDownLoad=f.isDownLoad;e.isNewDir=f.isNewDir;e.isReName=f.isReName;e.isShare=f.isShare;e.bucketName=f.bucketName;e.prefix=f.prefix;class st{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.classList.add("custom-header-group","row","light");this.eGui.style.width="100%";this.eGui.innerHTML=`
                    <div class="col-xs-6 text-left">
                           ${e.isUpLoad?`<button id = "uploadFile" class="btn btn-primary" >Upload</button>`:``}
                           ${e.isNewDir?`<button id="createFolder" class="btn btn-success" >New folder</button>`:``}
                           ${e.isDelete?`<button id="deleteFolder" class="btn btn-default" >Delete folder</button>`:``}
                    </div>
                    <div class="col-xs-6 text-right">
                        ${e.isDownLoad?`<button id="download" class="btn btn-default  btn-sm" type="button">Download</button>`:``}
                        ${e.isReName?`<button id="reName" class="btn btn-default  btn-sm" type="button">Rename</button>`:``}
                        ${e.isDelete?`<button id="deleteFile" class="btn btn-default  btn-sm" type="button">Delete</button>`:``}
                    </div>
                `;e.isUpLoad&&this.eGui.querySelector("#uploadFile").addEventListener("click",()=>{ht()});e.isNewDir&&this.eGui.querySelector("#createFolder").addEventListener("click",()=>{vt()});e.isDownLoad&&this.eGui.querySelector("#download").addEventListener("click",()=>{g()});e.isDelete&&(this.eGui.querySelector("#deleteFile").addEventListener("click",()=>{lt()}),this.eGui.querySelector("#deleteFolder").addEventListener("click",()=>{e.deleteFolde()}));e.isReName&&this.eGui.querySelector("#reName").addEventListener("click",()=>{e.reName()})}addButton(n,t,i,r){const f=`<button id="${t}" class="${i}">${r}</button>`,u=this.eGui.querySelector(`#${n}`);u&&u.insertAdjacentHTML("beforeend",f)}getGui(){return this.eGui}destroy(){}}g=function(){const n=y.getSelectedRows();if(n.length==0){myAlert.message.info("Pelease Select File!");return}ti()};const ri=async n=>{const t=await $.ajax({url:`/Files/GetUrlDownLoad`,type:"POST",data:{FilePath:n,BucketName:e.bucketName}});window.location.href=t};e.share=function(){const n=y.getSelectedRows();if(n.length!=1){myAlert.message.info("Pelease Select a File!");return}nt(n[0])};var nt=function(n){const t=$.ajax({url:`/Files/GetUrlDownLoad`,type:"POST",data:{FilePath:n.filePath,BucketName:e.bucketName}});t.then(function(n){myAlert.message.success("Successfully obtained the sharing link and copied it to the clipboard!");navigator.clipboard.writeText(n)})},s={},h=[],o=[],a=[],k=null;b="";e.deleteFolde=function(){if(!e.upLoadPath){myAlert.message.info("Pelease select Folder！");return}var n=i.post("/Files/DeleteFile",{filePath:e.upLoadPath,bucketName:e.bucketName});n.then(function(n){n.data.code=="200"?(myAlert.message.success(n.data.msg),p=0,v()):myAlert.message.error(n.data.msg)})};e.reName=function(){var n=y.getSelectedRows();if(n.length!=1){myAlert.message.info("Pelease select a File！");return}d(n[0])};d=function(n){var i=t.open({templateUrl:"/Files/NewOrEditName",controller:"basic.files.newOrEditName as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{fileName:n.fileName,filePath:n.filePath,upLoadPath:e.upLoadPath,bucketName:e.bucketName,title:"rename",type:"file"}}}});i.result.then(function(){v()})};it=[{headerGroupComponent:st,children:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"File Name",field:"fileName",sortable:!0,filter:!0},{headerName:"File Size",field:"fileSize",valueGetter:function(n){return(n.data.fileSize/1024).toFixed(2)+" KB"}},{headerName:"Modified Time",field:"modifiedTime",sortable:!0}]}];p=0;rt={columnDefs:it,suppressMovableColumns:!0,dataTypeDefinitions:{object:{baseDataType:"object",extendsDataType:"object",valueParser:n=>({name:n.newValue}),valueFormatter:n=>n.value==null?"":n.value.name}},defaultColDef:{flex:1},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){y=n.api},getContextMenuItems:at};e.init=function(){u(function(){v();h=JSON.parse(localStorage.getItem("upLoadGridData"))||[];o=JSON.parse(localStorage.getItem("downLoadGridData"))||[];new agGrid.Grid(document.getElementById("fileGrid"),rt);new agGrid.Grid(document.getElementById("upLoadGrid"),pt);new agGrid.Grid(document.getElementById("downloadGrid"),ni)},0)};v=function(){$.ajax({url:`/Files/GetTree`,type:"post",data:{bucketName:e.bucketName,prefix:e.prefix},success:function(n){if(n.code=="200"){$("#treeview7").treeview({data:n.data});$("#treeview7").treeview("expandAll",{silent:!0});$("#treeview7").on("nodeSelected",function(n,t){e.upLoadPath=t.filePath;y.setGridOption("rowData",t.files)});$("#treeview7").on("click",".list-group-item",function(){const n=$(this),t=n[0].dataset.nodeid,i=$("#treeview7").treeview("getNode",t);$("#treeview7").treeview("uncheckAll",{silent:!0});p=i.nodeId});$("#treeview7").on("nodeUnselected",function(n,t){t.nodeId===p&&$("#treeview7").treeview("selectNode",[p,{silent:!0}])});const t=$("#treeview7").treeview("getNode",p);$("#treeview7").treeview("selectNode",[t.nodeId,{silent:!0}]);y.setGridOption("rowData",t.files);e.upLoadPath=t.filePath}else myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})};e.cancel=function(){e.upLoadTitle=="Uploading..."||e.downloadTitle=="Downloading"?myAlert.message.confirm("Uploading file or DownLoad File, are you sure you want to cancel the upload and close the page?",function(n){if(n)e.isCancel=!0,r.dismiss("cancel");else return}):r.dismiss("cancel")};ut=function(n){var t=n.defaultItems;return t!=null?[{name:"Delete",action:function(){var t=n.node,i;t?(i=t.data,n.api.applyTransaction({remove:[i]}),h=[],c.forEachNode(function(n){n.data.file=null;h.push(n.data)}),localStorage.setItem("upLoadGridData",JSON.stringify(h))):myAlert.message.error("请先选择要删除的行！")}},...t]:null};ft=function(n){var t=n.defaultItems;return t!=null?[{name:"Delete",action:function(){var t=n.node,i;t?(i=t.data,n.api.applyTransaction({remove:[i]}),o=[],l.forEachNode(function(n){n.data.file=null;o.push(n.data)}),localStorage.setItem("downLoadGridData",JSON.stringify(o))):myAlert.message.error("请先选择要删除的行！")}},...t]:null};class yt{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.classList.add("custom-header-group","row","light");this.eGui.style.width="100%";this.eGui.innerHTML=`
                    <div class="col-xs-6 text-left">
                    </div>
                    <div class="col-xs-6 text-right">
                        <button id="deleteUpLoad" class="btn btn-default  btn-sm" type="button">Delete</button>
                        <button id="emptyUpLoad" class="btn btn-default  btn-sm" type="button">Empty</button>
                    </div>
                `;this.eGui.querySelector("#deleteUpLoad").addEventListener("click",()=>{wt()});this.eGui.querySelector("#emptyUpLoad").addEventListener("click",()=>{bt()})}getGui(){return this.eGui}destroy(){}}var pt={columnDefs:[{headerGroupComponent:yt,children:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"File Name",field:"fileName"},{headerName:"File Type",field:"fileType"},{headerName:"File Size",field:"fileSize"},{headerName:"UpLoad Path",field:"upLoadPath"},{headerName:"Upload Progress",field:"progress",cellRenderer:et}]}],suppressMovableColumns:!0,dataTypeDefinitions:{object:{baseDataType:"object",extendsDataType:"object",valueParser:n=>({name:n.newValue}),valueFormatter:n=>n.value==null?"":n.value.name}},rowData:JSON.parse(localStorage.getItem("upLoadGridData"))||[],getRowId:n=>String(n.data.id),defaultColDef:{flex:1},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){c=n.api},getContextMenuItems:ut},wt=function(){var n=c.getSelectedNodes(),t=n.map(n=>n.data);c.applyTransaction({remove:t});h=[];c.forEachNode(function(n){n.data.file=null;h.push(n.data)});localStorage.setItem("upLoadGridData",JSON.stringify(h))},bt=function(){c.setGridOption("rowData",[]);h=[];localStorage.setItem("upLoadGridData",JSON.stringify(h))},fi=function(){var t=0;c.forEachNode(function(){t++});e.upLoadTitle="待上传："+t;n.$apply()};e.downloadTitle="Download";class kt{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.classList.add("custom-header-group","row","light");this.eGui.style.width="100%";this.eGui.innerHTML=`
             <div class="col-xs-6 text-left">
             </div>
             <div class="col-xs-6 text-right">
                 <button id="downLoadDelete" class="btn btn-default  btn-sm" type="button">Delete</button>
                 <button id="downLoadEmpty" class="btn btn-default  btn-sm" type="button">Empty</button>
             </div>
         `;this.eGui.querySelector("#downLoadDelete").addEventListener("click",()=>{dt()});this.eGui.querySelector("#downLoadEmpty").addEventListener("click",()=>{gt()})}getGui(){return this.eGui}destroy(){}}var dt=function(){var n=l.getSelectedNodes(),t=n.map(n=>n.data);l.applyTransaction({remove:t});o=[];l.forEachNode(function(n){n.data.file=null;o.push(n.data)});localStorage.setItem("downLoadGridData",JSON.stringify(o))},gt=function(){l.setGridOption("rowData",[]);o=[];localStorage.setItem("downLoadGridData",JSON.stringify(o))},l,ni={columnDefs:[{headerGroupComponent:kt,children:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"File Name",field:"fileName"},{headerName:"File Path",field:"filePath"},{headerName:"File Size",field:"fileSize",valueGetter:function(n){return(n.data.fileSize/1024).toFixed(2)+" KB"}},{headerName:"DownLoad Progress",field:"progress",cellRenderer:et}]}],suppressMovableColumns:!0,dataTypeDefinitions:{object:{baseDataType:"object",extendsDataType:"object",valueParser:n=>({name:n.newValue}),valueFormatter:n=>n.value==null?"":n.value.name}},rowData:JSON.parse(localStorage.getItem("downLoadGridData"))||[],getRowId:n=>String(n.data.id),defaultColDef:{flex:1},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){l=n.api},getContextMenuItems:ft}}]);angular.module("myApp").controller("basic.files.filesUpLoad",["$scope","$uibModal","$http","$uibModalInstance",function(n,t,i,r){var u=this,f;n.$on("$viewContentLoaded",function(){App.initAjax()});var h=class{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="custom-header-group";this.eGui.innerHTML=`
                    <button id="uploadFile" class="btn btn-primary">选择文件</button>
                    <button id="createFolder" class="btn btn-success" >上传文件</button>
                `;this.eGui.querySelector("#uploadFile").addEventListener("click",()=>{f()})}getGui(){return this.eGui}destroy(){}},e=function(n){var t=n.defaultItems;return t!=null?[{name:"Delete",action:function(){alert("Delete")}},...t]:null},o,s={columnDefs:[{headerGroupComponent:CustomHeaderGroup,children:[{headerName:"名称",field:"name",sortable:!0,filter:!0},{headerName:"类型",valueGetter:n=>n.data.isDirectory?"文件夹":"文件"},{headerName:"大小",field:"size"},{headerName:"路径",field:"path"},{headerName:"上传进度",field:"jindu",sortable:!0}]}],dataTypeDefinitions:{object:{baseDataType:"object",extendsDataType:"object",valueParser:n=>({name:n.newValue}),valueFormatter:n=>n.value==null?"":n.value.name}},rowData:[{name:"Documents",isDirectory:!0,size:"",lastModified:"2023-09-08"},{name:"Photo.jpg",isDirectory:!1,size:"2 MB",lastModified:"2023-09-01"},{name:"Music",isDirectory:!0,size:"",lastModified:"2023-08-15"},],defaultColDef:{flex:1},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){o=n.api},getContextMenuItems:e};u.init=function(){new agGrid.Grid(document.getElementById("upLoadGrid"),s)};u.cancel=function(){r.dismiss("cancel")};f=function(){alert("执行上传！")}}]);angular.module("myApp").controller("basic.files.newOrEditName",["$scope","$uibModalInstance","options","$http",function(n,t,i,r){var u=this;u.dismiss=function(){t.dismiss("cancel")};u.title="New Folder";u.requestParams={};u.isFile=i.type=="file"?!0:!1;u.bucketName=i.bucketName;u.init=function(){i.type=="file"?(u.title=i.title,u.requestParams.fileName=i.fileName,u.requestParams.filePath=i.filePath,u.requestParams.upLoadPath=i.upLoadPath):u.requestParams.filePath=i.filePath};u.save=function(){var n,i,f,e;if(!u.requestParams.fileName){myAlert.message.error("文件夹名称不能为空！");return}if(u.requestParams.fileName.indexOf("/")>=0){myAlert.message.error("The input name cannot contain ' / '!");return}u.isFile?(n=u.requestParams.upLoadPath+u.requestParams.fileName,i=r.post("/Files/ReNameFile",{newFilePath:n,filePath:u.requestParams.filePath,bucketName:u.bucketName}),i.then(function(n){n.data.code==200?(console.log(n),myAlert.message.success(n.data.msg),t.close()):myAlert.message.error(n.data.msg)})):(f=u.requestParams.filePath+u.requestParams.fileName,e=r.post("/Files/CheckFolder",{DirName:u.requestParams.filePath,bucketName:u.bucketName}),e.then(function(n){if(n.data.code==200){var i=r.post("/Files/CreateDir",{DirName:f,bucketName:u.bucketName});i.then(function(n){n.data.code==200?(console.log(n),myAlert.message.success(n.data.msg),t.close()):myAlert.message.error(n.data.msg)},function(){})}else myAlert.message.error(i.data.msg)}))}}]);angular.module("myApp").controller("warehouse.selectModal",["$scope","$uibModal","$uibModalInstance","options","$timeout",function(n,t,i){var r=this,u;r.requestParams={};r.init=function(){};u=function(){};r.save=function(){};r.cancel=function(){i.dismiss("cancel")}}]);angular.module("myApp").controller("orderManagement.bolSystem.bolSystem",["$scope","$uibModal","$timeout","$http",function(n,t,i,r){var u=this,s,f,o;n.$on("$viewContentLoaded",function(){App.initAjax()});u.requestParams={symbol:"="};s={Create:"Create",Edit:"Edit",View:"View"};o=function(){r({method:"POST",url:"/BOLSystem/GetBOLDetail",data:JSON.stringify(u.specialFilter())}).then(function(n){f.updateGridOptions({rowData:n.data.data})}).catch(function(){})};u.cnDropDown=[];u.ckDropDown=[];u.taDropDown=[];u.init=function(){const n={columnDefs:e,defaultColDef:{maxWidth:350,filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0,excelMode:"windows"}},pagination:!0,rowSelection:"multiple",enableRangeSelection:!0,suppressRowClickSelection:!0,onGridReady:function(n){f=n.api},onRowDoubleClicked:function(n){var i=n.data,r=[],f;r.push({"订单号":i.订单号,"集装箱箱号":i.集装箱箱号,"运单号":i.运单号});f=t.open({templateUrl:"/BOLSystem/BOLSystemEdit",controller:"orderManagement.bolSystem.bolSystemEdit as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{orders:r,dataItem:i}}}});f.result.then(function(){u.search()})}};new agGrid.createGrid(document.getElementById("agGrid"),n);u.initData();i(function(){o()},100)};u.initData=function(){$("#startETA").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.startETA=n.date.format("YYYY-MM-DD")});$("#endETA").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.endETA=n.date.format("YYYY-MM-DD")});$("#startReleaseDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.startReleaseDate=n.date.format("YYYY-MM-DD")});$("#endReleaseDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.endReleaseDate=n.date.format("YYYY-MM-DD")});$("#startPickDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.startPickDate=n.date.format("YYYY-MM-DD")});$("#endPickDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.endPickDate=n.date.format("YYYY-MM-DD")});$("#startDeliveryDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.startDeliveryDate=n.date.format("YYYY-MM-DD")});$("#endDeliveryDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.endDeliveryDate=n.date.format("YYYY-MM-DD")});$("#startDCDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.startDCDate=n.date.format("YYYY-MM-DD")});$("#endDCDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.endDCDate=n.date.format("YYYY-MM-DD")});$("#startPODrcDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.startPODrcDate=n.date.format("YYYY-MM-DD")});$("#endPODrcDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.requestParams.endPODrcDate=n.date.format("YYYY-MM-DD")});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Consignee Name",desc:"BOL"}}).then(function(n){n.data.code===200?(u.cnDropDown=n.data.data,u.cnDropDown.length>0&&(u.requestParams.consigneeName=u.cnDropDown[0].text)):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Transport Agent",type:"1"}}).then(function(n){n.data.code===200?u.taDropDown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Location"}}).then(function(n){n.data.code===200?u.ckDropDown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})};const e=[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,field:"ID",sortable:!1,filterParams:{suppressSorting:!1},pinned:"left"},{headerName:"Customer",field:"客户名"},{headerName:"PI No.",field:"PI#"},{headerName:"Batch/Shipments",field:"Customer_BATCH"},{headerName:"M/BL",field:"提单号"},{headerName:"ETD",field:"ETD"},{headerName:"ETA",field:"ETA"},{headerName:"Port of arrival",field:"目的港"},{headerName:"ISF order received date",field:"ISF指令收到时间"},{headerName:"Clearance release time",field:"清关完成时间"},{headerName:"D/O sent time",field:"DO发送时间"},{headerName:"Freight release time",field:"船公司放行时间"},{headerName:"Order No.",field:"订单号"},{headerName:"Transport mode BY",field:"运输方式"},{headerName:"rail ETA",field:"ETA_CN"},{headerName:"BOL",field:"运单号"},{headerName:"Container No.",field:"集装箱箱号"},{headerName:"Seal No.",field:"集装箱封号"},{headerName:"Type",field:"商品规格"},{headerName:"Lot no.",field:"LOT"},{headerName:"QTY",field:"数量",width:100},{headerName:"Pallets",field:"托盘数",width:120},{headerName:"Power",field:"单片瓦数",width:120},{headerName:"WATT",field:"瓦数",width:120},{headerName:"Delivery Place",field:"收货人公司"},{headerName:"SO",field:"tsw_so"},{headerName:"Release Order Date",field:"ReleaseOrderDate"},{headerName:"Pick up date",field:"提货日期"},{headerName:"Pick up time",field:"b",width:161},{headerName:"Carrier",field:"车队"},{headerName:"Delivery date",field:"派送日期"},{headerName:"Delivery time",field:"a",width:161},{headerName:"Delivery completion date",field:"送货完成日期"},{headerName:"POD receipt completion date",field:"POD返回日期"},{headerName:"Empty return",field:"Empty_return"},{headerName:"transit warehousing",field:"是否入库"},{headerName:"Location",field:"所在仓库"},{headerName:"Inbound Date",field:"入库日期"},{headerName:"Onbound Date",field:"出库日期"},{headerName:"DDP settlement",field:"DDP_settlement"},{headerName:"Remark",field:"备注"},];u.save=function(){};u.specialFilter=function(){return u.requestParams};u.search=function(){o()};u.resetting=function(){u.requestParams={consigneeName:u.cnDropDown[0].text,symbol:"="};o()};u.releaseOrder=function(){u.openUpdateDate("releaseOrder")};u.plannning=function(){var i=f.getSelectedRows(),e,o,n,s;if(i.length===0){myAlert.message.error("Please select a row of data！");return}for(e=!0,o=[],n=0;n<i.length;n++){if(i[n].送货完成日期||i[n].POD返回日期){e=!1;break}o.push({"订单号":i[n].订单号,"集装箱箱号":i[n].集装箱箱号,"运单号":i[n].运单号})}if(!e){myAlert.message.error("The delivery completion message already exists and cannot be scheduled !");return}s=t.open({templateUrl:"/BOLSystem/BOLSystemPlanning",controller:"orderManagement.bolSystem.bolSystemPlanning as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{}}}});s.result.then(function(n){r({method:"POST",url:"/BOLSystem/UpdatePlanning",data:JSON.stringify({orders:JSON.stringify(o),result:JSON.stringify(n)})}).then(function(n){n.data.code===200?(myAlert.message.success(n.data.msg),u.search()):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})};u.pickUp=function(){u.openUpdateDate("pickUp")};u.completion=function(){u.openUpdateDate("completion")};u.empytReturn=function(){u.openUpdateDate("empytReturn")};u.updateETA=function(){var n=f.getSelectedRows(),i;if(n.length!=1){myAlert.message.error("Please select a row of data！");return}i=t.open({templateUrl:"/BOLSystem/BOLSystemUpdate",controller:"orderManagement.bolSystem.bolSystemUpdate as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{type:"updateETA",orderNo:n[0].订单号,mbl:n[0].提单号,eta:n[0].ETA}}}});i.result.then(function(){u.search()})};u.updateRailETA=function(){u.openUpdateDate("updateRailETA")};u.openUpdateDate=function(n){var e=f.getSelectedRows(),o,s,i,h;if(e.length===0){myAlert.message.error("Please select a row of data！");return}for(o=[],s=!0,i=0;i<e.length;i++){if((n==="completion"||n==="empytReturn")&&(!e[i].提货日期||!e[i].派送日期)){s=!1;break}o.push({"订单号":e[i].订单号,"集装箱箱号":e[i].集装箱箱号})}if(!s){myAlert.message.error("Completion status cannot be updated without a delivery schedule !");return}h=t.open({templateUrl:"/BOLSystem/BOLSystemUpdate",controller:"orderManagement.bolSystem.bolSystemUpdate as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{type:n}}}});h.result.then(function(t){var i="";n==="releaseOrder"?i="/BOLSystem/UpdateCN":n==="pickUp"?i="/BOLSystem/UpdatePickup":n==="completion"?i="/BOLSystem/UpdateCompletion":n==="empytReturn"?i="/BOLSystem/UpdateEmptyReturn":n==="updateRailETA"&&(i="/BOLSystem/UpdateETACN");r({method:"POST",url:i,data:JSON.stringify({orders:JSON.stringify(o),result:JSON.stringify(t)})}).then(function(n){n.data.code===200?(myAlert.message.success(n.data.msg),u.search()):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})},function(){}).finally(function(){})};u.newDDP=async function(){var n=f.getSelectedRows();if(n.length===0){myAlert.message.error("Please select a row of data！");return}myAlert.message.confirm("Are you sure to batch the following delivery data to add DDP orders ?",async function(t){var u,f,i,e;if(t){for(u=[],f=!0,i=0;i<n.length;i++){if(!n[i].订单号||!n[i].集装箱箱号||!n[i].提单号){f=!1;break}u.push({"订单号":n[i].订单号,"集装箱箱号":n[i].集装箱箱号,"提单号":n[i].提单号})}if(!f){myAlert.message.error("Completion status cannot be updated without a delivery schedule !");return}for(i=0;i<u.length;i++){e="Line "+(i+1)+", new DDP order [BL:"+u[i].提单号+","+u[i].集装箱箱号+"]...";commonUtil.message(e,"info");const n=await r({method:"POST",url:"/BOLSystem/CreateNewDDP",data:JSON.stringify({orderNo:u[i].订单号,mbl:u[i].提单号,containerNo:u[i].集装箱箱号})});n.data.code===200?i===u.length-1&&myAlert.message.success("New DDP order created successfully！"):myAlert.message.error(n.data.msg)}}})};u.exportBOL=function(){var i=f.getSelectedRows(),r,u,n,e;if(i.length===0){myAlert.message.error("Please select a row of data！");return}for(r=[],u=!0,n=0;n<i.length;n++)i[n].运单号&&i[n].集装箱箱号||(u=!1),r.push({bol:i[n].运单号,containerNo:i[n].集装箱箱号});if(!u){myAlert.message.error("The BOL number or container number is empty and cannot be print !");return}e=t.open({templateUrl:"/BOLSystem/BolSystemExport",controller:"orderManagement.bolSystem.bolSystemExport as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{gridData:r}}}})};u.exportExcel=function(){u.busying=!0;r({method:"POST",url:"/BOLSystem/GetBOLDetail",data:JSON.stringify(u.specialFilter())}).then(function(n){var i;if(n.data.code===200){var u=n.data.data,r=new ExcelJS.Workbook,t=r.addWorksheet("Sheet 1"),f=e.map(function(n){return n.headerName?{header:n.headerName,key:n.field}:null}).filter(function(n){return n!==null});t.columns=f;u.forEach(function(n){t.addRow(n)});e.forEach(function(n,i){var r=0;n.headerName&&(r=Math.max(r,n.headerName.length),t.getColumn(i+1).width=r+2)});t.eachRow({includeEmpty:!0},function(n){n.eachCell({includeEmpty:!0},function(n){if(n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},n.font={name:"Arial",size:10},isDateValue(n.value)){n.value instanceof Date||(n.value=toChinaStandardTime(n.value));const t=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",timeZone:"Asia/Shanghai"});n.value=t.format(n.value)}})});i=t.getRow(1);i.font={name:"Arial",size:10,bold:!0};i.alignment={horizontal:"center"};r.xlsx.writeBuffer().then(function(n){var i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.href=URL.createObjectURL(i);t.download="ExportExcel.xlsx";t.click()})}}).catch(function(n){console.log(n)}).finally(function(){u.busying=!1})};u.rayosExport=function(){u.busying=!0;r({method:"POST",url:"/BOLSystem/GetBOLDetail",data:JSON.stringify(u.specialFilter())}).then(function(n){var i;if(n.data.code===200){var u=n.data.data,r=new ExcelJS.Workbook,t=r.addWorksheet("Sheet 1");t.columns=[{header:"BOL",key:"运单号"},{header:"Container No.",key:"集装箱箱号"},{header:"Seal No.",key:"集装箱封号"},{header:"Type",key:"商品规格"},{header:"Lot no.",key:"lot"},{header:"QTY",key:"数量"},{header:"Pallets",key:"托盘数"},{header:"Power",key:"单片瓦数"},{header:"WATT",key:"瓦数"},{header:"SO",key:"tsw_so"},{header:"Pick up date",key:"提货日期"},{header:"Delivery date",key:"派送日期"},{header:"Delivery time",key:"a"},{header:"transit warehousing",key:"是否入库"},{header:"Location",key:"所在仓库"},];u.forEach(function(n){t.addRow(n)});e.forEach(function(n,i){var r=0;n.headerName&&(r=Math.max(r,n.headerName.length),t.getColumn(i+1).width=r+2)});t.eachRow({includeEmpty:!0},function(n){n.eachCell({includeEmpty:!0},function(n){if(n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},n.font={name:"Dotum",size:9},isDateValue(n.value)){n.value instanceof Date||(n.value=toChinaStandardTime(n.value));const t=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",timeZone:"Asia/Shanghai"});n.value=t.format(n.value)}})});i=t.getRow(1);i.font={name:"Dotum",size:10};i.alignment={horizontal:"center"};r.xlsx.writeBuffer().then(function(n){var i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.href=URL.createObjectURL(i);t.download="RayosExport.xlsx";t.click()})}}).catch(function(n){console.log(n)}).finally(function(){u.busying=!1})};u.exportReport=function(){u.busying=!0;r({method:"POST",url:"/BOLSystem/GetBOLDetail",data:JSON.stringify(u.specialFilter())}).then(function(n){var i;if(n.data.code===200){var f=n.data.data,r=new ExcelJS.Workbook,t=r.addWorksheet("Sheet 1"),o=e.map(function(n){if(!n.headerName)return null;if(u.requestParams.consigneeName!="Rayos"){if(n.field!="是否入库"&&n.field!="派送日期"&&n.field!="a"&&n.field!="备注"&&n.field!="ID"&&n.field!="订单号"&&n.field!="ColCheck"&&n.field!="ISF指令收到时间"&&n.field!="清关完成时间"&&n.field!="DO发送时间"&&n.field!="船公司放行时间"&&n.field!="运单号"&&n.field!="车队"&&n.field!="DDP_settlement")return{header:n.headerName,key:n.field}}else if(n.field!="SO"||n.field!="集装箱箱号"||n.field!="商品规格"||n.field!="lot"||n.field!="数量"||n.field!="托盘数"||n.field!="单片瓦数"||n.field!="瓦数"||n.field!="提货日期"||n.field!="送货完成日期"||n.field!="POD返回日期"||n.field!="备注")return{header:n.headerName,key:n.field};return null}).filter(function(n){return n!==null});t.columns=o;f.forEach(function(n){t.addRow(n)});e.forEach(function(n,i){var r=0;n.headerName&&(r=Math.max(r,n.headerName.length),t.getColumn(i+1).width=r+2)});t.eachRow({includeEmpty:!0},function(n){n.eachCell({includeEmpty:!0},function(n){if(n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},n.font={name:"Arial",size:10},isDateValue(n.value)){n.value instanceof Date||(n.value=toChinaStandardTime(n.value));const t=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",timeZone:"Asia/Shanghai"});n.value=t.format(n.value)}})});i=t.getRow(1);i.font={name:"Arial",size:10,bold:!0};i.alignment={horizontal:"center"};r.xlsx.writeBuffer().then(function(n){var i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.href=URL.createObjectURL(i);t.download="ExportReport.xlsx";t.click()})}}).catch(function(n){console.log(n)}).finally(function(){u.busying=!1})}}]);angular.module("myApp").controller("orderManagement.bolSystem.bolSystemEdit",["$scope","$uibModalInstance","options","$http","$timeout",function(n,t,i,r,u){var f=this,e,o;f.cancel=function(){t.dismiss("cancel")};f.requestParams={};e=[];o=[];f.isEmpty=!1;f.init=function(){u(function(){$("#datetimepicker2").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.date=n.date.format("YYYY-MM-DD")});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Transport Agent",type:"2"}}).then(function(n){n.data.code===200?f.carrierDropDown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Location"}}).then(function(n){n.data.code===200?f.locationDropDown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});$("#pickUpdate").datetimepicker({format:"YYYY-MM-DD HH:mm",useCurrent:!1}).on("dp.change",function(n){f.requestParams.pickUpdate=n.date.format("YYYY-MM-DD HH:mm")});$("#deliveryDate").datetimepicker({format:"YYYY-MM-DD HH:mm",useCurrent:!1}).on("dp.change",function(n){f.requestParams.deliveryDate=n.date.format("YYYY-MM-DD HH:mm")});$("#inboundDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.inboundDate=n.date.format("YYYY-MM-DD")});$("#outboundDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.outboundDate=n.date.format("YYYY-MM-DD")});$("#deliveryCompletionDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.deliveryCompletionDate=n.date.format("YYYY-MM-DD")});$("#podDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.podDate=n.date.format("YYYY-MM-DD")});$("#emptyDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.emptyDate=n.date.format("YYYY-MM-DD")});f.requestParams.carrier=i.dataItem.车队;f.requestParams.pickUpdate=i.dataItem.提货日期==null?null:i.dataItem.提货日期+" "+i.dataItem.a;f.requestParams.deliveryDate=i.dataItem.派送日期==null?null:i.dataItem.派送日期+" "+i.dataItem.b;f.requestParams.location=i.dataItem.所在仓库;f.requestParams.inboundDate=i.dataItem.入库日期;f.requestParams.outboundDate=i.dataItem.出库日期;f.requestParams.remark=i.dataItem.备注;f.requestParams.podDate=i.dataItem.POD返回日期;f.requestParams.deliveryCompletionDate=i.dataItem.送货完成日期;f.requestParams.abnormal=i.dataItem.Abnormal_feedback;f.requestParams.emptyDate=i.dataItem.Empty_return;i.dataItem.是否入库=="Yes"&&(f.requestParams.isTransit=!0)},0)};f.bolEmpty=function(){f.isEmpty?(f.afterRequest=f.requestParams,f.requestParams={},f.requestParams.isEmpty=f.isEmpty):(f.requestParams=f.afterRequest,f.requestParams.isEmpty=f.isEmpty)};f.isBusy=!1;f.save=async function(){await r({method:"POST",url:"/BOLSystem/UpdatePlanning",data:JSON.stringify({orders:JSON.stringify(i.orders),result:JSON.stringify(f.requestParams)})}).then(function(){}).catch(function(n){console.log(n)});await r({method:"POST",url:"/BOLSystem/UpdateDelivery",data:JSON.stringify({orders:JSON.stringify(i.orders),result:JSON.stringify(f.requestParams)})}).then(function(n){n.data.code===200?myAlert.message.success(n.data.msg):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});t.close()};f.specialFilter=function(){return{}}}]);angular.module("myApp").controller("orderManagement.bolSystem.bolSystemExport",["$scope","$uibModalInstance","options","$http","$timeout",function(n,t,i,r,u){var f=this,e,o;f.cancel=function(){t.dismiss("cancel")};f.requestParams={};o=[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1},pinned:"left"},{headerName:"BOL Number",field:"bol"},{headerName:"Container No.",field:"containerNo"},];f.init=function(){const n={columnDefs:o,defaultColDef:{flex:1,filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0,excelMode:"windows"}},pagination:!0,rowData:[],rowSelection:"multiple",enableRangeSelection:!0,suppressRowClickSelection:!0,onGridReady:function(n){e=n.api}};console.log(i.gridData);new agGrid.createGrid(document.getElementById("agGrid"),n);u(function(){e.updateGridOptions({rowData:i.gridData})},300)};f.exportAll=async function(){for(var f,u,t=i.gridData,n=0;n<t.length;n++)f=t[n].bol,u=t[n].containerNo,await r({method:"POST",url:"/BOLSystem/ExportBOL",data:JSON.stringify({bol:f,containerNo:u}),responseType:"blob"}).then(function(n){const r=new Blob([n.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.style.display="none";const i=window.URL.createObjectURL(r);t.href=i;t.download=u+".xlsx";document.body.appendChild(t);t.click();window.URL.revokeObjectURL(i);document.body.removeChild(t)}).catch(function(){})};f.selected=async function(){var t=e.getSelectedRows(),n,u,i;if(t.length==0){myAlert.message.error("please select data!");return}for(n=0;n<t.length;n++)u=t[n].bol,i=t[n].containerNo,await r({method:"POST",url:"/BOLSystem/ExportBOL",data:JSON.stringify({bol:u,containerNo:i}),responseType:"blob"}).then(function(n){const u=new Blob([n.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.style.display="none";const r=window.URL.createObjectURL(u);t.href=r;t.download=i+".xlsx";document.body.appendChild(t);t.click();window.URL.revokeObjectURL(r);document.body.removeChild(t)}).catch(function(){})};f.isBusy=!1;f.specialFilter=function(){return{}}}]);angular.module("myApp").controller("orderManagement.bolSystem.bolSystemPlanning",["$scope","$uibModalInstance","options","$http","$timeout",function(n,t,i,r,u){var f=this,e,o;f.cancel=function(){t.dismiss("cancel")};f.requestParams={};e=[];o=[];f.isEmpty=!1;f.init=function(){u(function(){$("#datetimepicker2").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.date=n.date.format("YYYY-MM-DD")});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Transport Agent",type:"2"}}).then(function(n){n.data.code===200?f.carrierDropDown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Location"}}).then(function(n){n.data.code===200?f.locationDropDown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});$("#pickUpdate").datetimepicker({format:"YYYY-MM-DD HH:mm",useCurrent:!1}).on("dp.change",function(n){f.requestParams.pickUpdate=n.date.format("YYYY-MM-DD HH:mm")});$("#deliveryDate").datetimepicker({format:"YYYY-MM-DD HH:mm",useCurrent:!1}).on("dp.change",function(n){f.requestParams.deliveryDate=n.date.format("YYYY-MM-DD HH:mm")});$("#inboundDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.inboundDate=n.date.format("YYYY-MM-DD")});$("#outboundDate").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.requestParams.outboundDate=n.date.format("YYYY-MM-DD")})},0)};f.bolEmpty=function(){f.isEmpty?(f.afterRequest=f.requestParams,f.requestParams={},f.requestParams.isEmpty=f.isEmpty):f.requestParams=f.afterRequest};f.isBusy=!1;f.save=function(){if(!f.isEmpty&&!f.requestParams.isTransit&&(!f.requestParams.carrier||!f.requestParams.pickUpdate||!f.requestParams.deliveryDate)){myAlert.message.error("Planned order error !");return}t.close(f.requestParams)};f.specialFilter=function(){return{}}}]);angular.module("myApp").controller("orderManagement.bolSystem.bolSystemUpdate",["$scope","$uibModalInstance","options","$http","$timeout",function(n,t,i,r,u){var f=this;f.cancel=function(){t.dismiss("cancel")};f.isPickUp=i.type=="pickUp"?!0:!1;f.isReleaseOrder=i.type=="releaseOrder"?!0:!1;f.isCompletion=i.type=="completion"?!0:!1;f.isEmptyReturn=i.type=="empytReturn"?!0:!1;f.isUpdateETA=i.type=="updateETA"?!0:!1;f.isUpdateRailETA=i.type=="updateRailETA"?!0:!1;f.request={};f.init=function(){u(function(){if(i.type=="releaseOrder")f.modalTitle="Release Order",f.title="Release Order date";else if(i.type=="pickUp")f.modalTitle="Pick Up",f.title="Pick Up date";else if(i.type=="completion"){f.title="Delivery completion date";f.modalTitle="Completion";$("#datetimepicker3").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.request.date2=n.date.format("YYYY-MM-DD")})}else i.type=="empytReturn"?(f.modalTitle="Update EmptyReturn",f.title="Empty Return"):i.type=="updateETA"?(f.modalTitle="Update ETA",f.title="ETA",f.request.orderNo=i.orderNo,f.request.mbl=i.mbl,f.request.date=i.eta):i.type=="updateRailETA"&&(f.modalTitle="UpdateRailETA",f.title="rail ETA");$("#datetimepicker2").datetimepicker({format:i.type=="pickUp"?"YYYY-MM-DD HH:mm":"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){f.request.date=n.date.format(i.type=="pickUp"?"YYYY-MM-DD HH:mm":"YYYY-MM-DD")})},0)};f.checkPalles=function(){};f.search=function(){};f.isBusy=!1;f.save=function(){f.isUpdateETA&&r({method:"POST",url:"/BOLSystem/UpdateETA",data:JSON.stringify({orderNo:i.orderNo,eta:f.request.date})}).then(function(n){n.data.code===200?(myAlert.message.success(n.data.msg),f.search()):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});t.close(f.request)};f.specialFilter=function(){return{}}}]);angular.module("myApp").controller("orderManagement.flashReport.flashReport",["$scope","$uibModal","$timeout","$http",function(n,t,i,r){function s(n){const t=new Date(n),i=t.getTimezoneOffset()+480;return new Date(t.getTime()-i*6e4)}function h(n){if(n instanceof Date)return!0;if(typeof n=="string"){const i=/^\d{4}-\d{2}-\d{2}$/;if(!i.test(n))return!1;const t=new Date(n);if(isNaN(t.getTime()))return!1;if(i.exec(n)[1]){const[r,u,f]=i.exec(n)[1].split("-").map(Number);if(t.getFullYear()!==r||t.getMonth()!==u-1||t.getDate()!==f)return!1}return!0}return!1}var u=this,f,e,o;n.$on("$viewContentLoaded",function(){App.initAjax()});u.requestParams={};u.busying=!1;u.specialFilter=function(){var n={};return n.type=u.requestParams.type,n.lotNo=u.requestParams.lotNo,n.outBoundOrderNo=u.requestParams.outBoundOrderNo,n.palletNo=u.requestParams.palletNo,n.sn=u.requestParams.sn,n.outboundCheckType=u.requestParams.outboundCheckType,n};u.init=function(){u.initGrid()};u.initGrid=function(){new agGrid.createGrid(document.getElementById("agGrid"),u.gridOptions)};e=[{headerName:"lotno",field:"lotNo"},{headerName:"Num",field:"num"},{headerName:"Model Type",field:"modelType"},{headerName:"S/N",field:"sn"},{headerName:"Isc(A)",field:"iscA"},{headerName:"Voc(V)",field:"vocV"},{headerName:"Pmax(W)",field:"pmaxW"},{headerName:"Vpm(V)",field:"vpmV"},{headerName:"Ipm(A)",field:"ipmA"},{headerName:"FF",field:"ff"},{headerName:"Pallet No.",field:"palletNo"},{headerName:"Color Grade",field:"colorGrade"},{headerName:"Outbound Order No.",field:"outNoticeNo"},{headerName:"outbound check",field:"outboundCheck"},{headerName:"ScanClass",field:"scanClass"},];u.gridOptions={rowModelType:"serverSide",columnDefs:e,paginationPageSize:20,defaultColDef:{sortable:!0,filter:"agSetColumnFilter",filterParams:{values:getUniqueValues,excelMode:"windows",refreshValuesOnOpen:!1},suppressColumnsToolPanel:!0},getRowId:function(n){const t=n.data.id;return t?t.toString():""},pagination:!0,suppressMovableColumns:!0,rowSelection:"multiple",enableRangeSelection:!0,onPaginationChanged:function(n){n.newPage&&f.setGridOption("defaultColDef",o())},onGridReady:function(n){f=n.api;const t={getRows:function(n){createDataSourceV2("/FlashReport/GetFlashReportData",u.specialFilter(),n)}};n.api.setGridOption("serverSideDatasource",t)}};o=function(){return colDefault={sortable:!0,filter:"agSetColumnFilter",filterParams:{values:getUniqueValues,excelMode:"windows",refreshValuesOnOpen:!1},suppressColumnsToolPanel:!0}};u.search=function(){f.refreshServerSide();f.onFilterChanged()};u.resetting=function(){u.requestParams={};u.search()};u.export=function(){u.busying=!0;r({method:"POST",url:"/FlashReport/GetFlashReportAsExcel",data:u.specialFilter()}).then(function(n){var i;if(n.data.code===200){var u=n.data.data,r=new ExcelJS.Workbook,t=r.addWorksheet("Sheet 1"),f=e.map(function(n){return{header:n.headerName,key:n.field}});t.columns=f;u.forEach(function(n){t.addRow(n)});e.forEach(function(n,i){var r=0;r=Math.max(r,n.headerName.length);t.getColumn(i+1).width=r+5});t.eachRow({includeEmpty:!0},function(n){n.eachCell({includeEmpty:!0},function(n){if(n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},n.font={name:"Arial",size:10},h(n.value)){n.value instanceof Date||(n.value=s(n.value));const t=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",timeZone:"Asia/Shanghai"});n.value=t.format(n.value)}})});i=t.getRow(1);i.font={name:"Arial",size:10,bold:!0};i.alignment={horizontal:"center"};r.xlsx.writeBuffer().then(function(n){var i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.href=URL.createObjectURL(i);t.download="FlashReportData.xlsx";t.click()})}}).catch(function(n){console.log(n)}).finally(function(){u.busying=!1})}}]);angular.module("myApp").controller("wareHouse.editIssueQty",["$scope","$uibModalInstance","options",function(n,t,i){var r=this;r.dismiss=function(){t.dismiss("cancel")};r.requestParams={};r.init=function(){r.requestParams.GoodsNo=i.GoodsNo;r.requestParams.ProblematicQty=i.ProblematicQty};r.save=function(){$.ajax({url:`/Warehouse/EditProblematicQty`,type:"post",contentType:"application/json",data:JSON.stringify({GoodsNo:i.GoodsNo,ProblematicQty:r.requestParams.ProblematicQty,TrackingNumber:i.TrackingNumber}),success:function(){commonUtil.message("保存成功！","success");t.close()},error:function(n){console.error("Error fetching data:",n)}})}}]);angular.module("myApp").controller("warehouse.issueDetail",["$scope","$uibModal","$uibModalInstance","options","$timeout","$compile","selectModal","$http",function(n,t,i,r,u,f,e,o){function g(){$.ajax({url:"/Warehouse/GetCurrenty",type:"post",success:function(n){s.currencies=n.data},error:function(n){console.error("Error fetching currencies:",n)}})}function nt(n){var i=document.createElement("div"),t;return i.classList.add("button-container-ag"),t=document.createElement("button"),t.innerHTML='<i class="bi bi-trash3"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),t.addEventListener("click",function(){s.delete(n.data)}),t.disabled=s.isView,i.appendChild(t),i}function it(n){n.api.autoSizeAllColumns()}function ft(){}function et(t){var i=document.createElement("div"),e,h,o,r,u,c;return i.classList.add("button-container-ag"),e=document.createElement("button"),e.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',e.classList.add("btn","btn-primary","btn-sm"),e.addEventListener("click",function(){t.data.status!="Closed"&&s.requestParams.State!="Closed"?s.editProcess(t.data.processNo):s.View(t.data.processNo)}),h=f(e)(n),i.appendChild(h[0]),o=document.createElement("button"),o.innerHTML='<i class="bi bi-calendar-check-fill"><\/i> Log',o.classList.add("btn","btn-primary","btn-sm"),o.addEventListener("click",function(){d(t.data)}),i.appendChild(o),r=document.createElement("button"),r.innerHTML="Closed",r.classList.add("btn","btn-primary","btn-sm"),r.disabled=s.isView||t.data.status=="Closed",r.addEventListener("click",function(){s.closeProcess(t.data)}),i.appendChild(r),u=document.createElement("button"),u.innerHTML='<i class="bi bi-trash3"><\/i> Delete',u.classList.add("btn","btn-danger","btn-sm"),u.addEventListener("click",function(){s.deleteProcess(t.data)}),u.disabled=s.isView||t.data.status!="New",c=f(u)(n),i.appendChild(c[0]),i}var s=this,v,c,y,p,w,l,b,rt,h,k,d,a;s.requestParams={IsHIHInsurance:!1,IsInsurance:!1,IsInsurancePay:!1,IsWithdrawCase:!1};s.isBusy=!1;c={Create:"Create",Edit:"Edit",View:"View"};s.isNew=!0;s.isView=r.pageStatus==c.View?!0:!1;s.isEdit=r.pageStatus==c.Edit?!0:!1;y=["Inbound damage","Damage in WHS","Missing","Wrong loading","Loading damage","Damage during transportation","Steal"];s.currencies=[];s.init=function(){u(function(){g();s.initDate();s.initSelect();r.TrackingNumber!=null?(p(),new agGrid.createGrid(document.getElementById("gridDetail"),b),l(),new agGrid.createGrid(document.getElementById("grid"),k),h()):$.post("/UserSession/GetUserAuth",function(t){s.requestParams.Operator=t.account;n.$digest()})},0)};s.selectCurrentProcess=["Received problem notification","Notify customers","The customer initiates a claim","Confirm the responsible party","Claimant","Compensation completed"];s.initDate=function(){$("#firstDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"})};s.initSelect=function(){var n=$("#IssueType");$.each(y,function(t,i){n.append($("<option><\/option>").val(i).text(i))});$.each(s.selectCurrentProcess,function(n,t){$("#CurrentProcess").append($("<option><\/option>").val(t).text(t))});$(".selectpicker").selectpicker()};p=function(){$.ajax({url:`/Warehouse/GetIssueTrackingDetail`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:r.TrackingNumber}),success:function(t){var i=JSON.parse(t).data;i.TrackingDate=moment(i.TrackingDate).format("YYYY-MM-DD");s.requestParams=i;s.requestParams.CurrentNode2=s.requestParams.CurrentProcess;s.isNew=s.requestParams.State!="New"?!1:!0;s.requestParams.FirstDate&&$("#firstDate").datepicker("setDate",new Date(s.requestParams.FirstDate));$("#IssueType").val(s.requestParams.IssueType).trigger("change");$("#IsFiles").val(s.requestParams.IsFiles).trigger("change");$("#CurrentProcess").val(s.requestParams.CurrentProcess).trigger("change");$("#CurrentNode2").selectpicker("refresh");n.$digest()},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};s.openProcess=function(){w(s.requestParams)};w=function(n){var i=t.open({templateUrl:"/WareHouse/IssueProcess",controller:"warehouse.IssueProcess as vm",backdrop:"static",size:"full",resolve:{options:function(){return{dataItem:n}}}})};l=function(){$.ajax({url:`/Warehouse/GetIssueDetailList`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:r.TrackingNumber}),success:function(n){var t=JSON.parse(n);v.setGridOption("rowData",t.data)},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};class tt{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){const n=this.params.api.getModel().getRowCount(),t=this.params.api.getModel().getRootNode().childrenAfterSort.reduce((n,t)=>n+(t.data.ProblematicQty||0),0);this.eGui.innerHTML=`
            <div style="display: flex; justify-content: space-between; padding: 0 10px;">
                <span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${n}</span>
                <span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Problematic Qty(PCS): ${t}</span>
            </div>`}}b={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},hide:s.isView,pinned:"right"},{headerName:"HiH #",field:"DocumentNo"},{headerName:"WHS",field:"WHS"},{headerName:"LotNo",field:"LotNo"},{headerName:"Type",field:"Type"},{headerName:"Power",field:"Power"},{headerName:"Qty(pcs)",field:"Qty"},{headerName:"Problematic Qty(PCS)",field:"ProblematicQty",aggFunc:"sum",valueGetter:n=>n.data?n.data.ProblematicQty:0},{headerName:"Inbound Date",field:"InBoundDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.InBoundDate?null:new Date(n.data.InBoundDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"PI Number",field:"PiNo"},{headerName:"Released Date",field:"ReleasedDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.ReleasedDate?null:new Date(n.data.ReleasedDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Consignee",field:"Consignee"},{headerName:"Incoterms",field:"Incoterms"},{headerName:"Outbound Date",field:"OutboundDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.OutboundDate?null:new Date(n.data.OutboundDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Carrier",field:"Carrier"},],components:{buttonCellRenderer:nt},defaultColDef:{filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0}},statusBar:{statusPanels:[{statusPanel:tt,align:"left"},]},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){v=n.api},onFirstDataRendered:it};s.delete=function(n){myAlert.message.confirm("Are you sure you want to delete it?",function(t){t&&$.ajax({url:`/Warehouse/DeleteIssueDetail`,type:"post",data:{Items:n.Id,TrackingNumber:r.TrackingNumber},success:function(n){n.code=="200"?(myAlert.message.success("Delete successfully！"),l()):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};s.save=function(){s.isBusy=!0;s.requestParams.IssueType=$("#IssueType").val();s.requestParams.CurrentProcess=$("#CurrentProcess").val();$.ajax({url:`/Warehouse/CreateOrEditIssue`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:s.requestParams.TrackingNumber,OrderType:"Issue Type",Operator:s.requestParams.Operator,State:"New",FirstDate:s.requestParams.FirstDate!=null?new Date(s.requestParams.FirstDate):null,CurrentProcess:s.requestParams.CurrentProcess,IssueType:s.requestParams.IssueType,IsFiles:$("#IsFiles").val(),Notes:s.requestParams.Notes,Compensation:s.requestParams.CompensationID,InsuranceCompany:s.requestParams.InsuranceCompany,GoodsCurrency:s.requestParams.GoodsCurrency,GoodsMoney:s.requestParams.GoodsMoney,PolicyNo:s.requestParams.PolicyNo,ClaimsCurrency:s.requestParams.ClaimsCurrency,ClaimsAmount:s.requestParams.ClaimsAmount,IsHIHInsurance:s.requestParams.IsHIHInsurance,IsInsurance:s.requestParams.IsInsurance,IsInsurancePay:s.requestParams.IsInsurancePay,IsWithdrawCase:s.requestParams.IsWithdrawCase,CustomerCurrency:s.requestParams.CustomerCurrency,CustomerAmount:s.requestParams.CustomerAmount}),success:function(t){var i=JSON.parse(t);i.code=="200"?(myAlert.message.success(i.msg),s.isEdit||s.close()):myAlert.message.error(i.msg);s.isBusy=!1;n.$apply()},error:function(n){myAlert.message.error(n.responseText)}})};s.openSelect=function(){if(s.requestParams.TrackingNumber){var n=t.open({templateUrl:"/WareHouse/SelectDetail",controller:"wareHouse.selectDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{TrackingNumber:s.requestParams.TrackingNumber}}}});n.result.then(function(){l()},function(){}).finally(function(){})}};s.close=function(){i.dismiss()};rt=function(n){var i=t.open({templateUrl:"/WareHouse/EditIssueQty",controller:"wareHouse.editIssueQty as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{GoodsNo:n.Id,ProblematicQty:n.ProblematicQty,TrackingNumber:r.TrackingNumber}}}});i.result.then(function(){l()},function(){}).finally(function(){})};s.tracking=function(){myAlert.message.confirm("Are you sure you want to start tracking this issue?",function(t){t&&$.ajax({url:`/Warehouse/ReleaseIssueTracking`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:r.TrackingNumber}),success:function(t){var i=JSON.parse(t);i.code=="200"?(myAlert.message.success(i.msg),s.requestParams.State="Tracking",s.isNew=!1,u(function(){$("#CustomerCurrency").selectpicker("refresh")}),n.$apply()):myAlert.message.error(i.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};s.end=function(){myAlert.message.confirm("Are you sure you want to End it?",function(t){t&&$.ajax({url:`/Warehouse/EndIssueTracking`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:r.TrackingNumber}),success:function(t){var i=JSON.parse(t);i.code=="200"?(myAlert.message.success("Close successfully ！"),s.requestParams.State="Closed",s.isView=!0,v.redrawRows(),gridApi.redrawRows(),n.$apply()):myAlert.message.error(i.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};s.hih=function(){s.requestParams.IsHIHInsurance||(s.requestParams.IsInsurance=!1,s.requestParams.IsInsurancePay=!1,s.requestParams.IsWithdrawCase=!1)};s.insurance=function(){s.requestParams.IsInsurance||(s.requestParams.IsInsurancePay=!1,s.requestParams.IsWithdrawCase=!1)};s.pay=function(){s.requestParams.IsInsurancePay||(s.requestParams.IsWithdrawCase=!1)};s.openFiles=function(){var n=t.open({templateUrl:"/Files/FilesManage",controller:"basic.files.filesManage as vm",backdrop:"static",size:"full",keyboard:!1,resolve:{options:function(){return{bucketName:"hih-owms",prefix:"业务/"+r.TrackingNumber+"/",isUpLoad:!s.isView,isDownLoad:!0,isReName:!s.isView,isShare:!1,isDelete:!s.isView,isNewDir:!s.isView}}}});n.result.then(function(){},function(){}).finally(function(){h()})};s.selectOperator=function(){e.open({title:"select Operator",url:"/Warehouse/GetUserListByCsCode",showFilter:!0,isCheckSingle:!0,height:300,columns:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"User Name",field:"userName",filterable:!1},],retProperty:"userName",callback:function(n){var t=n[0];s.requestParams.Operator=t.userName}})};s.Compensation=function(){e.open({title:"select Compensation party",url:"/Warehouse/GetSupplierList",showFilter:!0,isCheckSingle:!0,height:300,columns:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"Supplier ID",field:"supplierID",filterable:!1},{headerName:"Supplier abbreviation",field:"supplierName",filterable:!1},{headerName:"Specify the full name of the payment",field:"supplierFullName",filterable:!1},{headerName:"Related Units",field:"relatedUnits",filterable:!1},],retProperty:"userName",callback:function(n){var t=n[0];s.requestParams.CompensationID=t.supplierID;s.requestParams.Compensation=t.supplierName}})};s.InsuranceCompany=function(){e.open({title:"select Insurance company",url:"/Warehouse/GetInsuranceCompanyList",showFilter:!0,isCheckSingle:!0,height:300,columns:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"Supplier ID",field:"supplierID",filterable:!1},{headerName:"Supplier abbreviation",field:"supplierName",filterable:!1},{headerName:"Specify the full name of the payment",field:"supplierFullName",filterable:!1},{headerName:"Related Units",field:"relatedUnits",filterable:!1},],retProperty:"userName",callback:function(n){var t=n[0];s.requestParams.InsuranceCompany=t.supplierName}})};h=function(){$.ajax({url:`/Warehouse/GetIssueProcess`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:r.TrackingNumber}),success:function(n){gridApi.setGridOption("rowData",n)},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};class ut{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){const n=this.params.api.getModel().getRowCount();this.eGui.innerHTML=`<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${n}</span>`}}k={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRendererProcess",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},minWidth:300,pinned:"right"},{headerName:"ProcessNo",field:"processNo"},{headerName:"Generation Date",field:"createTime",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.createTime?null:new Date(n.data.createTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Executor",field:"executor"},{headerName:"Schedule Date",field:"scheduleDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.scheduleDate?null:new Date(n.data.scheduleDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Content of Execution",field:"content"},{headerName:"Execution deadline",field:"deadline",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.deadline?null:new Date(n.data.deadline)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Status",field:"status"}],defaultColDef:{flex:1,filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0}},components:{buttonCellRendererProcess:et},statusBar:{statusPanels:[{statusPanel:ut,align:"left"},]},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){gridApi=n.api},onFirstDataRendered:ft};d=function(n){var i="",r;s.requestParams.State=="Closed"?i="Closed":n.status=="Closed"&&(i="Closed");r=t.open({templateUrl:"/WareHouse/IssueProcessLog",controller:"warehouse.issueProcessLog as vm",backdrop:"static",size:"full",resolve:{options:function(){return{ProcessNo:n.processNo,TrackingNumber:n.trackingNumber,State:i}}}});r.result.then(function(){},function(){}).finally(function(){h()})};s.deleteProcess=function(n){myAlert.message.confirm("Are you sure you want to delete it?",function(t){t&&$.ajax({url:`/Warehouse/DeleteProcess`,type:"post",data:{processNo:n.processNo},success:function(n){n.code=="200"?(myAlert.message.success("Delete successfully！"),h()):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};s.closeProcess=function(n){myAlert.message.confirm("Are you sure you want to Closed it?",function(t){t&&$.ajax({url:`/Warehouse/ProcessClosed`,type:"post",data:{processNo:n.processNo},success:function(n){n.code=="200"?(h(),myAlert.message.success("Closed successfully！")):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};s.saveProcess=function(){commonUtil.message("保存成功！","success")};s.addProcess=function(){s.requestParams.TrackingNumber&&a(null,c.Create)};s.editProcess=function(n){a(n,c.Edit)};s.View=function(n){a(n,c.View)};s.updateNode=function(){o({method:"POST",url:"/Warehouse/UpdateCurrentNode",data:$.param({TrackingNumber:s.requestParams.TrackingNumber,CurrentProcess:s.requestParams.CurrentProcess}),headers:{"Content-Type":"application/x-www-form-urlencoded"}}).then(function(n){n.data.code===200?($("#CurrentProcess").val(s.requestParams.CurrentProcess).trigger("change"),myAlert.message.success("Update success！")):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)})};a=function(n,i){var u=t.open({templateUrl:"/WareHouse/ProcessCreate",controller:"warehouse.processCreate as vm",backdrop:"static",size:"full",resolve:{options:function(){return{TrackingNumber:r.TrackingNumber,ProcessNo:n,pageStatus:i}}}});u.result.then(function(){},function(){}).finally(function(){h()})}}]);angular.module("myApp").controller("warehouse.IssueProcess",["$scope","$uibModal","$uibModalInstance","options",function(n,t,i,r){function l(n){var i=document.createElement("div"),r,f,t;return i.classList.add("button-container-ag"),r=document.createElement("button"),r.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',r.classList.add("btn","btn-primary","btn-sm"),r.addEventListener("click",function(){n.data.status!="Execute"?u.edit(n.data.processNo):u.View(n.data.processNo)}),i.appendChild(r),f=document.createElement("button"),f.innerHTML='<i class="bi bi-calendar-check-fill"><\/i> Log',f.classList.add("btn","btn-primary","btn-sm"),f.addEventListener("click",function(){c(n.data)}),i.appendChild(f),t=document.createElement("button"),t.innerHTML="Delete",t.classList.add("btn","btn-danger","btn-sm"),t.addEventListener("click",function(){u.delete(n.data)}),n.data.status=="Execute"&&t.setAttribute("disabled",!0),i.appendChild(t),i}var u=this,o={Create:"Create",Edit:"Edit",View:"View"},s,f,h,c,e;u.requestParams={};u.init=function(){$("#firstDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});var n=$("#IssueType");$.each(["Inbound damage","Damage in WHS","Missing","Wrong loading","Loading damage","Damage during transportation","steal"],function(t,i){n.append($("<option><\/option>").val(i).text(i))});$(".selectpicker").selectpicker();r!=null&&(u.requestParams=r.dataItem,$("#IssueType").val(u.requestParams.IssueType).trigger("change"));h()};f=function(){$.ajax({url:`/Warehouse/GetIssueProcess`,type:"post",contentType:"application/json",data:JSON.stringify({TrackingNumber:r.dataItem.TrackingNumber}),success:function(n){s.setGridOption("rowData",n)},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};h=function(){function i(n){n.api.autoSizeAllColumns()}class n{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){const n=this.params.api.getModel().getRowCount();this.eGui.innerHTML=`<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${n}</span>`}}var t={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},pinned:"right"},{headerName:"ProcessNo",field:"processNo"},{headerName:"Generation Date",field:"createTime",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.createTime?null:new Date(n.data.createTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Executor",field:"executor"},{headerName:"Schedule Date",field:"scheduleDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.scheduleDate?null:new Date(n.data.scheduleDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Content of Execution",field:"content"},{headerName:"Execution deadline",field:"deadline",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.deadline?null:new Date(n.data.deadline)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Status",field:"status"}],defaultColDef:{maxWidth:300,filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0}},components:{buttonCellRenderer:l},statusBar:{statusPanels:[{statusPanel:n,align:"left"},]},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){s=n.api},onFirstDataRendered:i};new agGrid.createGrid(document.getElementById("grid"),t);f()};c=function(n){var i=t.open({templateUrl:"/WareHouse/IssueProcessLog",controller:"warehouse.issueProcessLog as vm",backdrop:"static",size:"full",resolve:{options:function(){return{ProcessNo:n.processNo,TrackingNumber:n.trackingNumber}}}})};u.delete=function(n){myAlert.message.confirm("Are you sure you want to delete it?",function(t){t&&$.ajax({url:`/Warehouse/DeleteProcess`,type:"post",data:{processNo:n.processNo},success:function(n){n.code=="200"?(myAlert.message.success("Delete successfully！"),f()):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};u.add=function(){e(null,o.Create)};u.edit=function(n){e(n,o.Edit)};u.View=function(n){e(n,o.View)};e=function(n,i){var u=t.open({templateUrl:"/WareHouse/ProcessCreate",controller:"warehouse.processCreate as vm",backdrop:"static",size:"full",resolve:{options:function(){return{TrackingNumber:r.dataItem.TrackingNumber,ProcessNo:n,pageStatus:i}}}});u.result.then(function(){f()},function(){}).finally(function(){})};u.close=function(){i.close()}}]);angular.module("myApp").controller("warehouse.issueProcessLog",["$scope","$uibModal","$uibModalInstance","options","$compile",function(n,t,i,r){function h(n){var f=document.createElement("div"),i,t;return f.classList.add("button-container-ag"),i=document.createElement("button"),i.innerHTML='<i class="bi bi-pencil-fill"><\/i> View',i.classList.add("btn","btn-primary","btn-sm"),i.addEventListener("click",function(){s(n.data.content)}),f.appendChild(i),t=document.createElement("button"),t.innerHTML='<i class="bi bi-trash3"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),t.addEventListener("click",function(){u.delete(n.data)}),t.disabled=r.State=="Closed",f.appendChild(t),f}var u=this,e,f,o,s;u.requestParams={};u.isView=r.State=="Closed"?!0:!1;u.init=function(){o()};f=function(){$.ajax({url:`/Warehouse/GetProcessLogList`,type:"post",data:{ProcessNo:r.ProcessNo},success:function(n){e.setGridOption("rowData",n)},error:function(){}})};o=function(){function i(){}class n{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){const n=this.params.api.getModel().getRowCount();this.eGui.innerHTML=`<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${n}</span>`}}var t={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},pinned:"right"},{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"Generation Date",field:"createTime",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.createTime?null:new Date(n.data.createTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Create User",field:"createUser"},{headerName:"Content of Log",field:"content",MaxWidth:900}],defaultColDef:{flex:1,filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0}},components:{buttonCellRenderer:h},statusBar:{statusPanels:[{statusPanel:n,align:"left"},]},masterDetail:!0,detailCellRendererParams:{detailGridOptions:{columnDefs:[{headerName:"详细信息",field:"content"},]},getDetailRowData:function(n){n.successCallback(n.data.content)}},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){e=n.api},onFirstDataRendered:i};new agGrid.createGrid(document.getElementById("grid"),t);f()};u.delete=function(n){myAlert.message.confirm("Are you sure you want to delete it?",function(t){t&&$.ajax({url:`/Warehouse/DeleteProcessLog`,type:"post",data:{ID:n.id},success:function(n){n.code=="200"?(myAlert.message.success("Delete successfully！"),f()):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};u.save=function(){if(!u.requestParams.Content){myAlert.message.info("Please enter the content！");return}$.ajax({url:`/Warehouse/InsertProcessLog`,type:"post",data:{ProcessNo:r.ProcessNo,TrackingNumber:r.TrackingNumber,Content:u.requestParams.Content},success:function(){u.requestParams.Content="";f();myAlert.message.success("Save successfully！");n.$apply()},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};s=function(n){var i=t.open({templateUrl:"/warehouse/LogDetail",controller:"warehouse.logDetail as vm",backdrop:"static",size:"lg",resolve:{options:function(){return n}}})};u.close=function(){i.close()}}]);angular.module("myApp").controller("IssueTracking",["$scope","$uibModal","$timeout",function(n,t){function o(n){var e=document.createElement("div"),r,t;return e.classList.add("button-container-ag"),r=document.createElement("button"),r.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',r.classList.add("btn","btn-primary","btn-sm"),r.addEventListener("click",function(){n.data.State.toUpperCase()!="Closed".toUpperCase()?u(n.data.TrackingNumber,f.Edit):u(n.data.TrackingNumber,f.View)}),e.appendChild(r),t=document.createElement("button"),t.innerHTML='<i class="bi bi-trash3"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),n.data.State&&n.data.State.toUpperCase()=="New".toUpperCase()||t.setAttribute("disabled",!0),t.addEventListener("click",function(){i.delete(n.data)}),e.appendChild(t),e}var i=this,f,e,r;n.$on("$viewContentLoaded",function(){App.initAjax()});f={Create:"Create",Edit:"Edit",View:"View"};r=function(){$.ajax({url:`/Warehouse/GetIssueTracking`,type:"post",data:i.specialFilter(),success:function(n){var t=JSON.parse(n);e.setGridOption("rowData",t.data)},error:function(n){n.responseText&&myAlert.message.error(n.responseText)}})};i.selectOptions=["Inbound damage","Damage in WHS","Missing","Wrong loading","Loading damage","Damage during transportation","steal"];i.selectCurrentProcess=["Received problem notification","Notify customers","The customer initiates a claim","Confirm the responsible party","Claimant","Compensation completed"];i.init=function(){function u(n){n.api.getToolPanelInstance("filters").expandFilters();n.api.autoSizeAllColumns()}i.initDate();$("#IssueType2").val("").selectpicker("refresh");$("#CurrentProcess2").val("").selectpicker("refresh");$("#State2").val("").selectpicker("refresh");class n{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){const n=this.params.api.getModel().getRowCount();this.eGui.innerHTML=`<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${n}</span>`}}const t={sideBar:{toolPanels:[{id:"columns",labelDefault:"Columns",labelKey:"columns",iconKey:"columns",toolPanel:"agColumnsToolPanel",suppressFiltersToolPane:!0},{id:"filters",labelDefault:"Filters",labelKey:"filters",iconKey:"filter",toolPanel:"agFiltersToolPanel"}]},columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"Tracking number",field:"TrackingNumber"},{headerName:"Tracking Date",field:"TrackingDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.TrackingDate?null:new Date(n.data.TrackingDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Status",field:"State"},{headerName:"First Report Date",field:"FirstDate",filter:"agDateColumnFilter",valueGetter:function(n){return!n.data||!n.data.FirstDate?null:new Date(n.data.FirstDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Issue Type",field:"IssueType"},{headerName:"Current Node",field:"CurrentProcess"},{headerName:"Operator",field:"Operator"},{headerName:"Remarks on files or not",field:"IsFiles"},{headerName:"Issue description",field:"Notes",minWidth:200},{headerName:"HiH insurance or not",field:"IsHIHInsurance",editable:!1,cellRenderer:function(n){return`<div class="ag-custom-checkbox">
                                     <input type="checkbox" ${n.value?"checked":""} disabled>
                                    </div>`},cellRendererParams:{suppressCheckedIcon:!0,suppressUncheckedIcon:!0}},{headerName:"Insurance or not",field:"IsInsurance",editable:!1,cellRenderer:function(n){return`<div class="ag-custom-checkbox">
                                     <input type="checkbox" ${n.value?"checked":""} disabled>
                                    </div>`},cellRendererParams:{suppressCheckedIcon:!0,suppressUncheckedIcon:!0}},{headerName:"Insurance pay or not",field:"IsInsurancePay",editable:!1,cellRenderer:function(n){return`<div class="ag-custom-checkbox">
                                     <input type="checkbox" ${n.value?"checked":""} disabled>
                                    </div>`},cellRendererParams:{suppressCheckedIcon:!0,suppressUncheckedIcon:!0}},{headerName:"Withdraw case or not",field:"IsWithdrawCase",editable:!1,cellRenderer:function(n){return`<div class="ag-custom-checkbox">
                                     <input type="checkbox" ${n.value?"checked":""} disabled>
                                    </div>`},cellRendererParams:{suppressCheckedIcon:!0,suppressUncheckedIcon:!0}},],components:{buttonCellRenderer:o},defaultColDef:{maxWidth:350,minWidth:200,filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0}},statusBar:{statusPanels:[{statusPanel:n,align:"left"},]},rowSelection:"single",enableRangeSelection:!0,suppressRowClickSelection:!0,onGridReady:function(n){e=n.api},onFirstDataRendered:u};new agGrid.createGrid(document.getElementById("myGrid"),t);r()};i.initDate=function(){var n=$("#IssueType2");$.each(i.selectOptions,function(t,i){n.append($("<option><\/option>").val(i).text(i))});$.each(i.selectCurrentProcess,function(n,t){$("#CurrentProcess2").append($("<option><\/option>").val(t).text(t))});$("#myDate").daterangepicker({autoUpdateInput:!0,startDate:moment().subtract(1,"years"),endDate:new Date,locale:{format:"YYYY-MM-DD"}});$("#FirstDate").daterangepicker({autoUpdateInput:!0,startDate:moment().subtract(1,"years"),endDate:new Date,locale:{format:"YYYY-MM-DD"}});$("#FirstDate").val("");$("#myDate").val("")};i.requestParams={trackingNumber:"",currentProcess:""};i.invMoveImport=function(){var n=t.open({templateUrl:"/WareHouse/demo",controller:"issueTrackingdemo as vm",backdrop:"static",size:"full",resolve:{}})};i.edit=function(){const n=e.getSelectedRows();var t=n[0];u(t.TrackingNumber)};i.delete=function(n){myAlert.message.confirm("Are you sure you want to delete it?",function(t){t&&$.ajax({url:`/Warehouse/DeleteIssueTracking`,type:"post",data:{TrackingNumber:n.TrackingNumber},success:function(n){n.code=="200"?(myAlert.message.success("Delete successfully！"),r()):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})})};i.add=function(){u(null)};var u=function(n,i=null){var u=t.open({templateUrl:"/WareHouse/IssueDetail",controller:"warehouse.issueDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{TrackingNumber:n,pageStatus:i}}}});u.result.then(function(){},function(){}).finally(function(){r()})},s=function(n){var i=t.open({templateUrl:"/Files/FilesManage",controller:"basic.files.filesManage as vm",backdrop:"static",size:"full",resolve:{options:function(){return{dataItem:n}}}})},h=function(n){var i=t.open({templateUrl:"/WareHouse/IssueProcess",controller:"warehouse.IssueProcess as vm",backdrop:"static",size:"full",resolve:{options:function(){return{dataItem:n}}}})};i.save=function(){commonUtil.message("保存成功！","success")};i.specialFilter=function(){var n={},t,r;return n.TrackingNumber=i.requestParams.trackingNumber,n.LotNo=i.requestParams.lotno,n.CurrentProcess=$("#CurrentProcess2").val(),n.IssueType=$("#IssueType2").val(),n.State=$("#State2").val(),n.IsFiles=$("#IsFiles2").val(),$("#myDate").val()!=""&&(t=$("#myDate").data("daterangepicker")),$("#FirstDate").val()!=""&&(r=$("#FirstDate").data("daterangepicker")),n.StartDate=t==undefined?null:t.startDate.format("YYYY-MM-DD"),n.EndDate=t==undefined?null:t.endDate.format("YYYY-MM-DD"),n.FirstStartDate=r==undefined?null:r.startDate.format("YYYY-MM-DD"),n.FirstEndDate=r==undefined?null:r.endDate.format("YYYY-MM-DD"),n};$('input[id="myDate"]').on("cancel.daterangepicker",function(){$("#myDate").val("")});$('input[id="FirstDate"]').on("cancel.daterangepicker",function(){$("#FirstDate").val("")});i.search=function(){r()};i.resetting=function(){i.requestParams={};$("#myDate").val("");$("#FirstDate").val("");$("#IssueType2").val("").selectpicker("refresh");$("#CurrentProcess2").val("").selectpicker("refresh");$("#State2").val("").selectpicker("refresh");$("#IsFiles2").val("").selectpicker("refresh");r()}}]);angular.module("myApp").controller("warehouse.logDetail",["$scope","$uibModalInstance","options","$timeout",function(n,t,i){var r=this;r.dismiss=function(){t.dismiss("cancel")};r.requestParams={};r.init=function(){r.requestParams.Content=i}}]);angular.module("myApp").controller("warehouse.processCreate",["$scope","$uibModal","$uibModalInstance","options","$timeout",function(n,t,i,r,u){var f=this,e={Create:"Create",Edit:"Edit",View:"View"},o;f.isCreate=r.pageStatus==e.Create?!0:!1;f.isEdit=r.pageStatus==e.Edit?!0:!1;f.isView=r.pageStatus==e.View?!0:!1;f.requestParams={TrackingNumber:r.TrackingNumber,executor:""};f.title="Process Action";f.isCreate&&(f.title="Process Create");f.isEdit&&(f.title="Process Edit");f.isView&&(f.title="Process View");f.init=function(){$("#scheduleDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#deadline").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#status").selectpicker();u(function(){o()},0)};o=function(){r.ProcessNo!=null?$.ajax({url:`/Warehouse/GetIssueProcessDetail`,type:"post",data:{ProcessNo:r.ProcessNo},success:function(n){f.requestParams=n;f.requestParams.createTime=moment(n.createTime).format("YYYY-MM-DD");$("#scheduleDate").datepicker("setDate",f.requestParams.scheduleDate);$("#deadline").datepicker("setDate",f.requestParams.deadline);$("#status").val(n.status).trigger("change")},error:function(n,t,i){console.error("Error fetching data:",t,i)}}):($.post("/UserSession/GetUserAuth",function(t){f.requestParams.executor=t.name;n.$digest()}),$("#status").val("New").trigger("change"))};f.isbusy=!1;f.save=function(){f.isbusy=!0;f.requestParams.status=$("#status").val();$.ajax({url:`/Warehouse/IssueProcessCreateOrEdit`,type:"post",data:f.requestParams,success:function(t){t.code==200?(commonUtil.message("Successfully saved！","success"),i.close()):(commonUtil.message(t.msg,"danger"),f.isbusy=!1,n.$apply())},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};f.cancel=function(){i.dismiss("cancel")}}]);angular.module("myApp").controller("wareHouse.selectDetail",["$scope","$uibModalInstance","options","$uibModal",function(n,t,i,r){var u=this,f,e,o;u.cancel=function(){t.dismiss("cancel")};u.init=function(){o()};u.requestParams={GoodsNo:""};u.selectedItems=u.selectedItems||[];u.search=function(){const n=f.getSelectedRows();n.forEach(n=>{const t=u.selectedItems.some(t=>t.GoodsNo===n.GoodsNo);t||u.selectedItems.push({GoodsNo:n.GoodsNo,Id:n.Id,Qty:n.Qty})});e()};u.save=function(){const n=f.getSelectedRows();n.forEach(n=>{const t=u.selectedItems.some(t=>t.GoodsNo===n.GoodsNo);t||u.selectedItems.push({GoodsNo:n.GoodsNo,Id:n.Id,Qty:n.Qty})});$.ajax({url:`/Warehouse/InsertIssueDetail`,type:"post",contentType:"application/json",data:JSON.stringify({Items:JSON.stringify(u.selectedItems),KHDH:"TSW",TrackingNumber:i.TrackingNumber}),success:function(){commonUtil.message("保存成功！","success");t.close()},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};u.specialFilter=function(){var n={};return n.LotNo=u.requestParams.LotNo,n};e=function(){$.ajax({url:`/Warehouse/GetSelectIssueDetailList`,type:"post",contentType:"application/json",data:JSON.stringify(u.specialFilter()),success:function(n){var t=JSON.parse(n);f.setGridOption("rowData",t.data)},error:function(n,t,i){console.error("Error fetching data:",t,i)}})};u.openSplitOrder=function(){const n=f.getSelectedRows();if(n.length!=1){myAlert.message.warn("please selece one row！");return}console.log(n[0]);var t={inventoryqQty:n[0].Qty,power:n[0].Power,lotNo:n[0].LotNo,goodsNo:n[0].GoodsNo,PiNo:n[0].PiNo,qty1:n[0].PalletsQty,totalPower:n[0].TotalPower,singleTonQty:n[0].QTYPerPallet},i=r.open({templateUrl:"/OrdersOverView/SplitOrder",controller:"orderManagement.ordersOverView.splitOrder as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{rowData:t}}}});i.result.then(function(){},function(){}).finally(function(){e()})};o=function(){function t(n){n.api.autoSizeAllColumns()}var n={sideBar:{toolPanels:[{id:"columns",labelDefault:"Columns",labelKey:"columns",iconKey:"columns",toolPanel:"agColumnsToolPanel",suppressFiltersToolPane:!0},{id:"filters",labelDefault:"Filters",labelKey:"filters",iconKey:"filter",toolPanel:"agFiltersToolPanel"}]},columnDefs:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"HiH #",field:"GoodsNo"},{headerName:"WHS",field:"WHS"},{headerName:"LotNo",field:"LotNo"},{headerName:"Type",field:"Type"},{headerName:"Power",field:"Power"},{headerName:"Qty(pcs)",field:"Qty"},{headerName:"Inbound Date",field:"InBoundDate",valueGetter:function(n){return!n.data||!n.data.InBoundDate?null:new Date(n.data.InBoundDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"PI Number",field:"PiNo"},{headerName:"Released Date",field:"ReleasedDate",valueGetter:function(n){return!n.data||!n.data.ReleasedDate?null:new Date(n.data.ReleasedDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Consignee",field:"Consignee"},{headerName:"Incoterms",field:"Incoterms"},{headerName:"Outbound Date",field:"OutboundDate",valueGetter:function(n){return!n.data||!n.data.OutboundDate?null:new Date(n.data.OutboundDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Carrier",field:"Carrier"},],defaultColDef:{filter:"agSetColumnFilter",filterParams:{suppressSorting:!0}},dataTypeDefinitions:{object:{baseDataType:"object",extendsDataType:"object",valueParser:n=>({name:n.newValue}),valueFormatter:n=>n.value==null?"":n.value.name}},rowSelection:"multiple",suppressRowClickSelection:!0,onGridReady:function(n){f=n.api},onFirstDataRendered:t};new agGrid.createGrid(document.getElementById("gridDetail2"),n);e()}}]);angular.module("myApp").controller("wareHouse.splitOrder",["$scope","$uibModalInstance","options","$http",function(n,t,i,r){var u=this,e,f;u.cancel=function(){t.dismiss("cancel")};u.requestParams={SplitGoodsNo:"",RemainingQty:0,RemainingTotalPower:0,SplitTotalPower:0,SplitPalletsQty:0,RemainingPalletsQty:0};u.init=function(){u.requestParams.Qty=i.rowData.Qty;u.requestParams.Power=i.rowData.Power;u.requestParams.LotNo=i.rowData.LotNo;u.requestParams.GoodsNo=i.rowData.GoodsNo;u.requestParams.PiNo=i.rowData.PiNo;u.requestParams.PalletsQty=i.rowData.PalletsQty;u.requestParams.TotalPower=i.rowData.TotalPower;u.requestParams.QTYPerPallet=i.rowData.QTYPerPallet};u.search=function(){f()};u.updateRemainingQty=function(){u.requestParams.SplitTotalPower=u.requestParams.SplitQty*u.requestParams.Power;u.requestParams.SplitPalletsQty=u.requestParams.QTYPerPallet==0?0:Number(parseFloat(u.requestParams.SplitQty/u.requestParams.QTYPerPallet).toFixed(2));u.requestParams.RemainingQty=u.requestParams.Qty-u.requestParams.SplitQty;u.requestParams.RemainingPalletsQty=parseFloat(u.requestParams.RemainingQty/u.requestParams.QTYPerPallet).toFixed(2);u.requestParams.RemainingTotalPower=u.requestParams.RemainingQty*u.requestParams.Power};u.isBusy=!1;u.save=function(){if(!(u.requestParams.RemainingQty>0)||u.requestParams.SplitQty<=0||!u.requestParams.SplitQty||u.requestParams.SplitPalletsQty<0||u.requestParams.RemainingPalletsQty<0){myAlert.message.error("Please enter the correct disassembly quantity!");return}if(u.requestParams.SplitGoodsNo){myAlert.message.error("It has been successfully disassembled, please do not repeat the operation!");return}u.isBusy=!0;myAlert.message.confirm("Are you sure you want to Split 【"+u.requestParams.GoodsNo+"】?",function(n){n&&r({method:"POST",url:"/Warehouse/InsertSplitOrder",data:$.param(u.requestParams),headers:{"Content-Type":"application/x-www-form-urlencoded"}}).then(function(n){n.data.code===200?(u.requestParams.SplitGoodsNo=n.data.msg,myAlert.message.success("Split order completed！")):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1})})};u.specialFilter=function(){var n={};return n.LotNo=u.requestParams.LotNo,n};f=function(){$.ajax({url:`/Warehouse/GetSelectIssueDetailList`,type:"post",contentType:"application/json",data:JSON.stringify(u.specialFilter()),success:function(n){var t=JSON.parse(n);e.setGridOption("rowData",t.data)},error:function(n,t,i){console.error("Error fetching data:",t,i)}})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.editDispatch",["$scope","$uibModalInstance","options","$http","options",function(n,t,i,r,i){var u=this,f;u.cancel=function(){t.dismiss("cancel")};u.requestParams={orderNo:""};u.isCompleted=!1;u.init=function(){u.requestParams.orderNo=i.orderNo;u.isCompleted=i.isCompleted;u.dispatchType="direct";u.warehouseList=[];u.selectedWarehouse=null;u.loadWarehouseData();u.initGrid();n.$watch("vm.dispatchType",function(n){n==="direct"&&(u.selectedWarehouse=null)})};u.loadWarehouseData=function(){r({method:"POST",url:"/OrdersOverView/GetWarehouseData"}).then(function(n){n.data&&n.data.data?u.warehouseList=n.data.data:console.error("Error loading warehouse data")}).catch(function(n){console.error("Error fetching warehouse data:",n)})};u.isBusy=!1;u.initData=function(){r({method:"POST",url:"/OrdersOverView/GetGoodsDetail",data:{orderNo:i.orderNo}}).then(function(n){n.data.code===200?f.updateGridOptions({rowData:n.data.data}):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1})};u.initGrid=function(){const n={columnDefs:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"Container No.",field:"containerNo",width:"190"},{headerName:"Type",field:"type",width:"135"},{headerName:"Power",field:"power",width:"115"},{headerName:"Qty",field:"qty",width:"105"},{headerName:"Part No.",field:"partNo",width:"135"},{headerName:"Total Vol",field:"totalVol",width:"135"},{headerName:"Pallet Count",field:"palletCount",width:"158"},{headerName:"GW",field:"gw",width:"115"},{headerName:"EMA",field:"ema",width:"115"},{headerName:"Packet",field:"packet",width:"115"},{headerName:"Dispatch To",field:"dispatchTo",width:"360"},],defaultColDef:{filter:"agMultiColumnFilter",filterParams:{suppressSorting:!0}},rowData:[],rowSelection:"multiple",enableRangeSelection:!0,suppressRowClickSelection:!0,onGridReady:function(n){f=n.api}};new agGrid.createGrid(document.getElementById("agGrid"),n);u.initData()};u.dispatch=function(){var n,t,e;if(u.dispatchType==="warehouse"&&!u.selectedWarehouse){myAlert.message.error("Please select a warehouse");return}if(n=f.getSelectedRows(),!n||n.length===0){myAlert.message.error("Please select at least one item to dispatch");return}t=n.map(function(n){return n.id}).join(",");e={orderNo:i.orderNo,type:u.dispatchType,items:t,warehouseName:u.dispatchType==="warehouse"?u.selectedWarehouse:null};myAlert.message.confirm("Are you sure you want to dispatch "+n.length+" selected item(s)?",function(n){n&&r({method:"POST",url:"/OrdersOverView/DispatchGoods",data:e}).then(function(n){n.data.code===200?(myAlert.message.success("Dispatch successful"),u.initData()):myAlert.message.error(n.data.msg)}).catch(function(n){u.isBusy=!1;console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1})})};u.clearDispatch=function(){var n,t,e;if(u.dispatchType==="warehouse"&&!u.selectedWarehouse){myAlert.message.error("Please select a warehouse");return}if(n=f.getSelectedRows(),!n||n.length===0){myAlert.message.error("Please select at least one item to dispatch");return}t=n.map(function(n){return n.id}).join(",");e={orderNo:i.orderNo,type:"clear",items:t};myAlert.message.confirm("Are you sure you want to Clear "+n.length+" selected item(s)?",function(n){n&&r({method:"POST",url:"/OrdersOverView/DispatchGoods",data:e}).then(function(n){n.data.code===200?(myAlert.message.success("Dispatch successful"),u.initData()):myAlert.message.error(n.data.msg)}).catch(function(n){u.isBusy=!1;console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1})})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.editOrder",["$scope","$uibModalInstance","options","$http","options",function(n,t,i,r,i){var u=this;u.cancel=function(){t.dismiss("cancel")};u.isCompleted=!1;u.requestParams={orderNo:""};u.init=function(){u.isCompleted=i.isCompleted;$("#cargoNotificationReceivedTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#receivedCustomsDocumentsTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#sentCustomsDocumentsTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#carrierReleaseTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#customsClearanceCompletionTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#rev7501").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#doResceivedTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#doSendingTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#confirmedDOSendingTime").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});u.requestParams.orderNo=i.orderNo;u.initData()};u.isBusy=!1;u.save=function(){myAlert.message.confirm("Are you sure you want to modify the data for Order No. 【"+i.orderNo+"】?",function(n){n&&(u.isBusy=!0,r({method:"POST",url:"/OrdersOverView/EditOrderSubDetail",data:{orderNo:i.orderNo,cargoNotificationReceivedTime:u.requestParams.cargoNotificationReceivedTime,receivedCustomsDocumentsTime:u.requestParams.receivedCustomsDocumentsTime,sentCustomsDocumentsTime:u.requestParams.sentCustomsDocumentsTime,cvdCaseNo:u.requestParams.cvdCaseNo,carrierReleaseTime:u.requestParams.carrierReleaseTime,rev7501:u.requestParams.rev7501,doResceivedTime:u.requestParams.doResceivedTime,doSendingTime:u.requestParams.doSendingTime,ewRef:u.requestParams.ewRef,confirmedDOSendingTime:u.requestParams.confirmedDOSendingTime,customsClearanceCompletionTime:u.requestParams.customsClearanceCompletionTime}}).then(function(n){n.data.code===200?(u.requestParams.SplitGoodsNo=n.data.msg,myAlert.message.success("Split order completed！")):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1}))})};u.initData=function(){r({method:"POST",url:"/OrdersOverView/GetOrderEditDetail",data:{orderNo:i.orderNo}}).then(function(n){n.data.code===200?(u.requestParams=n.data.data,u.requestParams.cargoNotificationReceivedTime&&$("#cargoNotificationReceivedTime").datepicker("setDate",new Date(u.requestParams.cargoNotificationReceivedTime)),u.requestParams.receivedCustomsDocumentsTime&&$("#receivedCustomsDocumentsTime").datepicker("setDate",new Date(u.requestParams.receivedCustomsDocumentsTime)),u.requestParams.sentCustomsDocumentsTime&&$("#sentCustomsDocumentsTime").datepicker("setDate",new Date(u.requestParams.sentCustomsDocumentsTime)),u.requestParams.carrierReleaseTime&&$("#carrierReleaseTime").datepicker("setDate",new Date(u.requestParams.carrierReleaseTime)),u.requestParams.customsClearanceCompletionTime&&$("#customsClearanceCompletionTime").datepicker("setDate",new Date(u.requestParams.customsClearanceCompletionTime)),u.requestParams.rev7501&&$("#rev7501").datepicker("setDate",new Date(u.requestParams.rev7501)),u.requestParams.doResceivedTime&&$("#doResceivedTime").datepicker("setDate",new Date(u.requestParams.doResceivedTime)),u.requestParams.doSendingTime&&$("#doSendingTime").datepicker("setDate",new Date(u.requestParams.doSendingTime)),u.requestParams.confirmedDOSendingTime&&$("#confirmedDOSendingTime").datepicker("setDate",new Date(u.requestParams.confirmedDOSendingTime))):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.invboundDetail",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){function h(n){var i=document.createElement("div"),t;return e.isComplete||e.isAudited?i:(i.classList.add("button-container-ag"),t=document.createElement("button"),t.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),n.data.status!="入库通知未处理"&&n.data.status!="库存_已入库"&&n.data.status!="入库通知未处理_出库通知锁定"&&(t.disabled=!0),t.addEventListener("click",function(){(n.data.status=="入库通知未处理"||n.data.status=="库存_已入库"||n.data.status=="入库通知未处理_出库通知锁定")&&(n.data.status=="库存_已入库"&&(s+=n.data.goodsNo+","),o.applyTransaction({remove:[n.data]}))}),i.appendChild(t),i)}var e=this,o,s;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.saving=!1;e.specialFilter=function(){return{}};s="";e.isComplete=f.isComplete;e.isAudited=f.isAudited;e.init=function(){$("#entryDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});r(function(){e.requestParams.orderID=f.invboundNo;f.entryDate?$("#entryDate").datepicker("setDate",new Date(f.entryDate)):$("#entryDate").datepicker("setDate",new Date);e.initData();new agGrid.createGrid(document.getElementById("agGird"),e.gridOptions)},0)};e.gridOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},hide:e.isComplete||e.isAudited,suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"goodsNo"},{headerName:"Warehouse Code",field:"warehouseAgent"},{headerName:"Location",field:"warehouseLocation"},{headerName:"GPI No.",field:"customerCode"},{headerName:"Description",field:"goodsName"},{headerName:"Type",field:"goodsSpec"},{headerName:"Lot No.",field:"lotNo"},{headerName:"Power",field:"power"},{headerName:"Qty",field:"inventoryqQty"},{headerName:"Package",field:"packing"},{headerName:"Pallets",field:"qty1"},{headerName:"Total Vol",field:"totalPower"},{headerName:"QTY Per Pallet",field:"singleTonQty"},{headerName:"BL No.",field:"billNo"},{headerName:"Container No.",field:"boxNo"},{headerName:"Index No.",field:"indexNo"},{headerName:"Class",field:"goodsClass"},{headerName:"Storage location",field:"location"}],defaultColDef:{filter:!1},components:{buttonCellRenderer:h},rowClassRules:{"color-new":function(n){if(n.data.status=="入库通知未处理"||n.data.status=="入库通知未处理_出库通知锁定")return!0},"color-release":function(n){if(n.data.status!="库存_已入库"&&n.data.status!="入库通知未处理_出库通知锁定")return!0}},pagination:!0,rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){o=n.api}};e.initData=function(){u({method:"POST",url:"/OrdersOverView/GetInvDetail",data:{invNo:e.requestParams.orderID}}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns()}).catch(function(n){console.log(n)})};e.search=function(){};e.dismiss=function(){t.dismiss()};e.addInvBoundOrder=function(){var n="",t;o.forEachNode(function(t){t.data.status=="入库通知未处理"&&(n+=t.data.goodsNo+",")});t=i.open({templateUrl:"/OrdersOverView/InvLoadData",controller:"orderManagement.ordersOverView.invLoadData as vm",backdrop:"static",size:"full",resolve:{options:function(){return{ids:n,type:"Invbound",orderNo:f.orderNo}}}});t.result.then(function(n){o.applyTransaction({add:n})},function(){})};e.save=function(){var n,t;if(e.saving=!0,!e.requestParams.entryDate){myAlert.message.error("Please select the date");e.saving=!1;return}n="";o.forEachNode(function(t){(t.data.status=="入库通知未处理"||t.data.status=="入库通知未处理_出库通知锁定")&&(n+=t.data.goodsNo+",")});t=e.requestParams.entryDate;u({method:"POST",url:"/OrdersOverView/CreateOrEdidInvBound",data:{ids:n,entryDate:t,invNo:e.requestParams.orderID,orderNo:f.orderNo,delIds:s}}).then(function(n){s="";n.data.code==200?(e.requestParams.orderID||(e.requestParams.orderID=n.data.data),myAlert.message.success("Successfully saved"),e.saving=!1):(myAlert.message.error(n.data.msg),e.saving=!1);e.initData()}).catch(function(n){console.log(n);e.saving=!1})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.invLoadData",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){function l(){var t=document.createElement("div"),n;return t.classList.add("button-container-ag"),n=document.createElement("button"),n.innerHTML='<i class="bi bi-pencil-fill"><\/i> delete',n.classList.add("btn","btn-primary","btn-sm"),n.addEventListener("click",function(){}),t.appendChild(n),t}var e=this,o,s,c,h;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.warehouse=[];e.saving=!1;u({method:"POST",url:"/OrdersOverView/GetWarehouseData",data:{}}).then(function(n){n.data.code===200?e.warehouse=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});e.specialFilter=function(){var n={},t=f.ids;return s&&s.forEachNode(function(n){t+=n.data.goodsNo+","}),n.orderNo=f.orderNo,n.ids=t,n.warehouse=e.requestParams.warehouseCode,n.lotNo=e.requestParams.lotNo,n.containerNo=e.requestParams.containerNo,n};e.init=function(){r(function(){new agGrid.createGrid(document.getElementById("agGird"),e.gridOptions);new agGrid.createGrid(document.getElementById("agGird2"),e.gridOptions2);e.initData()},0)};c=[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"HiH #",field:"goodsNo"},{headerName:"WHS",field:"warehouseLocation"},{headerName:"Container No.",field:"boxNo"},{headerName:"SKU No.",field:"skuno"},{headerName:"LotNo",field:"lotNo"},{headerName:"Type",field:"goodsSpec"},{headerName:"Power",field:"power"},{headerName:"Qty(pcs)",field:"inventoryqQty"},{headerName:"Package",field:"package"},{headerName:"Pallets",field:"qty1"},{headerName:"QTY Per Pallet",field:"singleTonQty"},{headerName:"Inbound Notice Date",field:"inNoticeDate",valueGetter:function(n){return!n.data||!n.data.inNoticeDate?null:new Date(n.data.inNoticeDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Outbound Notification No.",field:"outboundNoticeNo",hide:f.type!="Outbound"},{headerName:"Expected delivery date",field:"expOutDate",hide:f.type!="Outbound",valueGetter:function(n){return!n.data||!n.data.expOutDate?null:new Date(n.data.expOutDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},];e.openSplitOrder=function(){const n=o.getSelectedRows();if(n.length!=1){myAlert.message.warn("please selece one row！");return}var t=n[0],r=i.open({templateUrl:"/OrdersOverView/SplitOrder",controller:"orderManagement.ordersOverView.splitOrder as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{rowData:t}}}});r.result.then(function(){},function(){}).finally(function(){e.initData()})};e.gridOptions={columnDefs:c,defaultColDef:{headerHidden:!0,filter:!1},pagination:!0,components:{buttonCellRenderer:l},rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){o=n.api},rowSelection:"multiple"};e.gridOptions2={columnDefs:c,pagination:!0,defaultColDef:{filter:!1},components:{buttonCellRenderer:l},rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){s=n.api},rowSelection:"multiple"};e.initData=function(){u({method:"POST",url:"/OrdersOverView/GetInvbountNoticeDetail",data:e.specialFilter()}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns();s.autoSizeAllColumns()}).catch(function(n){console.log(n)})};h=[];e.selectRow=function(){for(var t=o.getSelectedRows(),i=[],n=0;n<t.length;n++)i.push(t[n]),h.push(t[n]);s.applyTransaction({add:i});o.applyTransaction({remove:i})};e.cancelRow=function(){for(var r,t=s.getSelectedRows(),i=[],n=0;n<t.length;n++)i.push(t[n]),r=h.indexOf(t[n]),r>-1&&h.splice(r,1);o.applyTransaction({add:i});s.applyTransaction({remove:i})};e.search=function(){e.initData()};e.dismiss=function(){t.dismiss()};e.save=function(){t.close(h)}}]);angular.module("myApp").controller("orderManagement.ordersOverView.invNoticeDetail",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){var e=this,o,s;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.saving=!1;e.specialFilter=function(){return{}};e.isComplete=f.isComplete;e.init=function(){$("#entryDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#expInvDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});r(function(){e.requestParams.orderID=f.invNoticeNo;f.entryDate?$("#entryDate").datepicker("setDate",new Date(f.entryDate)):$("#entryDate").datepicker("setDate",new Date);f.expInvDate?$("#expInvDate").datepicker("setDate",new Date(f.expInvDate)):$("#expInvDate").datepicker("setDate",new Date);e.initData();new agGrid.createGrid(document.getElementById("agGird"),e.gridOptions)},0)};s="";e.gridOptions={columnDefs:[{headerName:"HiH #",field:"goodsNo"},{headerName:"Warehouse Code",field:"warehouseAgent"},{headerName:"Location",field:"warehouseLocation"},{headerName:"GPI No.",field:"customerCode"},{headerName:"Description",field:"goodsName"},{headerName:"Type",field:"goodsSpec"},{headerName:"Lot No.",field:"lotNo"},{headerName:"Power",field:"power"},{headerName:"Qty",field:"inventoryqQty"},{headerName:"Package",field:"packing"},{headerName:"Pallets",field:"qty1"},{headerName:"Total Vol",field:"totalPower"},{headerName:"QTY Per Pallet",field:"singleTonQty"},{headerName:"BL No.",field:"billNo"},{headerName:"Container No.",field:"boxNo"},{headerName:"Index No.",field:"indexNo"},{headerName:"Class",field:"goodsClass"},],defaultColDef:{filter:!1},rowClassRules:{"color-release":function(n){if(n.data.status!="入库通知未处理")return!0}},rowData:[],pagination:!0,suppressRowClickSelection:!0,onGridReady:function(n){o=n.api}};e.initData=function(){u({method:"POST",url:"/OrdersOverView/GetInvNoticeDetail",data:{invNoticeNo:e.requestParams.orderID}}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns()}).catch(function(n){console.log(n)})};e.rowData=[];e.search=function(){};e.dismiss=function(){t.dismiss()};e.save=function(){e.saving=!0;u({method:"POST",url:"/OrdersOverView/ImportInventory",data:{items:JSON.stringify(e.rowData),orderNo:f.orderNo,expInvDate:e.requestParams.expInvDate}}).then(function(n){n.data.code==200?(myAlert.message.success("Successfully saved"),t.dismiss()):(e.saving=!1,myAlert.message.error(n.data.msg))}).catch(function(n){e.saving=!1;console.log(n)})};e.triggerFileInput=function(){const n=document.getElementById("fileInput");n&&n.click()};r(function(){e.fileInput=document.getElementById("fileInput");fileInput&&e.fileInput.addEventListener("change",function(n){var t=n.target.files[0],i;t?(e.saving=!0,console.log("File selected2:",t),i=new FormData,i.append("FormFile",t),u.post("/OrdersOverView/GetDataByExcel",i,{headers:{"Content-Type":undefined},transformRequest:angular.identity}).then(function(n){n.data.code==200?(e.rowData=n.data.data,o.updateGridOptions({rowData:n.data.data}),o.autoSizeAllColumns()):myAlert.message.error(n.data.msg);e.saving=!1;fileInput.value=""}).catch(function(n){alert("上传失败: "+(n.data||n.statusText));e.saving=!1})):console.error("No file selected")})},0)}]);angular.module("myApp").controller("orderManagement.ordersOverView.loadData",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){function l(){var t=document.createElement("div"),n;return t.classList.add("button-container-ag"),n=document.createElement("button"),n.innerHTML='<i class="bi bi-pencil-fill"><\/i> delete',n.classList.add("btn","btn-primary","btn-sm"),n.addEventListener("click",function(){}),t.appendChild(n),t}var e=this,o,s,c,h;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.saving=!1;e.warehouse=[];u({method:"POST",url:"/OrdersOverView/GetWarehouseData",data:{}}).then(function(n){n.data.code===200?e.warehouse=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});e.specialFilter=function(){var n={},t=f.ids;return s&&s.forEachNode(function(n){n.data.status=="库存_已入库"&&(t+=n.data.goodsNo+",")}),n.orderNo=f.orderNo,n.ids=t,n.warehouse=e.requestParams.warehouseCode,n.lotNo=e.requestParams.lotNo,n.containerNo=e.requestParams.containerNo,n};e.init=function(){r(function(){new agGrid.createGrid(document.getElementById("agGird"),e.gridOptions);new agGrid.createGrid(document.getElementById("agGird2"),e.gridOptions2);e.initData()},0)};c=[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"HiH #",field:"goodsNo"},{headerName:"WHS",field:"warehouseLocation"},{headerName:"Container No.",field:"boxNo"},{headerName:"SKU No.",field:"skuno"},{headerName:"LotNo",field:"lotNo"},{headerName:"Type",field:"goodsSpec"},{headerName:"Power",field:"power"},{headerName:"Qty(pcs)",field:"inventoryqQty"},{headerName:"Package",field:"package"},{headerName:"Pallets",field:"qty1"},{headerName:"QTY Per Pallet",field:"singleTonQty"},{headerName:"Inbound Date",field:"inStockDate",valueGetter:function(n){return!n.data||!n.data.inStockDate?null:new Date(n.data.inStockDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Outbound Notification No.",field:"outboundNoticeNo",hide:f.type!="Outbound"},{headerName:"Expected delivery date",field:"expOutDate",hide:f.type!="Outbound",valueGetter:function(n){return!n.data||!n.data.expOutDate?null:new Date(n.data.expOutDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},];e.openSplitOrder=function(){const n=o.getSelectedRows();if(n.length!=1){myAlert.message.warn("please selece one row！");return}var t=n[0],r=i.open({templateUrl:"/OrdersOverView/SplitOrder",controller:"orderManagement.ordersOverView.splitOrder as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{rowData:t}}}});r.result.then(function(){},function(){}).finally(function(){e.initData()})};e.gridOptions={columnDefs:c,defaultColDef:{filter:!1},pagination:!0,components:{buttonCellRenderer:l},rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){o=n.api},rowSelection:"multiple"};e.gridOptions2={columnDefs:c,pagination:!0,defaultColDef:{filter:!1},components:{buttonCellRenderer:l},rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){s=n.api},rowSelection:"multiple"};e.initData=function(){f.type=="Outbound"?u({method:"POST",url:"/OrdersOverView/GetOutbountNoticeDetail",data:e.specialFilter()}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns();s.autoSizeAllColumns()}).catch(function(n){console.log(n)}):u({method:"POST",url:"/OrdersOverView/GetInventoryData",data:e.specialFilter()}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns();s.autoSizeAllColumns()}).catch(function(n){console.log(n)})};h=[];e.selectRow=function(){for(var t=o.getSelectedRows(),i=[],n=0;n<t.length;n++)i.push(t[n]),h.push(t[n]);s.applyTransaction({add:i});o.applyTransaction({remove:i})};e.cancelRow=function(){for(var r,t=s.getSelectedRows(),i=[],n=0;n<t.length;n++)i.push(t[n]),r=h.indexOf(t[n]),r>-1&&h.splice(r,1);o.applyTransaction({add:i});s.applyTransaction({remove:i})};e.search=function(){e.initData()};e.dismiss=function(){t.dismiss()};e.save=function(){t.close(h)}}]);angular.module("myApp").controller("orderManagement.ordersOverView.orderDetail",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){var e=this,o,s;n.$on("$viewContentLoaded",function(){App.initAjax()});e.state="已审核";e.lotNoStr="";e.isTSW=!1;e.requestParams={};e.isAudit=!1;e.isWarehouse=!1;e.isSUNOVAInv=!1;e.isSUNOVAOut=!1;e.specialFilter=function(){return{}};e.init=function(){e.initData();$("#date").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"})};e.gridOptions={columnDefs:[{headerName:"fieldCategory",field:"fieldCategory",rowGroup:!0,hide:!0},{headerName:"name",field:"defaultColumnName",width:300},{headerName:"value",field:"value",flex:1}],defaultColDef:{filter:!1},rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){o=n.api}};e.initData=function(){u({method:"POST",url:"/OrdersOverView/GetOrderInfo",data:{orderNo:f.orderNo}}).then(function(n){e.requestParams=n.data.data;e.requestParams.auditTime&&(e.isAudit=!0);(e.requestParams.orderType=="Outbound delivery"||e.requestParams.orderType=="Inbound")&&(e.isWarehouse=!0);e.isCompleted=e.requestParams.isComplete;e.requestParams.csCode=="TSW"&&e.requestParams.orderType=="Outbound delivery"&&(e.lotNoStr="Outbound details include the split LOTNO:"+e.requestParams.lotNoSplitExplanation);e.requestParams.csCode=="SUNOVA"&&(e.requestParams.orderType=="Inbound"&&(e.isSUNOVAInv=!0),e.requestParams.orderType=="Outbound delivery"&&(e.isSUNOVAOut=!0))}).catch(function(n){console.log(n)});u({method:"POST",url:"/OrdersOverView/GetOrderDetail",data:{orderNo:f.orderNo}}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.expandAll()}).catch(function(n){console.log(n)});new agGrid.createGrid(document.getElementById("agGrid"),e.gridOptions)};s=[];e.openFiles=function(){var n=i.open({templateUrl:"/Files/FilesManage",controller:"basic.files.filesManage as vm",backdrop:"static",size:"full",keyboard:!1,resolve:{options:function(){return{bucketName:"hih-owms",prefix:"业务/"+f.orderNo+"/",isUpLoad:!e.isCompleted,isDownLoad:!0,isReName:!e.isCompleted,isShare:!1,isDelete:!e.isCompleted,isNewDir:!e.isCompleted}}}});n.result.then(function(){},function(){}).finally(function(){fetchDataProcess()})};e.openEdit=function(){var n=i.open({templateUrl:"/OrdersOverView/EditOrder",controller:"orderManagement.ordersOverView.editOrder as vm",backdrop:"static",size:"md",resolve:{options:function(){return{orderNo:f.orderNo,isCompleted:e.isCompleted}}}});n.result.then(function(){},function(){}).finally(function(){})};e.openDispatch=function(){var n=i.open({templateUrl:"/OrdersOverView/EditDispatch",controller:"orderManagement.ordersOverView.editDispatch as vm",backdrop:"static",size:"full",resolve:{options:function(){return{orderNo:f.orderNo,isCompleted:e.isCompleted}}}});n.result.then(function(){},function(){}).finally(function(){})};e.warehouseView=function(){var n=i.open({templateUrl:"/OrdersOverView/OrderWarehouse",controller:"orderManagement.ordersOverView.orderWarehouse as vm",backdrop:"static",size:"full",resolve:{options:function(){return{dataItem:f}}}});n.result.then(function(){},function(){}).finally(function(){})};e.inBoundEmail=function(){if(!e.requestParams.date){myAlert.message.info("Please select a date.");return}myAlert.message.confirm("Are you sure you want to send the email?",function(n){n&&u({method:"POST",url:"/OrdersOverView/CreateInboundEmail",data:{orderNo:e.requestParams.orderNo,date:e.requestParams.date}}).then(function(n){n.data.code===200?myAlert.message.success(n.data.msg):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})};e.outboundEmail=function(){if(!e.requestParams.date){myAlert.message.info("Please select a date.");return}myAlert.message.confirm("Are you sure you want to send the email?",function(n){n&&u({method:"POST",url:"/OrdersOverView/CreateOutboundEmail",data:{orderNo:e.requestParams.orderNo,date:e.requestParams.date}}).then(function(n){n.data.code===200?myAlert.message.success(n.data.msg):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})};e.search=function(){};e.dismiss=function(){t.dismiss()}}]);angular.module("myApp").controller("orderManagement.ordersOverView.ordersOverView",["$scope","$uibModal","$timeout","$http",function(n,t,i,r){function h(n){var u=document.createElement("div"),i,r;return u.classList.add("button-container-ag"),i=document.createElement("button"),i.innerHTML='<i class="fa fa-eye"><\/i> View',i.classList.add("btn","btn-primary","btn-sm"),i.addEventListener("click",function(){var i=n.data,r=t.open({templateUrl:"/OrdersOverView/OrderDetail",controller:"orderManagement.ordersOverView.orderDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{orderNo:i.orderNo}}}});r.result.then(function(){},function(){}).finally(function(){})}),u.appendChild(i),(n.data.orderType=="Outbound delivery"||n.data.orderType=="Inbound")&&(r=document.createElement("button"),r.innerHTML='<i class="bi bi-pencil-fill"><\/i> Warehouse',r.classList.add("btn","btn-primary","btn-sm"),r.addEventListener("click",function(){var i=n.data,r=t.open({templateUrl:"/OrdersOverView/OrderWarehouse",controller:"orderManagement.ordersOverView.orderWarehouse as vm",backdrop:"static",size:"full",resolve:{options:function(){return{dataItem:i}}}});r.result.then(function(){},function(){}).finally(function(){})}),u.appendChild(r)),u}var u=this,f;n.$on("$viewContentLoaded",function(){App.initAjax()});u.isTSW=!1;u.requestParams={type:"",pallets:0,quantity:0,record:0,orderStyle:null,isNotCompleted:!0};u.isXZCK=!1;u.isNewCreateOrder=!1;u.isNewCreateInboundOrder=!1;u.isGridUpDate=!1;u.csCode="";u.isDailyTUSM=!1;u.isOutbound=!1;u.isTUS=!1;u.isInbound=!1;u.orderStyleDropDownList=[];u.locationDropdown=[];u.warehouseDropdown=[];u.ConsigneeDropdown=[];u.specialFilter=function(){var n={};return u.isNewCreateOrder?(u.requestParams.orderType="Outbound delivery",n.orderType=u.requestParams.orderType,n.isNew=!0,u.isNewCreateOrder=!1,n):u.isNewCreateInboundOrder?(u.requestParams.orderType="Inbound",n.orderType=u.requestParams.orderType,n.isNew=!0,u.isNewCreateInboundOrder=!1,n):(n.orderNo=u.requestParams.orderNo,n.orderType=u.requestParams.orderType,n.piNo=u.requestParams.piNo,n.orderStartDate=u.requestParams.orderStartDate,n.orderEndDate=u.requestParams.orderEndDate,n.etaStartDate=u.requestParams.etaStartDate,n.etaEndDate=u.requestParams.etaEndDate,n.billNo=u.requestParams.mbl,n.containerNo=u.requestParams.containerNo,n.inboundStartDate=u.requestParams.inboundStartDate,n.inboundEndDate=u.requestParams.inboundEndDate,n.outboundStartDate=u.requestParams.outboundStartDate,n.outboundEndDate=u.requestParams.outboundEndDate,n.lotNo=u.requestParams.lotNo,n.transport=u.requestParams.transport,n.ccfStartDate=u.requestParams.ccfStartDate,n.ccfEndDate=u.requestParams.ccfEndDate,n.bodStartDate=u.requestParams.bodStartDate,n.bodEndDate=u.requestParams.bodEndDate,n.customerName=u.requestParams.consignee,n.warehouseLocation=u.requestParams.location,n.opStartDate=u.requestParams.opStartDate,n.opEndDate=u.requestParams.opEndDate,n.warehouseCode=u.requestParams.warehouseCode,n.isNotCompleted=u.requestParams.isNotCompleted,n)};u.init=function(){$("#orderStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#orderEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#etaStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#etaEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#inboundStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#inboundEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#outboundStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#outboundEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#ccfStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#ccfEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#bodStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#bodEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#opStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#opEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$.post("/UserSession/GetUserBasic",function(n){n.xzck&&(u.isXZCK=!0)});u.initData()};u.initGrid=function(){new agGrid.createGrid(document.getElementById("agGrid"),u.gridOptions)};u.initData=function(){$.post("/UserSession/GetUserBasic",function(n){u.csCode=n.csCode;u.isDailyTUSM=u.csCode=="TUSM"?!0:!1;u.isDailyTUM=u.csCode=="TUM"?!0:!1;u.isTUS=u.csCode=="TUS"?!0:!1});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Location"}}).then(function(n){n.data.code===200?u.locationDropdown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Warehouse Code"}}).then(function(n){n.data.code===200?u.warehouseDropdown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Consignee Name"}}).then(function(n){n.data.code===200?u.ConsigneeDropdown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetOrderTypeDropdown",data:{fieldName:"OrderStyle"}}).then(function(n){if(n.data.code===200){u.orderStyleDropDownList=n.data.data;u.orderStyleDropDownList.length==0&&(u.orderStyleDropDownList=[{text:"Inbound",value:"Inbound"},{text:"Outbound delivery",value:"Outbound delivery"}]);u.requestParams.orderType=u.orderStyleDropDownList[0].value;for(var t=0;t<u.orderStyleDropDownList.length;t++)u.orderStyleDropDownList[t].value=="Outbound delivery"&&(u.requestParams.orderType="Outbound delivery",u.isOutbound=!0),u.orderStyleDropDownList[t].value=="Inbound"&&(u.isInbound=!0);u.initGrid()}else myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})};var e=[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"Order No.",field:"orderNo"},{headerName:"Order Type",field:"orderType"},{headerName:"Order Date",field:"orderDate",valueGetter:function(n){return!n.data||!n.data.orderDate?null:new Date(n.data.orderDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Customer Code",field:"customerCode"},{headerName:"Location",field:"warehouseLocation"},{headerName:"PI No.",field:"piNo"},{headerName:"LOTNO",field:"lotNo"},{headerName:"Transport Agent",field:"transportationAgent"},{headerName:"Recipient Address",field:"address"},{headerName:"Customer Required Goods Reciving Date",field:"receiptDate",valueGetter:function(n){return!n.data||!n.data.receiptDate?null:new Date(n.data.receiptDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Special requirement",field:"trackingRemarks"},{headerName:"Delivery type",field:"deliveryType"},{headerName:"KMS",field:"kilometers"},{headerName:"Truck",field:"deliveryVehicleType"},{headerName:"Warehouse outbound date",field:"outboundCompletionTime",valueGetter:function(n){return!n.data||!n.data.outboundCompletionTime?null:new Date(n.data.outboundCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Delivery completion time",field:"deliveryCompletionTime",valueGetter:function(n){return!n.data||!n.data.deliveryCompletionTime?null:new Date(n.data.deliveryCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"POD receipt completion time",field:"podReturnTime",valueGetter:function(n){return!n.data||!n.data.podReturnTime?null:new Date(n.data.podReturnTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"DN No.",field:"managementNumber"},{headerName:"Warehouse Code",field:"warehouseAgent"},{headerName:"Description",field:"productName"},{headerName:"Cargo class",field:"productCategory"},{headerName:"Type",field:"productSpecification"},{headerName:"Power",field:"power"},{headerName:"Quantity",field:"quantity"},{headerName:"Total Vol",field:"totalPower"},{headerName:"Pallet QTY",field:"palletQty"},{headerName:"Chargeable Weight",field:"grossWeight"},{headerName:"Container No.",field:"containerNo"},{headerName:"Container QTY",field:"containerQty"},{headerName:"Container type",field:"containerType"},{headerName:"Cargo Value/ EUR",field:"cargoValue"},{headerName:"Consignee picks up",field:"consigneePicksUp"},{headerName:"Date of receiving order",field:"orderInstructionReceiptDate",valueGetter:function(n){return!n.data||!n.data.orderInstructionReceiptDate?null:new Date(n.data.orderInstructionReceiptDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Cancel order",field:"cancelOrder"},{headerName:"Send order to the supplier",field:"instructionSendingTime",valueGetter:function(n){return!n.data||!n.data.instructionSendingTime?null:new Date(n.data.instructionSendingTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Settlement completion time",field:"settlementCompletionTime",valueGetter:function(n){return!n.data||!n.data.settlementCompletionTime?null:new Date(n.data.settlementCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Operation completion time",field:"operationCompletionTime",valueGetter:function(n){return!n.data||!n.data.operationCompletionTime?null:new Date(n.data.operationCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},],o=[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"Order No.",field:"orderNo"},{headerName:"Order Type",field:"orderType"},{headerName:"Order Date",field:"orderDate",valueGetter:function(n){return!n.data||!n.data.orderDate?null:new Date(n.data.orderDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Customer Code",field:"customerCode"},{headerName:"PI No.",field:"piNo"},{headerName:"LOT NO.",field:"lotNo"},{headerName:"Location",field:"warehouseLocation"},{headerName:"Warehouse Code",field:"warehouseAgent"},{headerName:"Description",field:"productName"},{headerName:"Cargo class",field:"productCategory"},{headerName:"Power",field:"power"},{headerName:"Quantity",field:"quantity"},{headerName:"Total Vol",field:"totalPower"},{headerName:"Pallet QTY",field:"palletQty"},{headerName:"Volume",field:"volume"},{headerName:"Container No.",field:"containerNo"},{headerName:"Container QTY",field:"containerQty"},{headerName:"Container type",field:"containerType"},{headerName:"Port of Discharge",field:"destinationPort"},{headerName:"M/BL",field:"billNo"},{headerName:"Booking Date",field:"bookingDate",valueGetter:function(n){return!n.data||!n.data.bookingDate?null:new Date(n.data.bookingDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"ETD",field:"etd"},{headerName:"ETA",field:"eta"},{headerName:"ATA",field:"ata"},{headerName:"Transport Agent",field:"transportationAgent"},{headerName:"Recipient Address",field:"address"},{headerName:"Special requirement",field:"trackingRemarks"},{headerName:"Delivery type",field:"deliveryType"},{headerName:"Truck",field:"deliveryVehicleType"},{headerName:"Date of receiving order",field:"orderInstructionReceiptDate",valueGetter:function(n){return!n.data||!n.data.orderInstructionReceiptDate?null:new Date(n.data.orderInstructionReceiptDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Remark",field:"remarks"},{headerName:"Cancel order",field:"cancelOrder"},{headerName:"Send order to the supplier",field:"instructionSendingTime",valueGetter:function(n){return!n.data||!n.data.instructionSendingTime?null:new Date(n.data.instructionSendingTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Clearance release time",field:"customsClearanceCompletionTime",valueGetter:function(n){return!n.data||!n.data.customsClearanceCompletionTime?null:new Date(n.data.customsClearanceCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Actual time of inbound",field:"inboundCompletionTime",valueGetter:function(n){return!n.data||!n.data.inboundCompletionTime?null:new Date(n.data.inboundCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Settlement completion time",field:"settlementCompletionTime",valueGetter:function(n){return!n.data||!n.data.settlementCompletionTime?null:new Date(n.data.settlementCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Operation completion time",field:"operationCompletionTime",valueGetter:function(n){return!n.data||!n.data.operationCompletionTime?null:new Date(n.data.operationCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},],s=[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"Order No.",field:"orderNo"},{headerName:"MBL",field:"billNo"},{headerName:"Container No.",field:"containerNo"},{headerName:"ETA",field:"eta",valueGetter:function(n){return!n.data||!n.data.eta?null:new Date(n.data.eta)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Arrival Notice Received",field:"cargoNotificationReceivedTime",valueGetter:function(n){return!n.data||!n.data.cargoNotificationReceivedTime?null:new Date(n.data.cargoNotificationReceivedTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Customs Clearance Docs Received",field:"receivedCustomsDocumentsTime",valueGetter:function(n){return!n.data||!n.data.receivedCustomsDocumentsTime?null:new Date(n.data.receivedCustomsDocumentsTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Customs Clearance Docs Sent",field:"sentCustomsDocumentsTime",valueGetter:function(n){return!n.data||!n.data.sentCustomsDocumentsTime?null:new Date(n.data.sentCustomsDocumentsTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"AD/CVD",field:"cvdCaseNo"},{headerName:"Freight Release",field:"carrierReleaseTime",valueGetter:function(n){return!n.data||!n.data.carrierReleaseTime?null:new Date(n.data.carrierReleaseTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"Customs Clearance Finished",field:"customsClearanceCompletionTime",valueGetter:function(n){return!n.data||!n.data.customsClearanceCompletionTime?null:new Date(n.data.customsClearanceCompletionTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"7501 & 3461 Received",field:"rev7501",valueGetter:function(n){return!n.data||!n.data.rev7501?null:new Date(n.data.rev7501)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"DO Received",field:"doResceivedTime",valueGetter:function(n){return!n.data||!n.data.doResceivedTime?null:new Date(n.data.doResceivedTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"DO Sent",field:"doSendingTime",valueGetter:function(n){return!n.data||!n.data.doSendingTime?null:new Date(n.data.doSendingTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},{headerName:"East2West Ref #",field:"ewRef"},{headerName:"DO Confirmation",field:"confirmedDOSendingTime",valueGetter:function(n){return!n.data||!n.data.confirmedDOSendingTime?null:new Date(n.data.confirmedDOSendingTime)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD HH:mm:ss"):""}},];u.gridOptions={rowModelType:"serverSide",columnDefs:e,paginationPageSize:20,defaultColDef:{sortable:!0,filter:"agSetColumnFilter",filterParams:{values:getUniqueValues,excelMode:"windows",refreshValuesOnOpen:!1},suppressColumnsToolPanel:!0},getRowId:function(n){const t=n.data.orderNo;return t?t.toString():""},pagination:!0,suppressMovableColumns:!0,rowSelection:"multiple",enableRangeSelection:!0,components:{buttonCellRenderer:h},onPaginationChanged:function(){},onGridReady:function(n){f=n.api;u.isXZCK?f.getColumn("productName").setVisible(!1):f.getColumn("warehouseAgent").setVisible(!1);const t={getRows:function(n){createDataSourceV2("/OrdersOverView/GetOrderList",u.specialFilter(),n);u.isGridUpDate=!1}};n.api.setGridOption("serverSideDatasource",t)},onRowDoubleClicked:function(n){var i=n.data,r=t.open({templateUrl:"/OrdersOverView/OrderDetail",controller:"orderManagement.ordersOverView.orderDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{orderNo:i.orderNo}}}});r.result.then(function(){},function(){}).finally(function(){})}};u.changeOrderType=function(){u.isGridUpDate=!0};u.search=function(){u.isGridUpDate&&(u.requestParams.orderType=="Inbound"?(f.updateGridOptions({columnDefs:o}),u.isXZCK&&f.getColumn("warehouseAgent").setVisible(!1)):u.requestParams.orderType=="Customs clearance"?f.updateGridOptions({columnDefs:s}):(f.updateGridOptions({columnDefs:e}),u.isXZCK&&(f.getColumn("warehouseAgent").setVisible(!1),f.moveColumns(["lotNo"],16))));f.refreshServerSide();f.onFilterChanged()};u.resetting=function(){u.requestParams={};u.isXZCK=!0;u.requestParams.orderType=u.orderStyleDropDownList[0].value;u.search()};u.addOutBoundOrder=function(){myAlert.message.confirm("Are you sure to add a new outbound order?",function(n){n&&r.post("/OrdersOverView/AddOutBoundOrder",{}).then(function(n){n.data.code==200?(myAlert.message.info("Order creation successful：【"+n.data.data+"】"),u.isNewCreateOrder=!0,u.search()):myAlert.message.error(n.data.data.msg)})})};u.addInBoundOrder=function(){myAlert.message.confirm("Are you sure to add a new inbound order?",function(n){n&&r.post("/OrdersOverView/AddInBoundOrder",{}).then(function(n){console.log(n);n.data.code==200?(myAlert.message.info("Order creation successful：【"+n.data.data+"】"),u.isNewCreateInboundOrder=!0,u.search()):myAlert.message.error(n.data.data.msg)})})};u.openDailyReport=function(){var n=t.open({templateUrl:"/OrdersOverView/DailyReport",controller:"basicManagement.dailyReport.dailyReport as vm",backdrop:"static",size:"md",resolve:{options:function(){return{}}}});n.result.then(function(){},function(){}).finally(function(){})};u.dailyReport=function(){u.isBusy=!0;myAlert.message.confirm("Are you sure to generate the daily report?",function(t){t?r({method:"POST",url:"/OrdersOverView/EditDailyReport",data:{}}).then(function(n){n.data.code==200&&myAlert.message.success(n.data.msg)}).catch(function(n){console.log(n)}).finally(function(){u.isBusy=!1}):(u.isBusy=!1,n.$apply())})};u.dailyReportModule=function(){myAlert.message.confirm("Are you sure to generate the daily report?",function(t){t?r({method:"POST",url:"/OrdersOverView/EditDailyReport",data:{type:"module"}}).then(function(n){n.data.code==200&&myAlert.message.success(n.data.msg)}).catch(function(n){console.log(n)}).finally(function(){}):(u.isBusy=!1,n.$apply())})};u.dailyCustomsReport=function(){var n=t.open({templateUrl:"/OrdersTUS/CustomTUSReport",controller:"warehouse.OrderManagement.tus.customTUSReport as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{}}}});n.result.then(function(){},function(){}).finally(function(){})};u.dailyReportMaterials=function(){myAlert.message.confirm("Are you sure to generate the daily report?",function(t){t?r({method:"POST",url:"/OrdersOverView/EditDailyReport",data:{type:"materials"}}).then(function(n){n.data.code==200&&myAlert.message.success(n.data.msg)}).catch(function(n){console.log(n)}).finally(function(){}):(u.isBusy=!1,n.$apply())})};u.isDateValue=function(n){if(n instanceof Date)return!0;if(typeof n=="string"){if(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(n))return!0;if(!/^\d{4}-\d{2}-\d{2}$/.test(n))return!1;const t=new Date(n);return isNaN(t.getTime())?!1:!0}};u.export=function(){u.busying=!0;r({method:"POST",url:"/OrdersOverView/GetOrderListByExcel",data:u.specialFilter()}).then(function(n){var h,i;if(n.data.code===200){var c=n.data.data,r=new ExcelJS.Workbook,t=r.addWorksheet("Sheet 1"),f=[];f=u.requestParams.orderType=="Inbound"?o:u.requestParams.orderType=="Customs clearance"?s:e;h=f.filter(n=>n.headerName!="Actions").map(function(n){return{header:n.headerName,key:n.field}});t.columns=h;c.forEach(function(n){t.addRow(n)});e.forEach(function(n,i){var r=0;r=Math.max(r,n.headerName.length);t.getColumn(i+1).width=r+5});t.eachRow({includeEmpty:!0},function(n){n.eachCell({includeEmpty:!0},function(n){if(n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},n.font={name:"Arial",size:10},u.isDateValue(n.value)){let t=n.value;typeof n.value=="string"&&/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(n.value)&&(t=new Date(n.value));const i=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZone:"Asia/Shanghai"});n.value=i.format(t)}})});i=t.getRow(1);i.font={name:"Arial",size:10,bold:!0};i.alignment={horizontal:"center"};r.xlsx.writeBuffer().then(function(n){var i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.href=URL.createObjectURL(i);t.download="OrdersOverView.xlsx";t.click()})}}).catch(function(n){console.log(n)}).finally(function(){u.busying=!1})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.orderWarehouse",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){function l(n){var r=document.createElement("div"),t,i;return r.classList.add("button-container-ag"),t=document.createElement("button"),t.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',t.classList.add("btn","btn-primary","btn-sm"),t.addEventListener("click",function(){e.openOutNoticeDetail(n.data)}),r.appendChild(t),e.isComplete||(i=document.createElement("button"),i.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',i.classList.add("btn","btn-danger","btn-sm"),i.addEventListener("click",function(){myAlert.message.confirm("The modified content has not been saved yet. Are you sure you still want to execute it?",function(t){t&&u({method:"POST",url:"/OrdersOverView/DeleteOutboundNotice",data:{outNoticeNo:n.data.id,orderNo:f.dataItem.orderNo}}).then(function(n){n.data.code==200?(e.initData(),myAlert.message.success("Successfully Deleted")):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})}),r.appendChild(i)),r}function a(n){var r=document.createElement("div"),i,t;return r.classList.add("button-container-ag"),i=document.createElement("button"),i.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',i.classList.add("btn","btn-primary","btn-sm"),i.addEventListener("click",function(){e.openOutbountDetail(n.data)}),r.appendChild(i),e.isComplete||(t=document.createElement("button"),t.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),t.disabled=n.data.audited,t.addEventListener("click",function(){myAlert.message.confirm("The modified content has not been saved yet. Are you sure you still want to execute it?",function(t){t&&u({method:"POST",url:"/OrdersOverView/DeleteOutbound",data:{outboundNo:n.data.id,orderNo:f.dataItem.orderNo}}).then(function(n){n.data.code==200?(e.initData(),myAlert.message.success("Successfully Deleted")):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})}),r.appendChild(t)),r}function v(n){var r=document.createElement("div"),i,t;return r.classList.add("button-container-ag"),i=document.createElement("button"),i.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',i.classList.add("btn","btn-primary","btn-sm"),i.addEventListener("click",function(){e.openInvNoticeDetail(n.data)}),r.appendChild(i),t=document.createElement("button"),t.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),t.disabled=n.data.audited,t.addEventListener("click",function(){myAlert.message.confirm("Are you sure you want to delete the currently selected product details that are already included?",function(t){t&&u({method:"POST",url:"/OrdersOverView/DeleteInNotice",data:{id:n.data.id,orderNo:f.dataItem.orderNo}}).then(function(n){n.data.code==200?(e.initData(),myAlert.message.success("Successfully Deleted")):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})}),r.appendChild(t),r}function y(n){var r=document.createElement("div"),i,t;return r.classList.add("button-container-ag"),i=document.createElement("button"),i.innerHTML='<i class="bi bi-pencil-fill"><\/i> Edit',i.classList.add("btn","btn-primary","btn-sm"),i.addEventListener("click",function(){e.openInvbountDetail(n.data)}),r.appendChild(i),e.isComplete||(t=document.createElement("button"),t.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),t.disabled=n.data.audited,t.addEventListener("click",function(){myAlert.message.confirm("The modified content has not been saved yet. Are you sure you still want to execute it?",function(t){t&&u({method:"POST",url:"/OrdersOverView/DeleteInvbound",data:{invNo:n.data.id,orderNo:f.dataItem.orderNo}}).then(function(n){n.data.code==200?(e.initData(),myAlert.message.success("Successfully Deleted")):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})})}),r.appendChild(t)),r}var e=this,o,s,h,c;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.isComplete=!1;e.specialFilter=function(){return{}};e.isOutbound=!1;e.init=function(){u({method:"POST",url:"/OrdersOverView/GetOrderInfomation",data:{orderNo:f.dataItem.orderNo}}).then(function(n){n.data.code==200?(e.requestParams=n.data.data,e.isComplete=n.data.data.operationCompletionTime==null?!1:!0,e.isOutbound=n.data.data.orderType=="Outbound delivery"?!0:!1,r(function(){e.isOutbound?(new agGrid.createGrid(document.getElementById("outNoticeAgGird"),e.outNoticeOptions),new agGrid.createGrid(document.getElementById("outAgGrid"),e.outOptions)):(e.requestParams.eta&&(e.requestParams.eta=moment(e.requestParams.eta).format("YYYY-MM-DD")),e.requestParams.etd&&(e.requestParams.etd=moment(e.requestParams.etd).format("YYYY-MM-DD")),new agGrid.createGrid(document.getElementById("invNoticeAgGrid"),e.invNoticeOptions),new agGrid.createGrid(document.getElementById("invAgGrid"),e.invOptions));e.initData()},0)):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})};e.openOutNoticeDetail=function(n){var t=i.open({templateUrl:"/OrdersOverView/OutNoticeDetail",controller:"orderManagement.ordersOverView.outNoticeDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{outNoticeNo:n?.id,orderNo:f.dataItem.orderNo,entryDate:n?.entryDate,expOutDate:n?.expOutDate,isComplete:e.isComplete}}}});t.result.then(function(){},function(){}).finally(function(){e.initData()})};e.openOutbountDetail=function(n){var t=i.open({templateUrl:"/OrdersOverView/OutboundDetail",controller:"orderManagement.ordersOverView.outboundDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{outboundNo:n?.id,orderNo:f.dataItem.orderNo,entryDate:n?.entryDate,expOutDate:n?.expOutDate,isComplete:e.isComplete,isAudited:n?.audited}}}});t.result.then(function(){},function(){}).finally(function(){e.initData()})};e.outNoticeOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"id"},{headerName:"Recorded date",field:"entryDate"},{headerName:"Single recorded",field:"operator"},{headerName:"Remark",field:"remarks"},{headerName:"Order No.",field:"orderNo"},],defaultColDef:{flex:1,headerHidden:!0,filter:!1},components:{buttonCellRenderer:l},rowData:[],pagination:!0,suppressRowClickSelection:!0,onGridReady:function(n){o=n.api},onRowDoubleClicked:function(n){var t=n.data;e.openOutNoticeDetail(t)}};e.outOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer2",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"id"},{headerName:"Recorded date",field:"entryDate",valueGetter:function(n){return!n.data||!n.data.entryDate?null:new Date(n.data.entryDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Customer Code",field:"csCode"},{headerName:"Single recorded",field:"operator"},{headerName:"Remark",field:"remarks"},{headerName:"Audit",field:"audited"},],defaultColDef:{flex:1,headerHidden:!0,filter:!1},components:{buttonCellRenderer2:a},rowData:[],pagination:!0,suppressRowClickSelection:!0,onGridReady:function(n){s=n.api},onRowDoubleClicked:function(n){var t=n.data;e.openOutbountDetail(t)}};e.openInvNoticeDetail=function(n){var t=i.open({templateUrl:"/OrdersOverView/InvNoticeDetail",controller:"orderManagement.ordersOverView.invNoticeDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{invNoticeNo:n?.id,orderNo:f.dataItem.orderNo,entryDate:n?.entryDate,expInvDate:n?.expInvDate,isComplete:e.isComplete}}}});t.result.then(function(){},function(){}).finally(function(){e.initData()})};e.openInvbountDetail=function(n){var t=i.open({templateUrl:"/OrdersOverView/InvboundDetail",controller:"orderManagement.ordersOverView.invboundDetail as vm",backdrop:"static",size:"full",resolve:{options:function(){return{invboundNo:n?.id,orderNo:f.dataItem.orderNo,entryDate:n?.entryDate,expInvDate:n?.expInvDate,isComplete:e.isComplete,isAudited:n?.audited}}}});t.result.then(function(){},function(){}).finally(function(){e.initData()})};e.invNoticeOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"id"},{headerName:"Recorded date",field:"entryDate"},{headerName:"Single recorded",field:"operator"},{headerName:"Remark",field:"remarks"},{headerName:"Order No.",field:"orderNo"},],defaultColDef:{flex:1,headerHidden:!0,filter:!1},components:{buttonCellRenderer:v},rowData:[],pagination:!0,suppressRowClickSelection:!0,onGridReady:function(n){c=n.api},onRowDoubleClicked:function(n){var t=n.data;e.openInvNoticeDetail(t)}};e.invOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer2",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"id"},{headerName:"Recorded date",field:"entryDate",valueGetter:function(n){return!n.data||!n.data.entryDate?null:new Date(n.data.entryDate)},valueFormatter:function(n){return n.value?moment(n.value).format("YYYY-MM-DD"):""}},{headerName:"Customer Code",field:"csCode"},{headerName:"Single recorded",field:"operator"},{headerName:"Remark",field:"remarks"},{headerName:"Audit",field:"audited"},],defaultColDef:{flex:1,headerHidden:!0,filter:!1},components:{buttonCellRenderer2:y},rowData:[],pagination:!0,suppressRowClickSelection:!0,onGridReady:function(n){h=n.api},onRowDoubleClicked:function(n){var t=n.data;e.openInvbountDetail(t)}};e.initData=function(){e.requestParams.orderType=="Outbound delivery"?(u({method:"POST",url:"/OrdersOverView/GetOutNoticeOrderNo",data:{orderNo:f.dataItem.orderNo}}).then(function(n){o.updateGridOptions({rowData:n.data.data})}).catch(function(n){console.log(n)}),u({method:"POST",url:"/OrdersOverView/GetOutOrderNo",data:{orderNo:f.dataItem.orderNo}}).then(function(n){s.updateGridOptions({rowData:n.data.data})}).catch(function(n){console.log(n)})):e.requestParams.orderType=="Inbound"&&(u({method:"POST",url:"/OrdersOverView/GetInvNoticeOrderNo",data:{orderNo:f.dataItem.orderNo}}).then(function(n){c.updateGridOptions({rowData:n.data.data})}).catch(function(n){console.log(n)}),u({method:"POST",url:"/OrdersOverView/GetInvOrderNo",data:{orderNo:f.dataItem.orderNo}}).then(function(n){h.updateGridOptions({rowData:n.data.data})}).catch(function(n){console.log(n)}))};e.search=function(){};e.dismiss=function(){t.dismiss()};e.addOutBoundNotice=function(){e.openOutNoticeDetail(null)};e.addOutbount=function(){e.openOutbountDetail(null)};e.addInvound=function(){e.openInvbountDetail(null)};e.addInvoundNotice=function(){e.openInvNoticeDetail(null)}}]);angular.module("myApp").controller("orderManagement.ordersOverView.outboundDetail",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){function h(n){var i=document.createElement("div"),t;return e.isComplete||e.isAudited?i:(i.classList.add("button-container-ag"),t=document.createElement("button"),t.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),n.data.status!="库存_出库通知未处理"&&n.data.status!="已出库"&&(t.disabled=!0),t.addEventListener("click",function(){(n.data.status=="库存_出库通知未处理"||n.data.status=="已出库")&&(n.data.status=="已出库"&&(s+=n.data.goodsNo+","),o.applyTransaction({remove:[n.data]}))}),i.appendChild(t),i)}var e=this,o,s;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.saving=!1;e.specialFilter=function(){return{}};s="";e.isComplete=f.isComplete;e.isAudited=f.isAudited;e.init=function(){$("#entryDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});r(function(){e.requestParams.orderID=f.outboundNo;f.entryDate?$("#entryDate").datepicker("setDate",new Date(f.entryDate)):$("#entryDate").datepicker("setDate",new Date);e.initData();new agGrid.createGrid(document.getElementById("agGird"),e.gridOptions)},0)};e.gridOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},hide:e.isComplete||e.isAudited,suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"goodsNo"},{headerName:"Warehouse Code",field:"warehouseAgent"},{headerName:"Location",field:"warehouseLocation"},{headerName:"BL No.",field:"billNo"},{headerName:"Container No.",field:"boxNo"},{headerName:"Type",field:"goodsSpec"},{headerName:"Lot No.",field:"lotNo"},{headerName:"Power",field:"power"},{headerName:"Pallets",field:"qty1"},{headerName:"Qty",field:"inventoryqQty"},{headerName:"Total Vol",field:"totalPower"},{headerName:"Inbound Date",field:"inStockDate"},{headerName:"Outbound Order Date",field:"outNoticeDate"},{headerName:"Outbound Date",field:"outStockDate"},{headerName:"QTY Per Pallet",field:"singleTonQty"},{headerName:"Storage Location",field:"location"},{headerName:"Class",field:"goodsClass"},{headerName:"Index No.",field:"indexNo"},{headerName:"Customer",field:"customerName"},{headerName:"SO",field:"tsw_so"}],defaultColDef:{filter:!1},components:{buttonCellRenderer:h},rowClassRules:{"color-new":function(n){if(n.data.status=="库存_出库通知未处理")return!0}},pagination:!0,rowData:[],suppressRowClickSelection:!0,onGridReady:function(n){o=n.api}};e.initData=function(){u({method:"POST",url:"/OrdersOverView/GetGoodsNoByOutNo",data:{outboundNo:e.requestParams.orderID}}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns()}).catch(function(n){console.log(n)})};e.search=function(){};e.dismiss=function(){t.dismiss()};e.addOutBoundOrder=function(){var n="",t;o.forEachNode(function(t){t.data.status=="库存_出库通知未处理"&&(n+=t.data.goodsNo+",")});t=i.open({templateUrl:"/OrdersOverView/LoadData",controller:"orderManagement.ordersOverView.loadData as vm",backdrop:"static",size:"full",resolve:{options:function(){return{ids:n,type:"Outbound",orderNo:f.orderNo}}}});t.result.then(function(n){o.applyTransaction({add:n})},function(){})};e.save=function(){var n,t;if(e.saving=!0,!e.requestParams.entryDate){myAlert.message.error("Please select the date");e.saving=!1;return}n="";o.forEachNode(function(t){t.data.status=="库存_出库通知未处理"&&(n+=t.data.goodsNo+",")});t=e.requestParams.entryDate;u({method:"POST",url:"/OrdersOverView/CreateOrEdidOutBound",data:{ids:n,entryDate:t,outboundNo:e.requestParams.orderID,orderNo:f.orderNo,delIds:s}}).then(function(n){s="";n.data.code==200?(e.requestParams.orderID||(e.requestParams.orderID=n.data.data),myAlert.message.success("Successfully saved")):myAlert.message.error(n.data.msg);e.saving=!1;e.initData()}).catch(function(n){e.saving=!1;console.log(n)})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.outNoticeDetail",["$scope","$uibModalInstance","$uibModal","$timeout","$http","options",function(n,t,i,r,u,f){function h(n){var i=document.createElement("div"),t;return(i.classList.add("button-container-ag"),e.isComplete)?i:(t=document.createElement("button"),t.innerHTML='<i class="bi bi-archive-fill"><\/i> Delete',t.classList.add("btn","btn-danger","btn-sm"),t.disabled=!0,(n.data.status=="库存_已入库"||n.data.status=="库存_出库通知未处理"||n.data.status=="入库通知未处理_出库通知锁定")&&(t.disabled=!1),t.addEventListener("click",function(){if(n.data.status=="库存_已入库"||n.data.status=="库存_出库通知未处理"||n.data.status=="入库通知未处理_出库通知锁定"){if(n.data.status=="库存_出库通知未处理"||n.data.status=="入库通知未处理_出库通知锁定"){var t=n.data.goodsNo;s+=t+","}o.applyTransaction({remove:[n.data]})}}),i.appendChild(t),i)}var e=this,o,s;n.$on("$viewContentLoaded",function(){App.initAjax()});e.requestParams={};e.saving=!1;e.specialFilter=function(){return{}};e.isComplete=f.isComplete;e.init=function(){$("#entryDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#expOutDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});r(function(){e.requestParams.orderID=f.outNoticeNo;f.entryDate?$("#entryDate").datepicker("setDate",new Date(f.entryDate)):$("#entryDate").datepicker("setDate",new Date);f.expOutDate?$("#expOutDate").datepicker("setDate",new Date(f.expOutDate)):$("#expOutDate").datepicker("setDate",new Date);e.initData();new agGrid.createGrid(document.getElementById("agGird"),e.gridOptions)},0)};s="";e.gridOptions={columnDefs:[{headerName:"Actions",field:"actions",cellRenderer:"buttonCellRenderer",filter:!1,sortable:!1,filterParams:{suppressSorting:!1},hide:e.isComplete,suppressMovable:!0,suppressHeaderMenuButton:!0,suppressColumnsToolPanel:!0,lockPinned:!0,pinned:"right"},{headerName:"HiH #",field:"goodsNo"},{headerName:"Warehouse Code",field:"warehouseAgent"},{headerName:"Location",field:"warehouseLocation"},{headerName:"BL No.",field:"billNo"},{headerName:"Container No.",field:"boxNo"},{headerName:"Type",field:"goodsSpec"},{headerName:"Lot No.",field:"lotNo"},{headerName:"Power",field:"power"},{headerName:"Pallets",field:"qty1"},{headerName:"Qty",field:"inventoryqQty"},{headerName:"Total Vol",field:"totalPower"},{headerName:"Inbound Date",field:"inStockDate"},{headerName:"Outbound Order Date",field:"outNoticeDate"},{headerName:"Outbound Date",field:"outStockDate"},{headerName:"QTY Per Pallet",field:"singleTonQty"},{headerName:"Storage Location",field:"location"},{headerName:"Class",field:"goodsClass"},{headerName:"Index No.",field:"indexNo"},{headerName:"Customer",field:"customerName"},{headerName:"SO",field:"tsw_so"}],defaultColDef:{filter:!1},components:{buttonCellRenderer:h},rowClassRules:{"color-new":function(n){if(n.data.status=="库存_已入库")return!0},"color-release":function(n){if(n.data.status=="已出库")return!0}},rowData:[],pagination:!0,suppressRowClickSelection:!0,onGridReady:function(n){o=n.api}};e.initData=function(){u({method:"POST",url:"/OrdersOverView/GetGoodsNoByOutNoticeNo",data:{outNoticeNo:e.requestParams.orderID}}).then(function(n){o.updateGridOptions({rowData:n.data.data});o.autoSizeAllColumns()}).catch(function(n){console.log(n)})};e.search=function(){};e.dismiss=function(){t.dismiss()};e.addOutBoundOrder=function(){var n="",t;o.forEachNode(function(t){t.data.status=="库存_已入库"&&(n+=t.data.goodsNo+",")});t=i.open({templateUrl:"/OrdersOverView/LoadData",controller:"orderManagement.ordersOverView.loadData as vm",backdrop:"static",size:"full",resolve:{options:function(){return{ids:n,orderNo:f.orderNo}}}});t.result.then(function(n){o.applyTransaction({add:n})},function(){})};e.save=function(){var n,t,i;if(e.saving=!0,n=e.requestParams.entryDate,t=e.requestParams.expOutDate,!n||!t){e.saving=!1;myAlert.message.error("Please select the date");return}i="";o.forEachNode(function(n){n.data.status=="库存_已入库"&&(i+=n.data.goodsNo+",")});u({method:"POST",url:"/OrdersOverView/CreateOrEdidOutNotice",data:{ids:i,entryDate:n,expOutDate:t,outNoticeNo:e.requestParams.orderID,orderNo:f.orderNo,delIds:s}}).then(function(n){s="";n.data.code==200?(e.requestParams.orderID||(e.requestParams.orderID=n.data.data),e.saving=!1,myAlert.message.success("Successfully saved")):(myAlert.message.error(n.data.msg),e.saving=!1);e.initData()}).catch(function(n){console.log(n)})}}]);angular.module("myApp").controller("orderManagement.ordersOverView.splitOrder",["$scope","$uibModalInstance","options","$http",function(n,t,i,r){var u=this,e,f;u.cancel=function(){t.dismiss("cancel")};u.requestParams={SplitGoodsNo:"",RemainingQty:0,RemainingTotalPower:0,SplitTotalPower:0,SplitPalletsQty:0,RemainingPalletsQty:0};u.isLK=!1;u.init=function(){u.requestParams.Qty=i.rowData.inventoryqQty;u.requestParams.Power=Number(i.rowData.power);u.requestParams.LotNo=i.rowData.lotNo;u.requestParams.GoodsNo=i.rowData.goodsNo;u.requestParams.PalletsQty=Number(i.rowData.qty1);u.requestParams.TotalPower=Number(i.rowData.totalPower);u.requestParams.QTYPerPallet=i.rowData.singleTonQty};u.checkPalles=function(){const n=document.getElementById("SplitPalletsQty"),t=document.getElementById("RemainingPalletsQty");return parseFloat(u.requestParams.SplitPalletsQty)+parseFloat(u.requestParams.RemainingPalletsQty)==u.requestParams.PalletsQty?(n.classList.remove("is-invalid"),t.classList.remove("is-invalid"),!1):(n.classList.add("is-invalid"),t.classList.add("is-invalid"),!0)};u.search=function(){f()};u.updateRemainingQty=function(){u.requestParams.SplitTotalPower=u.requestParams.SplitQty*u.requestParams.Power;u.requestParams.SplitPalletsQty=u.requestParams.QTYPerPallet==0?0:Number(parseFloat(u.requestParams.SplitQty/u.requestParams.QTYPerPallet).toFixed(2));u.requestParams.RemainingQty=u.requestParams.Qty-u.requestParams.SplitQty;u.requestParams.RemainingPalletsQty=Number(parseFloat(u.requestParams.PalletsQty-u.requestParams.SplitPalletsQty));u.requestParams.RemainingTotalPower=u.requestParams.RemainingQty*u.requestParams.Power;u.checkPalles()};u.updatePalletsQty=function(){if(!u.requestParams.QTYPerPallet){u.checkPalles();return}u.isLK||(u.requestParams.SplitQty=parseFloat(u.requestParams.SplitPalletsQty)*parseFloat(u.requestParams.QTYPerPallet),u.requestParams.SplitTotalPower=u.requestParams.SplitQty*u.requestParams.Power,u.requestParams.RemainingQty=u.requestParams.Qty-u.requestParams.SplitQty,u.requestParams.RemainingTotalPower=u.requestParams.RemainingQty*u.requestParams.Power);u.requestParams.RemainingPalletsQty=Number(parseFloat(u.requestParams.PalletsQty-u.requestParams.SplitPalletsQty).toFixed(2));u.checkPalles()};u.isBusy=!1;u.save=function(){if(!(u.requestParams.RemainingQty>0)||u.requestParams.SplitQty<=0||!u.requestParams.SplitQty||u.requestParams.SplitPalletsQty<0||u.requestParams.RemainingPalletsQty<0){myAlert.message.error("Please enter the correct disassembly quantity!");return}if(u.requestParams.SplitGoodsNo){myAlert.message.error("It has been successfully disassembled, please do not repeat the operation!");return}if(u.checkPalles()){myAlert.message.error("The number of pallets is not correct, please check the quantity of pallets!");return}myAlert.message.confirm("Are you sure you want to Split 【"+u.requestParams.GoodsNo+"】?",function(n){n&&(u.isBusy=!0,r({method:"POST",url:"/Warehouse/InsertSplitOrder",data:$.param(u.requestParams),headers:{"Content-Type":"application/x-www-form-urlencoded"}}).then(function(n){n.data.code===200?(u.requestParams.SplitGoodsNo=n.data.msg,myAlert.message.success("Split order completed！")):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)}).finally(function(){u.isBusy=!1}))})};u.specialFilter=function(){var n={};return n.LotNo=u.requestParams.LotNo,n};f=function(){$.ajax({url:`/Warehouse/GetSelectIssueDetailList`,type:"post",contentType:"application/json",data:JSON.stringify(u.specialFilter()),success:function(n){var t=JSON.parse(n);e.setGridOption("rowData",t.data)},error:function(n,t,i){console.error("Error fetching data:",t,i)}})}}]);angular.module("myApp").controller("orderManagement.trucking.dlvDetail",["$scope","$uibModalInstance","options","$timeout","$http","selectModal","$uibModal",function(n,t,i,r,u,f,e){var o=this,s;o.isComplete=!1;o.dismiss=function(){t.close("cancel")};o.requestParams={};o.init=function(){o.isComplete=i.isCompleted;new agGrid.createGrid(document.getElementById("agGrid"),o.gridOptions)};u({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Transport Agent"}}).then(function(n){n.data.code===200?o.transportAgents=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});o.initData=function(){u({method:"POST",url:"/Trucking/GetZCDDetail",data:{ZCDNo:i.zcdNo}}).then(function(n){if(n.status===200){if(n.data==""){myAlert.message.error("No detailed data found");t.close();return}o.requestParams=n.data;o.initGridData()}else myAlert.message.error("No detailed data found"),t.close()}).catch(function(n){console.log(n)})};o.initGridData=function(){u({method:"POST",url:"/Trucking/GetTruckingDataByZCD",data:{zcdNo:i.zcdNo}}).then(function(n){n.data.code===200?(n.data.data.forEach(n=>n.isNewRow=!1),s.updateGridOptions({rowData:n.data.data,pinnedBottomRowData:[{LOTNO:"TOTAL：","托盘数":n.data.data.reduce((n,t)=>n+t.托盘数,0),"数量":n.data.data.reduce((n,t)=>n+t.数量,0)}]}),n.data.data.length==0&&t.close()):myAlert.message.error(n.data)}).catch(function(n){console.log(n)})};o.gridOptions={pagination:!0,sideBar:{toolPanels:[{id:"columns",labelDefault:"Columns",labelKey:"columns",iconKey:"columns",toolPanel:"agColumnsToolPanel",suppressFiltersToolPane:!1},{id:"filters",labelDefault:"Filters",labelKey:"filters",iconKey:"filter",toolPanel:"agFiltersToolPanel"}],hiddenByDefault:!0},columnDefs:[{headerCheckboxSelection:!0,checkboxSelection:!0,field:"checkBox",maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"Trailer",field:"车号",editable:!1},{headerName:"Container type",field:"箱型",editable:!1},{headerName:"Container#",field:"箱号",editable:!1},{headerName:"Batch",field:"LOTNO",editable:!1},{headerName:"Pallets",field:"托盘数",editable:!1,cellDataType:"number",enableValue:!0,aggFunc:"sum"},{headerName:"QTY",field:"数量",editable:!1},],defaultColDef:{flex:1,cellClass:"align-right",filter:"agMultiColumnFilter",filterParams:{excelMode:"windows"},cellStyle:function(n){return n.data.modifiedCells&&n.data.modifiedCells[n.colDef.field]?{"background-color":"BlanchedAlmond"}:null}},onCellValueChanged:function(n){n.oldValue===n.newValue&&n.oldValue||(n.data.modifiedCells=n.data.modifiedCells||{},n.data.modifiedCells[n.colDef.field]=!0,n.api.refreshCells({rowNodes:[n.node],columns:[n.column],force:!0}))},getRowStyle:n=>n.data.isNewRow?{backgroundColor:"blanchedalmond"}:null,rowSelection:"multiple",enableRangeSelection:!0,suppressRowClickSelection:!0,onGridReady:function(n){s=n.api;i.zcdNo!=null?o.initData():(u({method:"POST",url:"/Trucking/GetOrderInfo",data:{orderNo:i.orderNo}}).then(function(n){n.data.code===200?(o.requestParams.zip=n.data.data.recipientPostalCode,o.requestParams.city=n.data.data.receivingCity,o.requestParams.company=n.data.data.recipientCompany,o.requestParams.address=n.data.data.address,o.requestParams.country=n.data.data.destinationCountry,o.requestParams.orderNo=i.orderNo):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)}),i.rowData.forEach(n=>n.isNewRow=!0),s.updateGridOptions({rowData:i.rowData,pinnedBottomRowData:[{LOTNO:"TOTAL：","托盘数":i.rowData.reduce((n,t)=>n+t.托盘数,0),"数量":i.rowData.reduce((n,t)=>n+t.数量,0)}]}))}};o.add=function(){f.open({title:"select Trucking(Please enter batch No. to query)",url:"/Trucking/GetTruckingByOrder",filterParams:{type:"0",orderNo:o.requestParams.orderNo},showFilter:!0,isCheckSingle:!1,height:300,size:"md",columns:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"HIH No.",field:"订单号",filterable:!1},{headerName:"Pi#",field:"Pi#",filterable:!1},{headerName:"Batch",field:"LOTNO",filterable:!1},{headerName:"Pallets",field:"托盘数",filterable:!1},{headerName:"QTY",field:"数量",filterable:!1},],callback:function(n){if(n.length===0){myAlert.message.error("Please select the data!");return}const t=[];n.forEach(n=>{var i=!1;s.forEachNode(t=>{t.data.ID===n.ID&&(i=!0)});i||t.push(n)});t.forEach(n=>n.isNewRow=!0);s.applyTransaction({add:t});const i=[];s.forEachNode(n=>i.push(n.data));s.updateGridOptions({pinnedBottomRowData:[{LOTNO:"TOTAL：","托盘数":i.reduce((n,t)=>n+t.托盘数,0),"数量":i.reduce((n,t)=>n+t.数量,0)}]})}})};o.addAll=function(){f.open({title:"select Trucking(Please enter batch No. to query)",url:"/Trucking/GetTruckingByOrder",filterParams:{type:"1",orderNo:o.requestParams.orderNo},size:"md",showFilter:!0,isCheckSingle:!1,height:300,columns:[{headerCheckboxSelection:!0,checkboxSelection:!0,maxWidth:50,filter:!1,sortable:!1,filterParams:{suppressSorting:!1}},{headerName:"HIH No.",field:"订单号",filterable:!1},{headerName:"Pi#",field:"Pi#",filterable:!1},{headerName:"Batch",field:"LOTNO",filterable:!1},{headerName:"Pallets",field:"托盘数",filterable:!1},{headerName:"QTY",field:"数量",filterable:!1},],callback:function(n){if(n.length===0){myAlert.message.error("Please select the data!");return}const t=[];n.forEach(n=>{var i=!1;s.forEachNode(t=>{t.data.ID===n.ID&&(i=!0)});i||t.push(n)});t.forEach(n=>n.isNewRow=!0);s.applyTransaction({add:t});const i=[];s.forEachNode(n=>i.push(n.data));s.updateGridOptions({pinnedBottomRowData:[{LOTNO:"TOTAL：","托盘数":i.reduce((n,t)=>n+t.托盘数,0),"数量":i.reduce((n,t)=>n+t.数量,0)}]})}})};o.editRemark=function(){var n=e.open({templateUrl:"/Trucking/EditRemark",controller:"orderManagement.trucking.editRemark as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{}}}});n.result.then(function(n){o.requestParams.remark=n},function(){}).finally(function(){})};o.save=function(){var n=[],t;s.forEachNode(function(t){n.push(t.data)});t=n.map(function(n){return n.ID}).join(",");u({method:"POST",url:"/Trucking/CreateOrEditZCD",data:{items:t,orderNo:o.requestParams.orderNo,zcdNo:o.requestParams.zcdNo}}).then(function(n){n.data.code===200?o.requestParams.zcdNo||(i.zcdNo=n.data.data,o.requestParams.zcdNo=n.data.data):myAlert.message.error(n.data.msg)}).then(function(){u({method:"POST",url:"/Trucking/EditZCDInfo",data:{ZCDNo:i.zcdNo,country:o.requestParams.country,zip:o.requestParams.zip,city:o.requestParams.city,company:o.requestParams.company,address:o.requestParams.address,remark:o.requestParams.remark,transportAgent:o.requestParams.transportAgent}}).then(function(n){n.data.code===200?(o.initGridData(),myAlert.message.success(n.data.msg)):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)})}).catch(function(n){console.error("Error fetching data:",n)})};o.delete=function(){var n=s.getSelectedRows(),i,t;if(n.length===0){myAlert.message.error("Please select the data!");return}for(i=!0,t=0;t<n.length;t++)if(n[t].操作完成时间){i=!1;break}if(!i){myAlert.message.error("The batch selection operation has been completed and cannot be deleted!");return}s.applyTransaction({remove:n});const r=[];s.forEachNode(n=>r.push(n.data));s.updateGridOptions({pinnedBottomRowData:[{LOTNO:"TOTAL：","托盘数":r.reduce((n,t)=>n+t.托盘数,0),"数量":r.reduce((n,t)=>n+t.数量,0)}]})}}]);angular.module("myApp").controller("orderManagement.trucking.editRemark",["$scope","$uibModalInstance","options","$timeout",function(n,t,i,r){var u=this;u.dismiss=function(){t.dismiss("cancel")};u.requestParams={};u.options=[{text:"Customer informed on X/X/2022, waiting for his feedback. X/X/2022"},{text:"Customer informed on X/X/2022, waiting for his desired delivery plan. X/X/2022"},{text:"Customer informed on X/X/2022, batch is not yet ready. X/X/2022"},{text:"Customer replied on X/X/2022. HIH is looking for a truck. X/X/2022"},{text:"ETA of the batch is X/X/2022. X/X/2022"},{text:"Waiting CMR"},{text:"Waiting POD"}];u.init=function(){r(function(){$("#date").datepicker({autoclose:!0,clearBtn:!0,format:"dd/mm/yyyy"});$("#date").datepicker("setDate",new Date)},0)};u.modelChanged=function(){var t=new Date,r=t.getFullYear(),f=t.getMonth()+1,e=t.getDate(),i,n;u.requestParams.model!=null&&u.requestParams.model.endsWith("X/X/2022")?(i="",u.requestParams.checkBoxdate&&(i=u.requestParams.date),n=u.requestParams.model.substring(0,u.requestParams.model.length-9)+e+"/"+f+"/"+r,n=n.replace("X/X/2022",i),u.requestParams.remarks=n):u.requestParams.remarks=u.requestParams.model};u.dateChanged=function(){var n;if(u.requestParams.model!=null)if(n="",u.requestParams.checkBoxdate&&(n=u.requestParams.date),u.requestParams.model.endsWith("X/X/2022")){var t=new Date,r=t.getFullYear(),f=t.getMonth()+1,e=t.getDate(),i=u.requestParams.model.substring(0,u.requestParams.model.length-9)+e+"/"+f+"/"+r;i=i.replace("X/X/2022",n);u.requestParams.remarks=i}else u.requestParams.remarks=u.requestParams.model};u.save=function(){t.close(u.requestParams.remarks)}}]);angular.module("myApp").controller("orderManagement.Trucking",["$scope","$uibModal","$timeout","$http",function(n,t,i,r){function l(){}var u=this,h,f,e,c;n.$on("$viewContentLoaded",function(){App.initAjax()});u.saving=!1;u.requestParams={piNo:"",orderNo:""};h={Create:"Create",Edit:"Edit",View:"View"};const o=[{headerCheckboxSelection:!1,checkboxSelection:!0,maxWidth:50,filter:!1,field:"ID",sortable:!1,filterParams:{suppressSorting:!1},pinned:"left"},];e=!1;u.gridData=function(){f.deselectAll();f.refreshServerSide();e=!1;f.onFilterChanged()};u.editRemark=function(){var n=f.getSelectedRows(),r,u,i,e;if(n.length==0){myAlert.message.error("Please select the data!");return}for(r=!1,u=!0,i=0;i<n.length;i++){if(n[i].操作完成时间){r=!0;break}if(!n[i].ID){u=!1;break}}if(r){myAlert.message.error("Cannot edit the remark, because the operation has been completed!");return}if(!u){myAlert.message.error("The selection of data has not yet been executed GetData!");return}e=t.open({templateUrl:"/Trucking/EditRemark",controller:"orderManagement.trucking.editRemark as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{}}}});e.result.then(function(t){for(var i=0;i<n.length;i++)f.getRowNode((n[i].RowIndex-1).toString()).setDataValue("备注",t)},function(){}).finally(function(){})};let s=[];u.gridOptions={pagination:!0,paginationPageSize:20,paginationPageSizeSelector:[20,50,100],rowModelType:"serverSide",columnDefs:o,defaultColDef:{sortable:!0,cellClass:"align-right",autoHeaderHeight:!0,wrapHeaderText:!0,cellStyle:function(n){return n.data.modifiedCells&&n.data.modifiedCells[n.colDef.field]?{"background-color":"BlanchedAlmond"}:n.data.ID?null:{color:"red"}},filter:"agSetColumnFilter",filterParams:{values:getUniqueValues,excelMode:"windows",refreshValuesOnOpen:!0,showTooltips:!0},suppressMovable:!0,lockPinned:!0,suppressColumnsToolPanel:!0},tooltipShowDelay:100,rowSelection:"multiple",onCellValueChanged:function(n){n.oldValue===n.newValue&&n.oldValue||(n.data.modifiedCells=n.data.modifiedCells||{},n.data.modifiedCells[n.colDef.field]=!0,e=!0,n.api.refreshCells({rowNodes:[n.node],columns:[n.column],force:!0}))},onCellDoubleClicked:function(n){var f=n.column.colId,i=!1,r;if(n.data.操作完成时间&&(i=!0),f=="装车单号"){if(!n.value){myAlert.message.error("Please select the data with a loading order number!");return}r=t.open({templateUrl:"/Trucking/DLVDetail",controller:"orderManagement.trucking.dlvDetail as vm",backdrop:"static",size:"md",resolve:{options:function(){return{zcdNo:n.value,isCompleted:i}}}});r.result.then(function(){u.gridData()},function(){}).finally(function(){})}},suppressAggFuncInHeader:!0,enableRangeSelection:!0,suppressRowClickSelection:!0,onGridReady:function(n){f=n.api;const t=createDataSource("/Trucking/GetOrderTruckingList2",u.specialFilter);n.api.setGridOption("serverSideDatasource",t)},onFirstDataRendered:l};c={getRows:function(n){var i=Math.floor(n.request.startRow/(n.request.endRow-n.request.startRow))+1,r=n.request.endRow-n.request.startRow,f;const t=n.request.filterModel;f=n.request.sortModel;$.ajax({url:"/Trucking/GetOrderTruckingList2",type:"post",data:{piNo:u.requestParams.piNo,orderNo:u.requestParams.orderNo,pageSize:r,currentPage:i},success:function(i){var r=n.request.sortModel;if(r.length>0&&(i.data=i.data.sort((n,t)=>{for(let i=0;i<r.length;i++){const f=r[i],u=f.colId,e=f.sort==="asc"?1:-1;if(n[u]<t[u])return-1*e;if(n[u]>t[u])return 1*e}return 0})),Object.keys(t).length){const r=i.data.filter(n=>{for(const i in t){const r=t[i];if(r.values&&!r.values.includes(n[i]))return!1}return!0});n.success({rowData:r});return}n.success({rowData:i.data,rowCount:i.count})},error:function(t){t.responseText&&myAlert.message.error(t.responseText);n.failCallback()}})}};u.init=function(){$.ajax({url:`/Trucking/GetColumns`,type:"post",data:{module:"Trucking"},success:function(n){n.code=="200"?(n.data.forEach(n=>{let t={headerName:n.标题,field:n.字段,editable:t=>{const i=n.是否编辑,r=!t.data.操作完成时间,u=t.data.ID;return i&&r&&u}};if(n.otherRules){const i=eval("({"+n.otherRules+"})");t={...t,...i}}o.push(t)}),new agGrid.createGrid(document.getElementById("agGrid"),u.gridOptions)):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})};u.save=function(){u.saving=!0;f.stopEditing();i(()=>{var n=[];if(f.forEachNode(function(t){var r,e,u,i,f,o;if(t.data.modifiedCells&&!t.data.操作完成时间){r={};r.ID=t.data.ID;e=t.data.modifiedCells;for(u in e)i=t.data[u],i&&typeof i=="object"&&!isNaN(Date.parse(i))?(f=new Date(i),o=f.getFullYear()+"-"+("0"+(f.getMonth()+1)).slice(-2)+"-"+("0"+f.getDate()).slice(-2),r[u]=o):r[u]=i;n.push(r)}}),n.length===0){myAlert.message.info("No data has been modified!");u.saving=!1;return}r({method:"POST",url:"/Trucking/UpdateTruckingData",data:{items:JSON.stringify(n)}}).then(function(n){n.data.code===200?(myAlert.message.success("Save successfully！"),u.gridData(),u.saving=!1,e=!1):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)})},500)};u.specialFilter=function(){var n={};return n.piNo=u.requestParams.piNo,n.orderNo=u.requestParams.orderNo,n.lotNo=u.requestParams.lotNo,n};u.search=function(){f.stopEditing();e?myAlert.message.confirm("The modified content has not been saved yet. Are you sure you still want to execute it?",function(n){n&&u.gridData()}):u.gridData()};u.resetting=function(){u.requestParams={};f.setFilterModel(null);u.gridData()};u.getData=function(){myAlert.message.confirm("Are you sure you want to synchronize the Trucking information of the currently entered order number?",function(n){var t,i,e;if(n){if(t=f.getSelectedRows(),i=!1,t.length==0){myAlert.message.error("Please select the data!");return}if(e=t.map(function(n){if(n.ID){i=!0;return}return n.OrderNo}).join(","),i){myAlert.message.error("Please select the order number data that has not been generated with GetData!");return}u.saving=!0;r({method:"POST",url:"/Trucking/GetTruckingData",data:{orderNos:e},headers:{"Content-Type":"application/json"}}).then(function(n){n.data.code===200?(myAlert.message.success("Get date successfully！"),u.gridData()):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)}).finally(function(){u.saving=!1})}})};u.dlv=function(){var n=!1,i=f.getSelectedRows(),r=i.map(function(t){if(t.装车单号){n=!0;myAlert.message.error("The selected row has already generated a loading order and cannot be duplicated!");return}if(!t.ID){n=!0;myAlert.message.error("The selection of data has not yet been executed GetData!");return}return t.ID}).join(","),e,o;if(!n){if(r.length===0){myAlert.message.error("Please select the data!");return}e=i[0].订单号;o=t.open({templateUrl:"/Trucking/DLVDetail",controller:"orderManagement.trucking.dlvDetail as vm",backdrop:"static",size:"md",resolve:{options:function(){return{rowData:i,ids:r,orderNo:e,isCompleted:!1}}}});o.result.then(function(){u.gridData()},function(){}).finally(function(){})}};u.dlvCancel=function(){var e=f.getSelectedRows(),n=!0,t=!0,i=e.map(function(i){return i.装车单号||(n=!1),i.操作完成时间&&(t=!1),i.ID}).join(",");if(!n){myAlert.message.error("The batch number does not have a loading order number!");return}if(!t){myAlert.message.error("The order has been completed!");return}if(i.length===0){myAlert.message.error("Please select the data!");return}myAlert.message.confirm("Are you sure you want to cancel the loading order?",function(n){n&&r({method:"POST",url:"/Trucking/CancelZCD",data:{items:i}}).then(function(n){n.data.code===200?(myAlert.message.success("Successfully cancelled loading order！"),u.gridData()):myAlert.message.error(n.data.msg)}).catch(function(n){console.error("Error fetching data:",n)})})};u.dlvDetail=function(){var n=f.getSelectedRows(),i,r,e;if(n.length==0){myAlert.message.error("Please select the data!");return}if(i=n[0].装车单号,!i){myAlert.message.error("Please select the data with a loading order number!");return}r=!1;n[0].操作完成时间&&(r=!0);e=t.open({templateUrl:"/Trucking/DLVDetail",controller:"orderManagement.trucking.dlvDetail as vm",backdrop:"static",size:"md",resolve:{options:function(){return{zcdNo:i,isCompleted:r}}}});e.result.then(function(){u.gridData()},function(){}).finally(function(){})}}]);angular.module("myApp").controller("warehouse.OrderManagement.tus.customTUSReport",["$scope","$uibModalInstance","options","$http",function(n,t,i,r){var u=this;u.cancel=function(){t.dismiss("cancel")};u.request={};u.init=function(){$("#datetimepicker2").datetimepicker({format:"YYYY-MM",useCurrent:!1}).on("dp.change",function(n){u.request.cmrDate=n.date.format("YYYY-MM")})};u.isBusy=!1;u.save=function(){if(u.isBusy=!0,u.request.cmrDate==undefined||u.request.cmrDate==""){myAlert.message.error("Please enter the date.");u.isBusy=!1;return}myAlert.message.confirm("Are you sure you want to send the Email Report?",function(t){t?r({method:"POST",url:"/OrdersTUS/EditTUSReport",data:{strDate:u.request.cmrDate}}).then(function(n){n.data.code===200?(myAlert.message.success(n.data.msg),u.cancel()):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)}).finally(function(){u.isBusy=!1}):(u.isBusy=!1,n.$apply())})}}]);angular.module("myApp").controller("usermanage.changepassword",["$scope","$uibModal","$timeout","$http",function(n){var t=this;n.$on("$viewContentLoaded",function(){App.initAjax()});t.requestParams={};const f=document.getElementById("oldPassword"),r=document.getElementById("newPassword"),i=document.getElementById("confirmNewPassword"),u=document.getElementById("passwordMismatch");t.init=function(){console.log("init");r.addEventListener("input",t.checkPasswords);i.addEventListener("input",t.checkPasswords)};t.checkPasswords=function(){return console.log("checkPasswords"),r.value!==i.value?(i.classList.add("is-invalid"),u.style.display="block",!1):(i.classList.remove("is-invalid"),u.style.display="none",!0)};t.changePassword=function(){r.value&&r.value===i.value&&myAlert.message.confirm("Are you sure you want to change your password?",function(n){n&&$.ajax({method:"POST",url:"/UserManage/ChangePassword",data:{oldPassword:f.value,newPassword:r.value,confirmPassword:i.value}}).then(function(n){n.code===200?(myAlert.message.success(n.msg),setTimeout(function(){location.reload()},3e3)):myAlert.message.error(n.msg)})})}}]);angular.module("myApp").controller("warehouse.inventory.emailReport",["$scope","$uibModalInstance","options","$http",function(n,t,i,r){var u=this;u.cancel=function(){t.dismiss("cancel")};u.modalTitle=i.modalTitle==undefined?"Email Report":i.modalTitle;u.title=i.title==undefined?"Please enter the CMR date:":i.title;u.request={};u.init=function(){$("#datetimepicker2").datetimepicker({format:"YYYY-MM-DD",useCurrent:!1}).on("dp.change",function(n){u.request.cmrDate=n.date.format("YYYY-MM-DD")})};u.checkPalles=function(){};u.search=function(){};u.isBusy=!1;u.save=function(){if(u.isBusy=!0,u.request.cmrDate==undefined||u.request.cmrDate==""){myAlert.message.error("Please enter the CMR date.");u.isBusy=!1;return}myAlert.message.confirm("Are you sure you want to send the "+u.modalTitle+"?",function(t){t?r({method:"POST",url:"/Inventory/SendReportDetail",data:{emailType:i.emailType,cmrDate:u.request.cmrDate}}).then(function(n){n.data.code===200?(myAlert.message.success(n.data.msg),u.cancel()):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)}).finally(function(){u.isBusy=!1}):(u.isBusy=!1,n.$apply())})};u.specialFilter=function(){return{}}}]);angular.module("myApp").controller("warehouse.inventory.inventory",["$scope","$uibModal","$timeout","$http",function(n,t,i,r){function h(n){const t=new Date(n),i=t.getTimezoneOffset()+480;return new Date(t.getTime()-i*6e4)}function l(n){if(n instanceof Date)return!0;if(typeof n=="string"){const i=/^(\d{4}-\d{2}-\d{2})(?:T\d{2}:\d{2}:\d{2})?$/;if(!i.test(n))return!1;return!0;const t=new Date(n)}return!1}function a(){var n="Inventory",t;return f=="Inventory_non-CSMS"?n="non-CSMS":f=="Inventory_DailyOut"?n="Daily Outbound":f=="Inventory_DailyOutDP"&&(n="Daily Outbound-DDP"),t=h(new Date),n+"_Export"+moment(t).format("YYYYMMDD")+".xlsx"}var u=this,o,f,e,s;n.$on("$viewContentLoaded",function(){App.initAjax()});u.isTSW=!1;u.requestParams={type:"",pallets:0,quantity:0,record:0};u.isXZCK=!1;u.isGridUpDate=!1;u.csCode="";u.whCodeDropdown=[];u.locationDropdown=[];u.classDropdown=[];u.classInventoryDropdown=[];u.classNonCsmsDropdown=[{text:"1001",value:"1001"},{text:"7001",value:"7001"}];u.customStateDropdown=[{text:"Inbound Forecast",value:"入库通知未处理"},{text:"Existing Inventory",value:"库存"},{text:"Outbound Forecast",value:"出库通知未处理"},];f="Inventory";u.initModule="Inventory";u.specialFilter=function(){var n={};return n.lotNos=u.requestParams.lotNoList,n.whCode=u.requestParams.whCode,n.orderNo=u.requestParams.orderNo,n.location=u.requestParams.location,n.type=u.requestParams.type,n.class=u.requestParams.class,n.power=u.requestParams.power,n.piNo=u.requestParams.piNo,n.mbl=u.requestParams.mbl,n.customer=u.requestParams.customer,n.containerNo=u.requestParams.containerNo,n.customState=u.requestParams.customState,n.outboundStartDate=u.requestParams.outboundStartDate,n.outboundEndDate=u.requestParams.outboundEndDate,n.outboundOrderStartDate=u.requestParams.outboundOrderStartDate,n.outboundOrderEndDate=u.requestParams.outboundOrderEndDate,n.inboundEndDate=u.requestParams.inboundEndDate,n.inboundStartDate=u.requestParams.inboundStartDate,n.isNonCsms=u.requestParams.isNonCsms,u.requestParams.isDailyOutbound&&(n.cmrDate=u.requestParams.cmrDate,n.isDailyOutbound=u.requestParams.isDailyOutbound),u.requestParams.isDailyOutDDP&&(n.isDailyOutDDP=u.requestParams.isDailyOutDDP,n.ddpDate=u.requestParams.ddpDate),n};u.init=function(){$.post("/UserSession/GetUserBasic",function(n){u.csCode=n.csCode;u.csCode=="TSW"?(u.isTSW=!0,u.initData(),u.initGrid()):(u.initData(),u.initGrid())})};u.initGrid=function(){$.ajax({url:`/Inventory/InGetColumns`,type:"post",data:{module:f},success:function(n){n.code=="200"?(n.data.forEach(n=>{let t={headerName:n.标题,field:n.字段,editable:()=>n.是否编辑};if(n.otherRules){const i=eval("({"+n.otherRules+"})");t={...t,...i}}e.push(t)}),o?o.updateGridOptions({columnDefs:e}):new agGrid.createGrid(document.getElementById("agGrid"),u.gridOptions)):myAlert.message.error(n.msg)},error:function(n){myAlert.message.error(n.responseText)}})};e=[];u.initData=function(){r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Warehouse Code"}}).then(function(n){n.data.code===200?u.whCodeDropdown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Location"}}).then(function(n){n.data.code===200?u.locationDropdown=n.data.data:myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});r({method:"POST",url:"/Trucking/GetTransportAgentDropdown",data:{fieldName:"Cargo class"}}).then(function(n){n.data.code===200?(u.classDropdown=n.data.data,u.classInventoryDropdown=n.data.data):myAlert.message.error(n.data.msg)}).catch(function(n){console.log(n)});$("#inboundStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#inboundEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#outboundOrderStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#outboundOrderEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#outboundStartDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#outboundEndDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#cmrDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"});$("#ddpDate").datepicker({autoclose:!0,clearBtn:!0,format:"yyyy-mm-dd"})};class c{init(n){this.params=n;this.eGui=document.createElement("div");this.eGui.className="ag-status-name-value";this.updateRowCount();this.params.api.addEventListener("firstDataRendered",()=>{this.updateRowCount()});this.params.api.addEventListener("modelUpdated",()=>{this.updateRowCount()})}getGui(){return this.eGui}refresh(){return this.updateRowCount(),!0}updateRowCount(){this.eGui.innerHTML=`<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Record:${u.requestParams.record}</span>
                <span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Quantity: ${u.requestParams.quantity}</span>
                <span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Pallets: ${u.requestParams.pallets}</span>`}}u.gridOptions={rowModelType:"serverSide",columnDefs:e,paginationPageSize:20,defaultColDef:{sortable:!0,filter:"agSetColumnFilter",filterParams:{values:getUniqueValues,excelMode:"windows",refreshValuesOnOpen:!1},suppressColumnsToolPanel:!0},statusBar:{statusPanels:[{statusPanel:c,align:"left"},]},pagination:!0,suppressMovableColumns:!0,rowSelection:"multiple",enableRangeSelection:!0,onPaginationChanged:function(n){n.newPage&&o.setGridOption("defaultColDef",s())},onGridReady:function(n){o=n.api;const t={getRows:function(n){$.ajax({method:"POST",url:"/Inventory/GetInventorySUMQty",data:u.specialFilter()}).then(function(t){t.code==200?(createDataSourceV2("/Inventory/GetInventoryData",u.specialFilter(),n),u.requestParams.quantity=t.data.pcs,u.requestParams.pallets=t.data.ptls,u.requestParams.record=t.data.record):(u.requestParams.quantity=0,u.requestParams.pallets=0,u.requestParams.record=0,n.fail(),myAlert.message.error(t.msg))}).catch(function(n){console.log(n)})}};n.api.setGridOption("serverSideDatasource",t)}};s=function(){return colDefault={sortable:!0,filter:"agSetColumnFilter",filterParams:{values:getUniqueValues,excelMode:"windows",refreshValuesOnOpen:!1},suppressColumnsToolPanel:!0}};u.gridUpdate=function(n){u.isGridUpDate=!0;n==="nonCsms"&&(u.requestParams.isNonCsms?(f="Inventory_non-CSMS",u.classDropdown=u.classNonCsmsDropdown,u.requestParams.isDailyOutbound=!1,u.requestParams.isDailyOutDDP=!1):(f=u.initModule,u.classDropdown=u.classInventoryDropdown));n==="isDailyOutbound"&&(u.requestParams.isDailyOutbound?(u.requestParams.isNonCsms=!1,u.requestParams.isDailyOutDDP=!1,u.classDropdown=u.classInventoryDropdown,f="Inventory_DailyOut"):(f=u.initModule,u.classDropdown=u.classInventoryDropdown));n==="isDailyOutDDP"&&(u.requestParams.isDailyOutDDP?(u.requestParams.isNonCsms=!1,u.requestParams.isDailyOutbound=!1,u.classDropdown=u.classInventoryDropdown,f="Inventory_DailyOutDP"):(f=u.initModule,u.classDropdown=u.classInventoryDropdown))};u.search=function(){if(u.requestParams.isDailyOutbound&&!u.requestParams.cmrDate){myAlert.message.warn("Please enter CMR date!");return}if(u.requestParams.isDailyOutDDP&&!u.requestParams.ddpDate){myAlert.message.warn("Please enter DDP-CMR receipt date!");return}u.isGridUpDate&&(e=[],u.initGrid());u.isGridUpDate=!1;o.refreshServerSide();o.onFilterChanged()};u.resetting=function(){u.requestParams={};f=u.initModule;u.isGridUpDate=!0;u.search()};u.export=function(){u.busying=!0;r({method:"POST",url:"/Inventory/GetInventoryDataExcel",data:u.specialFilter()}).then(function(n){var i,u;if(n.data.code===200){var f=n.data.data,r=new ExcelJS.Workbook,t=r.addWorksheet("Sheet 1"),o=e.map(function(n){return{header:n.headerName,key:n.field}});t.columns=o;f.forEach(function(n){t.addRow(n)});e.forEach(function(n,i){var r=0;r=Math.max(r,n.headerName.length);t.getColumn(i+1).width=r+5});t.eachRow({includeEmpty:!0},function(n){n.eachCell({includeEmpty:!0},function(n){if(n.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},n.font={name:"Arial",size:10},l(n.value)){n.value instanceof Date||(n.value=h(n.value));const t=new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",timeZone:"Asia/Shanghai"});n.value=t.format(n.value)}})});i=t.getRow(1);i.font={name:"Arial",size:10,bold:!0};i.alignment={horizontal:"center"};u=a();r.xlsx.writeBuffer().then(function(n){var i=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),t=document.createElement("a");t.href=URL.createObjectURL(i);t.download=u;t.click()})}}).catch(function(n){console.log(n)}).finally(function(){u.busying=!1})};u.email=function(n){var r="Please enter the CMR date:",i;n=="fgi"?modalTitle="FGI Daily Outbound Report":n=="rma"&&(modalTitle="RMA Daily Outbound Report");i=t.open({templateUrl:"/Inventory/EmailReport",controller:"warehouse.inventory.emailReport as vm",backdrop:"static",size:"lg",resolve:{options:function(){return{emailType:n,title:r,modalTitle:modalTitle}}}});i.result.then(function(){},function(){}).finally(function(){})}}]);