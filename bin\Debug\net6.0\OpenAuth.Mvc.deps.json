{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"OpenAuth.Mvc/1.0.0": {"dependencies": {"Autofac.Extensions.DependencyInjection": "6.0.0", "IdentityServer4.AccessTokenValidation": "3.0.1", "Infrastructure": "1.0.0", "Microsoft.AspNet.Web.Optimization": "1.1.3", "Microsoft.AspNet.Web.Optimization.zh-Hans": "1.1.3", "Microsoft.Extensions.Logging.Log4Net.AspNetCore": "3.1.0", "Microsoft.VisualStudio.Web.CodeGeneration.Design": "3.1.0", "OpenAuth.App": "1.0.0", "OpenAuth.Repository": "1.0.0"}, "runtime": {"OpenAuth.Mvc.dll": {}}}, "aliyun-net-sdk-core/1.5.11": {"runtime": {"lib/netstandard2.0/aliyun-net-sdk-core.dll": {"assemblyVersion": "1.5.11.0", "fileVersion": "1.5.11.0"}}}, "aliyun-net-sdk-sts/3.1.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "aliyun-net-sdk-core": "1.5.11"}, "runtime": {"lib/netstandard2.0/aliyun-net-sdk-sts.dll": {"assemblyVersion": "3.1.2.0", "fileVersion": "3.1.2.0"}}}, "Aliyun.OSS.SDK.NetCore/2.14.1": {"runtime": {"lib/netstandard2.0/Aliyun.OSS.Core.dll": {"assemblyVersion": "2.14.1.0", "fileVersion": "2.14.1.0"}}}, "AngleSharp/1.1.2": {"dependencies": {"System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/net6.0/AngleSharp.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "Antlr/3.4.1.9004": {"runtime": {"lib/Antlr3.Runtime.dll": {"assemblyVersion": "3.4.1.9004", "fileVersion": "3.4.1.9004"}}}, "Autofac/5.2.0": {"runtime": {"lib/netstandard2.1/Autofac.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.0"}}}, "Autofac.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Autofac": "5.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac.Extras.Quartz/5.1.0": {"dependencies": {"Autofac": "5.2.0", "Quartz": "3.0.7"}, "runtime": {"lib/netstandard2.1/Autofac.Extras.Quartz.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.1.0.0"}}}, "AutoMapper/9.0.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "BouncyCastle.Cryptography/2.4.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.4.0.33771"}}}, "Castle.Core/4.4.0": {"dependencies": {"NETStandard.Library": "2.0.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.5/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.4.0.0"}}}, "Enums.NET/4.0.1": {"runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.1.0"}}}, "EnyimMemcachedCore/2.5.3": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0", "Newtonsoft.Json.Bson": "1.0.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/EnyimMemcachedCore.dll": {"assemblyVersion": "2.5.3.0", "fileVersion": "2.5.3.0"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"runtime": {"lib/net6.0/ExtendedNumerics.BigDecimal.dll": {"assemblyVersion": "2025.1001.2.129", "fileVersion": "2025.1001.2.129"}}}, "Google.Protobuf/3.5.1": {"dependencies": {"NETStandard.Library": "2.0.0"}, "runtime": {"lib/netstandard1.0/Google.Protobuf.dll": {"assemblyVersion": "3.5.1.0", "fileVersion": "3.5.1.0"}}}, "IdentityModel/4.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/IdentityModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IdentityModel.AspNetCore.OAuth2Introspection/4.0.1": {"dependencies": {"IdentityModel": "4.0.0"}, "runtime": {"lib/netcoreapp3.0/IdentityModel.AspNetCore.OAuth2Introspection.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.1.0"}}}, "IdentityServer4.AccessTokenValidation/3.0.1": {"dependencies": {"IdentityModel.AspNetCore.OAuth2Introspection": "4.0.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "3.1.2"}, "runtime": {"lib/netcoreapp3.0/IdentityServer4.AccessTokenValidation.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.1.0"}}}, "iTextSharp/5.5.13.4": {"dependencies": {"BouncyCastle.Cryptography": "2.4.0"}, "runtime": {"lib/net461/itextsharp.dll": {"assemblyVersion": "5.5.13.4", "fileVersion": "5.5.13.4"}}}, "log4net/2.0.12": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "2.0.12.0", "fileVersion": "2.0.12.0"}}}, "MathNet.Numerics.Signed/5.0.0": {"runtime": {"lib/net6.0/MathNet.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNet.Web.Optimization/1.1.3": {"dependencies": {"Microsoft.Web.Infrastructure": "1.0.0", "WebGrease": "1.5.2"}, "runtime": {"lib/net40/System.Web.Optimization.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.40211.0"}}}, "Microsoft.AspNet.Web.Optimization.zh-Hans/1.1.3": {"dependencies": {"Microsoft.AspNet.Web.Optimization": "1.1.3"}, "resources": {"lib/net40/zh-Hans/System.Web.Optimization.resources.dll": {"locale": "zh-Hans"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "3.1.2.0", "fileVersion": "3.100.220.6801"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/3.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"assemblyVersion": "3.1.2.0", "fileVersion": "3.100.220.6801"}}}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.Razor/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Razor.Language/3.1.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56505"}}}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "2.2.0", "Microsoft.AspNetCore.Razor": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.CodeAnalysis.Analyzers/2.9.4": {}, "Microsoft.CodeAnalysis.Common/3.3.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "2.9.4", "System.Collections.Immutable": "6.0.0", "System.Memory": "4.5.3", "System.Reflection.Metadata": "1.6.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.300.119.46211"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/3.3.1": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.3.1"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.300.119.46211"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/3.3.1": {"dependencies": {"Microsoft.CodeAnalysis.CSharp": "3.3.1", "Microsoft.CodeAnalysis.Common": "3.3.1", "Microsoft.CodeAnalysis.Workspaces.Common": "3.3.1"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.300.119.46211"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/3.1.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "3.1.0", "Microsoft.CodeAnalysis.CSharp": "3.3.1", "Microsoft.CodeAnalysis.Common": "3.3.1"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56505"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/3.3.1": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.3.1", "System.Composition": "1.0.31"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.300.119.46211"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeCoverage/16.5.0": {"runtime": {"lib/netcoreapp1.0/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "1*******", "fileVersion": "16.0.28223.3002"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/2.1.7": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Runtime.Caching": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite/7.0.5": {"dependencies": {"Microsoft.Data.Sqlite.Core": "7.0.5", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.4"}}, "Microsoft.Data.Sqlite.Core/7.0.5": {"dependencies": {"SQLitePCLRaw.core": "2.1.4"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "7.0.5.0", "fileVersion": "7.0.523.16503"}}}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/6.0.27": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.27", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.27", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.27.0", "fileVersion": "6.0.2724.6609"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.27": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.27.0", "fileVersion": "6.0.2724.6609"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.27": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.27": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.27", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.27.0", "fileVersion": "6.0.2724.6609"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.27": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.EntityFrameworkCore.Relational": "6.0.27"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "6.0.27.0", "fileVersion": "6.0.2724.6609"}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.DependencyModel/8.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileSystemGlobbing": "5.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {}, "Microsoft.Extensions.Hosting/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.Configuration.CommandLine": "5.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "5.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "5.0.0", "Microsoft.Extensions.Configuration.Json": "5.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "5.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Physical": "5.0.0", "Microsoft.Extensions.Hosting.Abstractions": "5.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "5.0.0", "Microsoft.Extensions.Logging.Console": "5.0.0", "Microsoft.Extensions.Logging.Debug": "5.0.0", "Microsoft.Extensions.Logging.EventLog": "5.0.0", "Microsoft.Extensions.Logging.EventSource": "5.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Http/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0"}}, "Microsoft.Extensions.Logging.Console/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "5.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0"}}, "Microsoft.Extensions.Logging.Debug/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Logging.EventLog/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.EventLog": "5.0.1"}}, "Microsoft.Extensions.Logging.EventSource/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "5.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.Logging": "6.0.0", "System.Xml.XPath.XDocument": "4.3.0", "log4net": "2.0.12"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Logging.Log4Net.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.8.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NET.Test.Sdk/16.5.0": {"dependencies": {"Microsoft.CodeCoverage": "16.5.0", "Microsoft.TestPlatform.TestHost": "16.5.0"}}, "Microsoft.NETCore.Platforms/5.0.1": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.TestPlatform.ObjectModel/16.5.0": {"dependencies": {"NuGet.Frameworks": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}, "lib/netstandard2.0/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}, "lib/netstandard2.0/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netstandard2.0/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netstandard2.0/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netstandard2.0/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/16.5.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "16.5.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netcoreapp2.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}, "lib/netcoreapp2.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}, "lib/netcoreapp2.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}, "lib/netcoreapp2.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}, "lib/netcoreapp2.1/testhost.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}, "resources": {"lib/netcoreapp2.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp2.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp2.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp2.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp2.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp2.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp2.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp2.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp2.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp2.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp2.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp2.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Web.CodeGeneration/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Contracts/3.1.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGeneration.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/3.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "3.1.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/3.1.0": {"dependencies": {"Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "3.1.0"}, "runtime": {"lib/netcoreapp3.1/dotnet-aspnet-codegenerator-design.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/3.1.0": {"dependencies": {"Microsoft.VisualStudio.Web.CodeGeneration.Core": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/3.1.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "3.1.0", "Microsoft.AspNetCore.Razor.Runtime": "2.2.0", "Microsoft.CodeAnalysis.CSharp": "3.3.1", "Microsoft.CodeAnalysis.Razor": "3.1.0", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/3.1.0": {"dependencies": {"Microsoft.CodeAnalysis.CSharp.Workspaces": "3.3.1", "Microsoft.VisualStudio.Web.CodeGeneration.Contracts": "3.1.0", "Newtonsoft.Json": "13.0.3", "NuGet.Frameworks": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/3.1.0": {"dependencies": {"Microsoft.VisualStudio.Web.CodeGeneration": "3.1.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.57003"}}}, "Microsoft.Web.Infrastructure/1.0.0": {"runtime": {"lib/net40/Microsoft.Web.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.20105.407"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.Registry.AccessControl/5.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.AccessControl": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Windows.Compatibility/5.0.2": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "Microsoft.Win32.Registry.AccessControl": "5.0.0", "Microsoft.Win32.SystemEvents": "6.0.0", "System.CodeDom": "5.0.0", "System.ComponentModel.Composition": "5.0.0", "System.ComponentModel.Composition.Registration": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Data.DataSetExtensions": "4.5.0", "System.Data.Odbc": "5.0.0", "System.Data.OleDb": "5.0.0", "System.Data.SqlClient": "4.8.1", "System.Diagnostics.EventLog": "5.0.1", "System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.AccountManagement": "5.0.0", "System.DirectoryServices.Protocols": "6.0.2", "System.Drawing.Common": "6.0.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.IO.Packaging": "5.0.0", "System.IO.Pipes.AccessControl": "5.0.0", "System.IO.Ports": "5.0.0", "System.Management": "5.0.0", "System.Reflection.Context": "5.0.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Runtime.Caching": "5.0.0", "System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Permissions": "6.0.0", "System.Security.Principal.Windows": "5.0.0", "System.ServiceModel.Duplex": "4.8.0", "System.ServiceModel.Http": "4.8.0", "System.ServiceModel.NetTcp": "4.8.0", "System.ServiceModel.Primitives": "4.8.0", "System.ServiceModel.Security": "4.8.0", "System.ServiceModel.Syndication": "5.0.0", "System.ServiceProcess.ServiceController": "5.0.0", "System.Speech": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Threading.AccessControl": "5.0.0"}}, "Moq/4.13.1": {"dependencies": {"Castle.Core": "4.4.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Moq.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MySql.Data/8.0.13": {"dependencies": {"Google.Protobuf": "3.5.1", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp2.0/MySql.Data.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.0"}}}, "NETStandard.Library/2.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Npgsql/6.0.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "6.0.10.0", "fileVersion": "6.0.10.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.22": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.27", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.27", "Microsoft.EntityFrameworkCore.Relational": "6.0.27", "Npgsql": "6.0.10"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NPOI/2.7.2": {"dependencies": {"BouncyCastle.Cryptography": "2.4.0", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.9", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Security.Cryptography.Xml": "8.0.2"}, "runtime": {"lib/net6.0/NPOI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NuGet.Frameworks/5.0.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.5923"}}}, "NUnit/3.13.1": {"dependencies": {"NETStandard.Library": "2.0.0"}, "runtime": {"lib/netstandard2.0/nunit.framework.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NUnit3TestAdapter/3.17.0": {"dependencies": {"Microsoft.DotNet.InternalAbstractions": "1.0.0", "System.ComponentModel.EventBasedAsync": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Diagnostics.Process": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "Oracle.EntityFrameworkCore/6.21.130": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.27", "Oracle.ManagedDataAccess.Core": "3.21.130"}, "runtime": {"lib/net6.0/Oracle.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Oracle.ManagedDataAccess.Core/3.21.130": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.2"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Pipelines.Sockets.Unofficial/2.2.0": {"dependencies": {"System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.45337"}}}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.27", "Microsoft.Extensions.DependencyInjection": "6.0.1", "MySqlConnector": "2.2.5"}, "runtime": {"lib/net6.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Quartz/3.0.7": {"dependencies": {"Microsoft.CSharp": "4.5.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Data.Common": "4.3.0", "System.Data.SqlClient": "4.8.1", "System.Net.NameResolution": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "runtime": {"lib/netstandard2.0/Quartz.dll": {"assemblyVersion": "3.0.7.0", "fileVersion": "3.0.7.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/5.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "5.0.0-rtm.20519.4", "runtime.linux-arm64.runtime.native.System.IO.Ports": "5.0.0-rtm.20519.4", "runtime.linux-x64.runtime.native.System.IO.Ports": "5.0.0-rtm.20519.4", "runtime.osx-x64.runtime.native.System.IO.Ports": "5.0.0-rtm.20519.4"}}, "runtime.osx-x64.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.1": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/2.1.9": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.4": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.4", "SQLitePCLRaw.provider.e_sqlite3": "2.1.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.4.1835", "fileVersion": "2.1.4.1835"}}}, "SQLitePCLRaw.core/2.1.4": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.4.1835", "fileVersion": "2.1.4.1835"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.4": {"runtimeTargets": {"runtimes/alpine-arm/native/libe_sqlite3.so": {"rid": "alpine-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-arm64/native/libe_sqlite3.so": {"rid": "alpine-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.4": {"dependencies": {"SQLitePCLRaw.core": "2.1.4"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.4.1835", "fileVersion": "2.1.4.1835"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.Data.Sqlite": "7.0.5", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "6.0.10", "Oracle.ManagedDataAccess.Core": "3.21.130", "SqlSugarCore.Dm": "1.2.0", "SqlSugarCore.Kdbndp": "7.4.0", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SqlSugarCore.Dm/1.2.0": {"dependencies": {"System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netstandard2.0/DmProvider.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.16649"}}}, "SqlSugarCore.Kdbndp/7.4.0": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "8.3.712.0", "fileVersion": "8.3.712.0"}}}, "StackExchange.Redis/2.2.4": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.0", "System.Diagnostics.PerformanceCounter": "6.0.1"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.4.27433"}}}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.CodeDom/5.0.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Composition/5.0.0": {"runtime": {"lib/netcoreapp2.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.ComponentModel.Composition.Registration/5.0.0": {"dependencies": {"System.ComponentModel.Composition": "5.0.0", "System.Reflection.Context": "5.0.0"}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Composition.Registration.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.ComponentModel.EventBasedAsync/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Composition/1.0.31": {"dependencies": {"System.Composition.AttributedModel": "1.0.31", "System.Composition.Convention": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Composition.TypedParts": "1.0.31"}}, "System.Composition.AttributedModel/1.0.31": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Convention/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Convention.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Hosting/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Runtime/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.TypedParts/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.TypedParts.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Data.DataSetExtensions/4.5.0": {}, "System.Data.Odbc/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1"}, "runtime": {"lib/netstandard2.0/System.Data.Odbc.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/freebsd/lib/netcoreapp2.0/System.Data.Odbc.dll": {"rid": "freebsd", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/linux/lib/netcoreapp2.0/System.Data.Odbc.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/osx/lib/netcoreapp2.0/System.Data.Odbc.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp2.0/System.Data.Odbc.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Data.OleDb/5.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.PerformanceCounter": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Data.OleDb.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Data.OleDb.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1523.11507"}}}, "System.Diagnostics.EventLog/5.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.Process/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.AccountManagement/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "System.Configuration.ConfigurationManager": "6.0.0", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.2", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.DirectoryServices.AccountManagement.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.DirectoryServices.AccountManagement.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.DirectoryServices.Protocols/6.0.2": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Formats.Asn1/8.0.1": {"runtime": {"lib/net6.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "6.8.0.11012"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Packaging/5.0.0": {"runtime": {"lib/netstandard2.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.IO.Pipelines/5.0.0": {}, "System.IO.Pipes.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.Ports/5.0.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "runtime.native.System.IO.Ports": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/linux/lib/netstandard2.0/System.IO.Ports.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/osx/lib/netstandard2.0/System.IO.Ports.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netstandard2.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Management/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.Win32.Registry": "5.0.0", "System.CodeDom": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Memory/4.5.3": {}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Private.ServiceModel/4.8.0": {"dependencies": {"System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "8.0.2", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Context/5.0.0": {"runtime": {"lib/netstandard2.1/System.Reflection.Context.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.5.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/5.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Serialization.Xml/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "8.0.1"}}, "System.Security.Cryptography.Pkcs/8.0.1": {"dependencies": {"System.Formats.Asn1": "8.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.Xml/8.0.2": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.8.0": {"dependencies": {"System.Private.ServiceModel": "4.8.0", "System.ServiceModel.Primitives": "4.8.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}}}, "System.ServiceModel.Http/4.8.0": {"dependencies": {"System.Private.ServiceModel": "4.8.0", "System.ServiceModel.Primitives": "4.8.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Http.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}}}, "System.ServiceModel.NetTcp/4.8.0": {"dependencies": {"System.Private.ServiceModel": "4.8.0", "System.ServiceModel.Primitives": "4.8.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}}}, "System.ServiceModel.Primitives/4.8.0": {"dependencies": {"System.Private.ServiceModel": "4.8.0"}, "runtime": {"lib/netcoreapp2.1/System.ServiceModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}, "lib/netcoreapp2.1/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}}}, "System.ServiceModel.Security/4.8.0": {"dependencies": {"System.Private.ServiceModel": "4.8.0", "System.ServiceModel.Primitives": "4.8.0"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Security.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.20.51907"}}}, "System.ServiceModel.Syndication/5.0.0": {"runtime": {"lib/netstandard2.0/System.ServiceModel.Syndication.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.ServiceProcess.ServiceController/5.0.0": {"dependencies": {"System.Diagnostics.EventLog": "5.0.1"}, "runtime": {"lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Speech/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Speech.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.321.7212"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Speech.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.321.7212"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.5": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Threading.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.1", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.5.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XDocument/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XPath": "4.3.0"}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "WebGrease/1.5.2": {"dependencies": {"Antlr": "3.4.1.9004", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/WebGrease.dll": {"assemblyVersion": "1.5.2.14234", "fileVersion": "1.5.2.14234"}}}, "Z.EntityFramework.Extensions.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.27", "System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/net6.0/Z.EntityFramework.Extensions.EFCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Z.EntityFramework.Plus.EFCore/*********": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.27", "Z.EntityFramework.Extensions.EFCore": "*********", "Z.Expressions.Eval": "6.1.1"}, "runtime": {"lib/net6.0/Z.EntityFramework.Plus.EFCore.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Z.Expressions.Eval/6.1.1": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "6.0.1", "System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/net6.0/Z.Expressions.Eval.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Infrastructure/1.0.0": {"dependencies": {"AutoMapper": "9.0.0", "EnyimMemcachedCore": "2.5.3", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Hosting": "5.0.0", "Microsoft.NET.Test.Sdk": "16.5.0", "NUnit": "3.13.1", "NUnit3TestAdapter": "3.17.0", "Newtonsoft.Json": "13.0.3", "SixLabors.ImageSharp": "2.1.9", "SqlSugarCore": "*********", "StackExchange.Redis": "2.2.4", "log4net": "2.0.12"}, "runtime": {"Infrastructure.dll": {}}}, "OpenAuth.App/1.0.0": {"dependencies": {"Aliyun.OSS.SDK.NetCore": "2.14.1", "Autofac": "5.2.0", "Autofac.Extensions.DependencyInjection": "6.0.0", "Autofac.Extras.Quartz": "5.1.0", "Infrastructure": "1.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "3.1.2", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "3.1.2", "Microsoft.Extensions.DependencyModel": "8.0.2", "Microsoft.Extensions.Http": "5.0.0", "Microsoft.Extensions.Logging.Log4Net.AspNetCore": "3.1.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "5.0.0", "Microsoft.NET.Test.Sdk": "16.5.0", "Microsoft.Windows.Compatibility": "5.0.2", "Moq": "4.13.1", "NPOI": "2.7.2", "NUnit": "3.13.1", "NUnit3TestAdapter": "3.17.0", "OpenAuth.Repository": "1.0.0", "Quartz": "3.0.7", "aliyun-net-sdk-sts": "3.1.2"}, "runtime": {"OpenAuth.App.dll": {}}}, "OpenAuth.Repository/1.0.0": {"dependencies": {"AngleSharp": "1.1.2", "Autofac": "5.2.0", "Autofac.Extensions.DependencyInjection": "6.0.0", "Infrastructure": "1.0.0", "Microsoft.EntityFrameworkCore.Relational": "6.0.27", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.27", "Microsoft.NET.Test.Sdk": "16.5.0", "Moq": "4.13.1", "MySql.Data": "8.0.13", "NUnit": "3.13.1", "NUnit3TestAdapter": "3.17.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.22", "Oracle.EntityFrameworkCore": "6.21.130", "Oracle.ManagedDataAccess.Core": "3.21.130", "Pomelo.EntityFrameworkCore.MySql": "6.0.2", "SqlSugarCore": "*********", "Z.EntityFramework.Plus.EFCore": "*********", "iTextSharp": "5.5.13.4"}, "runtime": {"OpenAuth.Repository.dll": {}}}, "Aspose.Cells/18.4.0.0": {"runtime": {"Aspose.Cells.dll": {"assemblyVersion": "18.4.0.0", "fileVersion": "18.4.0.0"}}}}}, "libraries": {"OpenAuth.Mvc/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "aliyun-net-sdk-core/1.5.11": {"type": "package", "serviceable": true, "sha512": "sha512-RoirVlkArmE6dEqOZO+Uv5MhwKYZss5f6N/auRUT1dg0yR0mj6A8DWJzFKVy8W+sox19BWH1uZg+3dc6o9cFXg==", "path": "aliyun-net-sdk-core/1.5.11", "hashPath": "aliyun-net-sdk-core.1.5.11.nupkg.sha512"}, "aliyun-net-sdk-sts/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-GStKhc6CpUZslOtmI3E3dUvqDw8MGNs7cPxM23M4PxvfqEoAlmKaIfSmDSNBO1RNMIp5jxmOp+1ZPSPV2N09gw==", "path": "aliyun-net-sdk-sts/3.1.2", "hashPath": "aliyun-net-sdk-sts.3.1.2.nupkg.sha512"}, "Aliyun.OSS.SDK.NetCore/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-UOI6I8Uw8A7pFh2GS6CSpJuhmcHH45k6asex8SRnZed2WWBXeGEaBFyv3HsMJNp+B6gPqyN079sQHWenGd3Y7g==", "path": "aliyun.oss.sdk.netcore/2.14.1", "hashPath": "aliyun.oss.sdk.netcore.2.14.1.nupkg.sha512"}, "AngleSharp/1.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-aRFpAqixbuj1Vmqy2hsWPF0PJygo1SfjvmpBvVWZv6i+/u+C/L4wDdwFIzyCGUbjqr61NsZdPNPqDE8wlmG2qA==", "path": "anglesharp/1.1.2", "hashPath": "anglesharp.1.1.2.nupkg.sha512"}, "Antlr/3.4.1.9004": {"type": "package", "serviceable": true, "sha512": "sha512-c1S+HBE+KYA5EBxtn25LEK02hHPH/tDQ6RviUTTCJpZIPoputtn8ArsQJy9lVJWZOnw37ufByO2Fmf1M8wpr8Q==", "path": "antlr/3.4.1.9004", "hashPath": "antlr.3.4.1.9004.nupkg.sha512"}, "Autofac/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8dBH0dsv75uDzl7Sw+HkhKDPUw2eXnlMjcSVMH+tLo2s67MpTKGyDj1pDcpR+IF2u4YRs0s3/x7R88YJzIWvg==", "path": "autofac/5.2.0", "hashPath": "autofac.5.2.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+1WNYENm4s/bSBrmTy2sD0zs6MCiCAlUaEbHJ+y7fKpXKqnFl4oTkIkUoDKlp05dl1VoAASBUJocaKKJa1KPCw==", "path": "autofac.extensions.dependencyinjection/6.0.0", "hashPath": "autofac.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Autofac.Extras.Quartz/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-YBqxH1CiS5mWPlQydQ05h+1+lNoWgaUv62+X+FxT1PH45u9xRlrsj6Eky2GUmNGkgtfJgiHlGecQfOAiNIkmsQ==", "path": "autofac.extras.quartz/5.1.0", "hashPath": "autofac.extras.quartz.5.1.0.nupkg.sha512"}, "AutoMapper/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xCqvoxT4HIrNY/xlXG9W+BA/awdrhWvMTKTK/igkGSRbhOhpl3Q8O8Gxlhzjc9JsYqE7sS6AxgyuUUvZ6R5/Bw==", "path": "automapper/9.0.0", "hashPath": "automapper.9.0.0.nupkg.sha512"}, "BouncyCastle.Cryptography/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SwXsAV3sMvAU/Nn31pbjhWurYSjJ+/giI/0n6tCrYoupEK34iIHCuk3STAd9fx8yudM85KkLSVdn951vTng/vQ==", "path": "bouncycastle.cryptography/2.4.0", "hashPath": "bouncycastle.cryptography.2.4.0.nupkg.sha512"}, "Castle.Core/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-b5rRL5zeaau1y/5hIbI+6mGw3cwun16YjkHZnV9RRT5UyUIFsgLmNXJ0YnIN9p8Hw7K7AbG1q1UclQVU3DinAQ==", "path": "castle.core/4.4.0", "hashPath": "castle.core.4.4.0.nupkg.sha512"}, "Enums.NET/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "path": "enums.net/4.0.1", "hashPath": "enums.net.4.0.1.nupkg.sha512"}, "EnyimMemcachedCore/2.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-gY1m3LhUEMOLx3L6ADlODPIvnlJoVz7zRBeK9rjhzYsBtk/q6pefl0DcytGAlNPgCKzCGdqFKb0OjsuVSII1jw==", "path": "enyimmemcachedcore/2.5.3", "hashPath": "enyimmemcachedcore.2.5.3.nupkg.sha512"}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "serviceable": true, "sha512": "sha512-+woGT1lsBtwkntOpx2EZbdbySv0aWPefE0vrfvclxVdbi4oa2bbtphFPWgMiQe+kRCPICbfFJwp6w1DuR7Ge2Q==", "path": "extendednumerics.bigdecimal/2025.1001.2.129", "hashPath": "extendednumerics.bigdecimal.2025.1001.2.129.nupkg.sha512"}, "Google.Protobuf/3.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-f2k1VNaB9bfvEsvARzzEL1TZiIpL33KKK3JMH7UANlPlJVptuvsk4qpBZEnz0pORWZOdUHlVwMQuUzFqjJYCxA==", "path": "google.protobuf/3.5.1", "hashPath": "google.protobuf.3.5.1.nupkg.sha512"}, "IdentityModel/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y7i2fezKKKnmxdENfEOpby4Es84e76D16SYcxWKggYraFOvvfKfQAn5KGuuR1X2h1aTrlKZy0lct0NxHg4aVVw==", "path": "identitymodel/4.0.0", "hashPath": "identitymodel.4.0.0.nupkg.sha512"}, "IdentityModel.AspNetCore.OAuth2Introspection/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZNdMZMaj9fqR3j50vYsu+1U3QGd6n8+fqwf+a8mCTcmXGor+HgFDfdq0mM34bsmD6uEgAQup7sv2ZW5kR36dbA==", "path": "identitymodel.aspnetcore.oauth2introspection/4.0.1", "hashPath": "identitymodel.aspnetcore.oauth2introspection.4.0.1.nupkg.sha512"}, "IdentityServer4.AccessTokenValidation/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qu/M6UyN4o9NVep7q545Ms7hYAnsQqSdLbN1Fjjrn4m35lyBfeQPSSNzDryAKHbodyWOQfHaOqKEyMEJQ5Rpgw==", "path": "identityserver4.accesstokenvalidation/3.0.1", "hashPath": "identityserver4.accesstokenvalidation.3.0.1.nupkg.sha512"}, "iTextSharp/5.5.13.4": {"type": "package", "serviceable": true, "sha512": "sha512-/cvCNv8AJ+XuD99u4NfHSSxkBJKTdvP36wcfQF9V1Cjzi1ycyFSa4vJ64a3DhFVUTKVO60WF8OmAqngQDTHgYA==", "path": "itextsharp/5.5.13.4", "hashPath": "itextsharp.5.5.13.4.nupkg.sha512"}, "log4net/2.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-9P67BCftJ7KG+B7rNOM1A9KczUwyEDed6zbAddy5Cj/73xVkzi+rEAHeOgUnW5wDqy1JFlY8+oTP0m1PgJ03Tg==", "path": "log4net/2.0.12", "hashPath": "log4net.2.0.12.nupkg.sha512"}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PSrHBVMf41SjbhlnpOMnoir8YgkyEJ6/nwxvjYpH+vJCexNcx2ms6zRww5yLVqLet1xLJgZ39swtKRTLhWdnAw==", "path": "mathnet.numerics.signed/5.0.0", "hashPath": "mathnet.numerics.signed.5.0.0.nupkg.sha512"}, "Microsoft.AspNet.Web.Optimization/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-TWKKpTWEdB8jXp2Vl2TXNKW30GBQg6nnh0y8iZWmGY5c1XY4f649vfDslNHsCsvk9JZxc1Rkm/UGDdfq4gjWRQ==", "path": "microsoft.aspnet.web.optimization/1.1.3", "hashPath": "microsoft.aspnet.web.optimization.1.1.3.nupkg.sha512"}, "Microsoft.AspNet.Web.Optimization.zh-Hans/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-DGchEWJ8tvcj11ZD9zd011VsV8d5BDMGN4n8FbGv4oZwswQ1Ox11bGSoJsEUY8mtIMGb8CODQGXOc1ME7+aF7g==", "path": "microsoft.aspnet.web.optimization.zh-hans/1.1.3", "hashPath": "microsoft.aspnet.web.optimization.zh-hans.1.1.3.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-hBgNbsvTgk8svtlCWkKXb1z8c0M29vjDKNJFAifoyZiIUYuv/Rn/hqb7gg8QuLW15NeG9r1PlLmoArdSjTHHZA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/3.1.2", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.3.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/3.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-pNfzXFQkLCEiaZJ2Zj6kYagsShEu66pKi0ecNot8j05F8wfsgHFzqFSt0bWF3Rf2z8GbBOrZzqczsN++aW1m5Q==", "path": "microsoft.aspnetcore.authentication.openidconnect/3.1.2", "hashPath": "microsoft.aspnetcore.authentication.openidconnect.3.1.2.nupkg.sha512"}, "Microsoft.AspNetCore.Html.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y4rs5aMEXY8G7wJo5S3EEt6ltqyOTr/qOeZzfn+hw/fuQj5GppGckMY5psGLETo1U9hcT5MmAhaT5xtusM1b5g==", "path": "microsoft.aspnetcore.html.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.html.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-V54PIyDCFl8COnTp9gezNHpUNHk7F9UnerGeZy3UfbnwYvfzbo+ipqQmSgeoESH8e0JvKhRTyQyZquW2EPtCmg==", "path": "microsoft.aspnetcore.razor/2.2.0", "hashPath": "microsoft.aspnetcore.razor.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-e/atqZ5CzmJWuG/yaYOzbWNON+oqNKfk1M/xTYW+hje/RoiUUjVP88wrX1s9rEHzaAU4UljIOSvMxLABc2X2gg==", "path": "microsoft.aspnetcore.razor.language/3.1.0", "hashPath": "microsoft.aspnetcore.razor.language.3.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Runtime/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7YqK+H61lN6yj9RiQUko7oaOhKtRR9Q/kBcoWNRemhJdTIWOh1OmdvJKzZrMWOlff3BAjejkPQm+0V0qXk+B1w==", "path": "microsoft.aspnetcore.razor.runtime/2.2.0", "hashPath": "microsoft.aspnetcore.razor.runtime.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/2.9.4": {"type": "package", "serviceable": true, "sha512": "sha512-alIJhS0VUg/7x5AsHEoovh/wRZ0RfCSS7k5pDSqpRLTyuMTtRgj6OJJPRApRhJHOGYYsLakf1hKeXFoDwKwNkg==", "path": "microsoft.codeanalysis.analyzers/2.9.4", "hashPath": "microsoft.codeanalysis.analyzers.2.9.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N5yQdGy+M4kimVG7hwCeGTCfgYjK2o5b/Shumkb/rCC+/SAkvP1HUAYK+vxPFS7dLJNtXLRsmPHKj3fnyNWnrw==", "path": "microsoft.codeanalysis.common/3.3.1", "hashPath": "microsoft.codeanalysis.common.3.3.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-W<PERSON><PERSON><PERSON>THem38H6VJ98x2Ssq0fweakJHnHYl7vbG8ARnsAwLoJKCQCy78EeY1oRrCKG42j0v6JVljKkeqSDA28UA==", "path": "microsoft.codeanalysis.csharp/3.3.1", "hashPath": "microsoft.codeanalysis.csharp.3.3.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-dHs/UyfLgzsVC4FjTi/x+H+yQifgOnpe3rSN8GwkHWjnidePZ3kSqr1JHmFDf5HTQEydYwrwCAfQ0JSzhsEqDA==", "path": "microsoft.codeanalysis.csharp.workspaces/3.3.1", "hashPath": "microsoft.codeanalysis.csharp.workspaces.3.3.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-EI/9iVMneil8nZn7mqEujJvV63VRaz01Czc69NTrCQAh5CRfBvoeqBVsDx6oC9CIYla/AJYss+U4B/6Usecx7A==", "path": "microsoft.codeanalysis.razor/3.1.0", "hashPath": "microsoft.codeanalysis.razor.3.1.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/3.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-NfBz3b5hFSbO+7xsCNryD+p8axsIJFTG7qM3jvMTC/MqYrU6b8E1b6JoRj5rJSOBB+pSunk+CMqyGQTOWHeDUg==", "path": "microsoft.codeanalysis.workspaces.common/3.3.1", "hashPath": "microsoft.codeanalysis.workspaces.common.3.3.1.nupkg.sha512"}, "Microsoft.CodeCoverage/16.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-PM5YLtyN45EyUGePJpaNogndlaQPrMgQQXHKMhMESC6KfSVvt+j7+dxBi8NYC6X6dZVysf7ngwhSW3wwvPJRSQ==", "path": "microsoft.codecoverage/16.5.0", "hashPath": "microsoft.codecoverage.16.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-dSdlcXPszeOjjDX9a0buMFgYqKrI5bTxdSgX3JyCa+OL80NUstJSxOJr0j9oOn8mpP5PgWeRC2bVf/Zf2Cjv+g==", "path": "microsoft.data.sqlclient/2.1.7", "hashPath": "microsoft.data.sqlclient.2.1.7.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.Data.Sqlite/7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-KGxbPeWsQMnmQy43DSBxAFtHz3l2JX8EWBSGUCvT3CuZ8KsuzbkqMIJMDOxWtG8eZSoCDI04aiVQjWuuV8HmSw==", "path": "microsoft.data.sqlite/7.0.5", "hashPath": "microsoft.data.sqlite.7.0.5.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-FTerRmQPqHrCrnoUzhBu+E+1DNGwyrAMLqHkAqOOOu5pGfyMOj8qQUBxI/gDtWtG11p49UxSfWmBzRNlwZqfUg==", "path": "microsoft.data.sqlite.core/7.0.5", "hashPath": "microsoft.data.sqlite.core.7.0.5.nupkg.sha512"}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AAguUq7YyKk3yDWPoWA8DrLZvURxB/LrDdTn1h5lmPeznkFUpfC3p459w5mQYQE0qpquf/CkSQZ0etiV5vRHFA==", "path": "microsoft.dotnet.internalabstractions/1.0.0", "hashPath": "microsoft.dotnet.internalabstractions.1.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.27": {"type": "package", "serviceable": true, "sha512": "sha512-HY4o8koPh+PXpOq5L9S8QLw5Svq/AZJhLoppCio244N8LAgAWBMQ9edfVh8r/1IQbEj5Xj6M7ZyzcrSOsRwAzA==", "path": "microsoft.entityframeworkcore/6.0.27", "hashPath": "microsoft.entityframeworkcore.6.0.27.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.27": {"type": "package", "serviceable": true, "sha512": "sha512-AzJrXp9CVtZK65uTkinEFgR/iMmtATeLFlh//L2a5Y8Nrngcrbo7FCCJaUv0UdfZNwcZQmIGnSyoBX+lHuMeNw==", "path": "microsoft.entityframeworkcore.abstractions/6.0.27", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.27.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.27": {"type": "package", "serviceable": true, "sha512": "sha512-T3c1wpLrkRKTJzeHWFLK09y3CPW9siGyNEqmiqcr7B6CTgaqIajKtXr3XRbIGU04BiO+KqVvcCYv+Zqh661gJA==", "path": "microsoft.entityframeworkcore.analyzers/6.0.27", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.27.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.27": {"type": "package", "serviceable": true, "sha512": "sha512-OaxySZ/RhQUVNKdfhInhHO8ht7e+VniIsCSqfnqPqjPzNodLlfxn2mM3nGn4fbHHQaGVQHlQWUGa/Xac3egEKg==", "path": "microsoft.entityframeworkcore.relational/6.0.27", "hashPath": "microsoft.entityframeworkcore.relational.6.0.27.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.27": {"type": "package", "serviceable": true, "sha512": "sha512-Yc66OLFHx7fY5vI1/q9F3FZNt+hdcUO1dhRQev9dffeFVn2v/AA2vGhh1NUpDHASA7iCS2DRkwCr/Y7LwI3fNQ==", "path": "microsoft.entityframeworkcore.sqlserver/6.0.27", "hashPath": "microsoft.entityframeworkcore.sqlserver.6.0.27.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LN322qEKHjuVEhhXueTUe7RNePooZmS8aGid5aK2woX3NPjSnONFyKUc6+JknOS6ce6h2tCLfKPTBXE3mN/6Ag==", "path": "microsoft.extensions.configuration/5.0.0", "hashPath": "microsoft.extensions.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Of1Irt1+NzWO+yEYkuDh5TpT4On7LKl98Q9iLqCdOZps6XXEWDj3AKtmyvzJPVXZe4apmkJJIiDL7rR1yC+hjQ==", "path": "microsoft.extensions.configuration.binder/5.0.0", "hashPath": "microsoft.extensions.configuration.binder.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OelM+VQdhZ0XMXsEQBq/bt3kFzD+EBGqR4TAgFDRAye0JfvHAaRi+3BxCRcwqUAwDhV0U0HieljBGHlTgYseRA==", "path": "microsoft.extensions.configuration.commandline/5.0.0", "hashPath": "microsoft.extensions.configuration.commandline.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqh6y6hAi0Z0fRsb4B/mP9OkKkSlifh5osa+N/YSQ+/S2a//+zYApZMUC1XeP9fdjlgZoPQoZ72Q2eLHyKLddQ==", "path": "microsoft.extensions.configuration.environmentvariables/5.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rRdspYKA18ViPOISwAihhCMbusHsARCOtDMwa23f+BGEdIjpKPlhs3LLjmKlxfhpGXBjIsS0JpXcChjRUN+PAw==", "path": "microsoft.extensions.configuration.fileextensions/5.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pak8ymSUfdzPfBTLHxeOwcR32YDbuVfhnH2hkfOLnJNQd19ItlBdpMjIDY9C5O/nS2Sn9bzDMai0ZrvF7KyY/Q==", "path": "microsoft.extensions.configuration.json/5.0.0", "hashPath": "microsoft.extensions.configuration.json.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+tK3seG68106lN277YWQvqmfyI/89w0uTu/5Gz5VYSUu5TI4mqwsaWLlSmT9Bl1yW/i1Nr06gHJxqaqB5NU9Tw==", "path": "microsoft.extensions.configuration.usersecrets/5.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw==", "path": "microsoft.extensions.dependencymodel/8.0.2", "hashPath": "microsoft.extensions.dependencymodel.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iuZIiZ3mteEb+nsUqpGXKx2cGF+cv6gWPd5jqQI4hzqdiJ6I94ddLjKhQOuRW1lueHwocIw30xbSHGhQj0zjdQ==", "path": "microsoft.extensions.fileproviders.abstractions/5.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1rkd8UO2qf21biwO7X0hL9uHP7vtfmdv/NLvKgCRHkdz1XnW8zVQJXyEYiN68WYpExgtVWn55QF0qBzgfh1mGg==", "path": "microsoft.extensions.fileproviders.physical/5.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ArliS8lGk8sWRtrWpqI8yUVYJpRruPjCDT+EIjrgkA/AAPRctlAkRISVZ334chAKktTLzD1+PK8F5IZpGedSqA==", "path": "microsoft.extensions.filesystemglobbing/5.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hiokSU1TOVfcqpQAnpiOzP2rE9p+niq92g5yeAnwlbSrUlIdIS6M8emCknZvhdOagQA9x5YWNwe1n0kFUwE0NQ==", "path": "microsoft.extensions.hosting/5.0.0", "hashPath": "microsoft.extensions.hosting.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbUOCePYBl1UhM+N2zmDSUyJ6cODulbtUd9gEzMFIK3RQDtP/gJsE08oLcBSXH3Q1RAQ0ex7OAB3HeTKB9bXpg==", "path": "microsoft.extensions.hosting.abstractions/5.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kT1ijDKZuSUhBtYoC1sXrmVKP7mA08h9Xrsr4VrS/QOtiKCEtUTTd7dd3XI9dwAb46tZSak13q/zdIcr4jqbyg==", "path": "microsoft.extensions.http/5.0.0", "hashPath": "microsoft.extensions.http.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3/d0HeMRnBekadbZlmbp+In8EvNNkQHSdbtRzjrGVckdZWpYs5GNrAfaYqVplDFW0WUedSaFJ3khB50BWYGsw==", "path": "microsoft.extensions.logging.configuration/5.0.0", "hashPath": "microsoft.extensions.logging.configuration.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jH0wbWhfvXjOVmCkbra4vbiovDtTUIWLQjCeJ7Xun3h4AHvwfzm7V7wlsXKs3tNnPrsCxZ9oaV0vUAgGY1JxOA==", "path": "microsoft.extensions.logging.console/5.0.0", "hashPath": "microsoft.extensions.logging.console.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9dvt0xqRrClvhaPNpfyS39WxnW9G55l5lrV5ZX7IrEgwo4VwtmJKtoPiKVYKbhAuOBGUI5WY3hWLvF+PSbJp5A==", "path": "microsoft.extensions.logging.debug/5.0.0", "hashPath": "microsoft.extensions.logging.debug.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CYzsgF2lqgahGl/HuErsIDaZZ9ueN+MBjGfO/0jVDLPaXLaywxlGKFpDgXMaB053DRYZwD1H2Lb1I60mTXS3jg==", "path": "microsoft.extensions.logging.eventlog/5.0.0", "hashPath": "microsoft.extensions.logging.eventlog.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hF+D6PJkrM0qXcSEGs1BwZwgP8c0BRkj26P/5wmYTcHKOp52GRey/Z/YKRmRIHIrXxj9tz/JgIjU9oWmiJ5HMw==", "path": "microsoft.extensions.logging.eventsource/5.0.0", "hashPath": "microsoft.extensions.logging.eventsource.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-cDCXF4hzpWX9h8E4Gh90JAbyww2/tYYQK3Y9n8YxSwT2zNiV+uxffhmO8jF4V6fL17zhqhvgKhup3mxN46WnrA==", "path": "microsoft.extensions.logging.log4net.aspnetcore/3.1.0", "hashPath": "microsoft.extensions.logging.log4net.aspnetcore.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-280RxNJqOeQqq47aJLy5D9LN61CAWeuRA83gPToQ8B9jl9SNdQ5EXjlfvF66zQI5AXMl+C/3hGnbtIEN+X3mqA==", "path": "microsoft.extensions.options.configurationextensions/5.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/16.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-yHZ<PERSON>hVSPuGqgHi+KhHiAZqNY08avkQraXKvgKgDU8c/ztmGzw7gmukkv49EaTq6T3xmp4XroWk3gAlbJHMxl8w==", "path": "microsoft.net.test.sdk/16.5.0", "hashPath": "microsoft.net.test.sdk.16.5.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-z3YFkbnl1RMj6lb+Bm/2tsZ0cJCULlB4uPOFqlj6dNSm/8feJl4UrXmG6TcZh8ipJQwkAS5I6UCs1FnGog4QZg==", "path": "microsoft.netcore.platforms/5.0.1", "hashPath": "microsoft.netcore.platforms.5.0.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/16.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-NnLFxmFBCAS6kye2JFszD5WKgj4Zve5KX/R0mhYwh6BVnSeybI2unRnjEPtLyY3CAVhwrY4bh/8LNFtslAcGZg==", "path": "microsoft.testplatform.objectmodel/16.5.0", "hashPath": "microsoft.testplatform.objectmodel.16.5.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/16.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytGymboQIvjNX5pLC0yp/Bz9sGDHqSnLQgBRtd4VrqOUgKmmcfxMYZ6p0TBZgAT1oijdC6xqUZ7rl8mbaaXTJw==", "path": "microsoft.testplatform.testhost/16.5.0", "hashPath": "microsoft.testplatform.testhost.16.5.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-T9/B2pRRFvDqpVgQAGTEgyXbrxIm8SmHz+aMauXLLg4/iAuSF5h14imIBSKeANxispjL1+1bCa1NaXxLdiXgZA==", "path": "microsoft.visualstudio.web.codegeneration/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Contracts/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkK8dUAMczEA7I8Min2A32KvmqsQxELabhqedDKyI+CcJ3omDNa7nL/eDwwp9UGobdnBnIVaqjULeQJpUYkkaQ==", "path": "microsoft.visualstudio.web.codegeneration.contracts/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.contracts.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-7P6KH81lAXpHEOqZDWS7yo7yJlXoFtQhKxYXaoCHc2VhLhGJ2UtxmKTUGheL4j0MPh4BBVxTHzAjFF26I07rOg==", "path": "microsoft.visualstudio.web.codegeneration.core/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.core.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZHXVe8nDkHXmPlBNGOl3SIxoUKfah2OJOKKmq769gBMqHJMaOrFKhPYsHdTORjKUtwc3AlO1CMLgnljFyyxA/Q==", "path": "microsoft.visualstudio.web.codegeneration.design/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.design.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-gYNrm4DuqomIrqPzPLBEtJr8xhCdc4qEckEz59NElVso4DZIz78UDvYSfzfhF3qlKzlVNV353INeXLunpaWWVg==", "path": "microsoft.visualstudio.web.codegeneration.entityframeworkcore/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.entityframeworkcore.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-O5FQNCS9YeHGkgp+M1YGZHnE3YIIt4/QznrxYLf1BMFYFRNl0tClbqdtAZtBnbveXHfrf5U0VKq2J/yMREGgNg==", "path": "microsoft.visualstudio.web.codegeneration.templating/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.templating.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-52c9OMBHxfJmRt0993W7BCAjZQmLqCmt0lpbFe2ecKSPuW5uZstXlNSUbv+vV9QUvcRRiKDdiWv/y98a2q/Qfw==", "path": "microsoft.visualstudio.web.codegeneration.utils/3.1.0", "hashPath": "microsoft.visualstudio.web.codegeneration.utils.3.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-y4EFZyLFlFMetMJbR8SJZmy9Ba4Pj5oozhdwksnobn+v+yGTZoWxRTNRSRSRi8n+CE+oxWMcyd8RUiGCJGj2rg==", "path": "microsoft.visualstudio.web.codegenerators.mvc/3.1.0", "hashPath": "microsoft.visualstudio.web.codegenerators.mvc.3.1.0.nupkg.sha512"}, "Microsoft.Web.Infrastructure/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FNmvLn5m2LTU/Rs2KWVo0SIIh9Ek+U0ojex7xeDaSHw/zgEP77A8vY5cVWgUtBGS8MJfDGNn8rpXJWEIQaPwTg==", "path": "microsoft.web.infrastructure/1.0.0", "hashPath": "microsoft.web.infrastructure.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwF501ZS/xKGWz5H3RLBvwta6E5kcMLB0UYGTgrZ8nL5bvrbGmtEcEObgMC/qRFhA3og/0Zh+eacrcA+0FBXJA==", "path": "microsoft.win32.registry.accesscontrol/5.0.0", "hashPath": "microsoft.win32.registry.accesscontrol.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Microsoft.Windows.Compatibility/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-2imzNvgFenWrUJMr4XKwR9LHy2LZK8VbmUE4Z8hTT9fB0mMisjZjn/1mgLAcn8TE5U7Bjk8zVEwsEttLC1yenA==", "path": "microsoft.windows.compatibility/5.0.2", "hashPath": "microsoft.windows.compatibility.5.0.2.nupkg.sha512"}, "Moq/4.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-ic4m9/b10tz9oRB1Oi5bW7E/FS6Pd2SH5OJFhlmhUJkQhiV5FyrIRxVUEaG5KOpSpcfSPGAVW4rRZt6OzrS5zg==", "path": "moq/4.13.1", "hashPath": "moq.4.13.1.nupkg.sha512"}, "MySql.Data/8.0.13": {"type": "package", "serviceable": true, "sha512": "sha512-lj/0OSRrs4VwsGDSjU0w24fwLxIU1fwesohzg+UfoDfqiSAu+fykeFF9y+fCmip8ZlkHegYNdEAHdhLfmZbSIw==", "path": "mysql.data/8.0.13", "hashPath": "mysql.data.8.0.13.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "NETStandard.Library/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7jnbRU+L08FXKMxqUflxEXtVymWvNOrS8yHgu9s6EM8Anr6T/wIX4nZ08j/u3Asz+tCufp3YVwFSEvFTPYmBPA==", "path": "netstandard.library/2.0.0", "hashPath": "netstandard.library.2.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Npgsql/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-vitwybpiG32EUOScHSjr1kTej8PwoYEWjHwTel5lwsq0OY4p0LJd80ZeGQHLa6zq12hsq9vyAfT9K80iWrpxPA==", "path": "npgsql/6.0.10", "hashPath": "npgsql.6.0.10.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.22": {"type": "package", "serviceable": true, "sha512": "sha512-iD0X/yef9KBsCBMDT1ZytW3pPYeYHeceUuiznaeGfkltvluG7O25aG2fwv2sO6icESL0CLNwNAF1Mu4cFrKmaA==", "path": "npgsql.entityframeworkcore.postgresql/6.0.22", "hashPath": "npgsql.entityframeworkcore.postgresql.6.0.22.nupkg.sha512"}, "NPOI/2.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-hKJeAQOuVl8b2qiQQg0ph8uhqm5ihq94SDSnmPu5ytCLIRTVcg2rFA274ow+IacrORImdvo0Q3K51L1t3vLqpw==", "path": "npoi/2.7.2", "hashPath": "npoi.2.7.2.nupkg.sha512"}, "NuGet.Frameworks/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c5JVjuVAm4f7E9Vj+v09Z9s2ZsqFDjBpcsyS3M9xRo0bEdm/LVZSzLxxNvfvAwRiiE8nwe1h2G4OwiwlzFKXlA==", "path": "nuget.frameworks/5.0.0", "hashPath": "nuget.frameworks.5.0.0.nupkg.sha512"}, "NUnit/3.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWBvrSelmTYwqHWvO3dA63z7cOpaFR/3nJ9MMVLBkWeaWa7oiglPPm5g1h96B4i2XXqjFexxhR5MyMjmIJYPfg==", "path": "nunit/3.13.1", "hashPath": "nunit.3.13.1.nupkg.sha512"}, "NUnit3TestAdapter/3.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-I9MNvK+GM2yXrHPitwZkAZKU9sYI2OO/8wKC+VuBD7V3z+ySQ1pSopX/urr0ooedI8/TIcajYPRO4vGRr7AM8A==", "path": "nunit3testadapter/3.17.0", "hashPath": "nunit3testadapter.3.17.0.nupkg.sha512"}, "Oracle.EntityFrameworkCore/6.21.130": {"type": "package", "serviceable": true, "sha512": "sha512-iPZ9XepmOaHuSKQMpXsOFF9qVwA3UVis4KwfmRZHe/jL6KybZ24sJWfpebBim/mv4I8FAXVAx88Zv1mSnz8Anw==", "path": "oracle.entityframeworkcore/6.21.130", "hashPath": "oracle.entityframeworkcore.6.21.130.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.130": {"type": "package", "serviceable": true, "sha512": "sha512-bXNwPyAaDivO84HEYiegK7r/LleSEfA0nc70bE88wkqd1E0Q1+r/SUbJ51xOK2vk6iHbTSMuhnC79BU8svPBjQ==", "path": "oracle.manageddataaccess.core/3.21.130", "hashPath": "oracle.manageddataaccess.core.3.21.130.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hzHplEIVOGBl5zOQZGX/DiJDHjq+RVRVrYgDiqXb6RriqWAdacXxp+XO9WSrATCEXyNOUOQg9aqQArsjase/A==", "path": "pipelines.sockets.unofficial/2.2.0", "hashPath": "pipelines.sockets.unofficial.2.2.0.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-KvlZ800CnEuEGnxj5OT1fCKGjQXxW5kpPlCP91JqBYG+2Z3927eqXmlX6LLKUt4swqE8ZsEQ+Zkpab8bqstf4g==", "path": "pomelo.entityframeworkcore.mysql/6.0.2", "hashPath": "pomelo.entityframeworkcore.mysql.6.0.2.nupkg.sha512"}, "Quartz/3.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-jnHuMwRprkqpmnKZU5xwEB95v1bF4JQWes82p2IPTAEGcBsZ5CSEQ4Pu1StCyD64JIm71C5/i7kuroZEHx5iwA==", "path": "quartz/3.0.7", "hashPath": "quartz.3.0.7.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"type": "package", "serviceable": true, "sha512": "sha512-Np6w3r1dSFB930GGZHIKCc5ZClRXZIqOrCAT0pzcd/zXnsZPvGqLZB1MnxAbVhvriJl71B0N0tJaaT1ICWXsyg==", "path": "runtime.linux-arm.runtime.native.system.io.ports/5.0.0-rtm.20519.4", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.5.0.0-rtm.20519.4.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"type": "package", "serviceable": true, "sha512": "sha512-VnGZmQ7pzMNkcTVdmGtXUQIbytK4Xk8F4/mxm0I+n7zbrsW/WNgLrWMTv9pb2Uyq09azXazNDQhZao4R4ebWcw==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/5.0.0-rtm.20519.4", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.5.0.0-rtm.20519.4.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"type": "package", "serviceable": true, "sha512": "sha512-kvMZgZjtcC6cA8Y8imKpjCpiOJKDtwlNekS86GzUol4Jmzh0FWiRwAj4E9ZKO8R7rTBGIA4rkmra9Ko8j7l6AA==", "path": "runtime.linux-x64.runtime.native.system.io.ports/5.0.0-rtm.20519.4", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.5.0.0-rtm.20519.4.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ME+/evR+UxVlWyGHUlLBoNTnsTdaylMbnvVwOp0Nl6XIZGGyXdqJqjlEew7e6TcKkJAA0lljhjKi3Kie+vzQ7g==", "path": "runtime.native.system.io.ports/5.0.0", "hashPath": "runtime.native.system.io.ports.5.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/5.0.0-rtm.20519.4": {"type": "package", "serviceable": true, "sha512": "sha512-N+dbbqhT7JBnPVHa7n2+Z5fHYO4a4UUhm7cQkbuQQoNkjbxLpxYnQ4lpRjr1RuQptqYkPmunKvN5etdFOObaiw==", "path": "runtime.osx-x64.runtime.native.system.io.ports/5.0.0-rtm.20519.4", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.5.0.0-rtm.20519.4.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "SixLabors.Fonts/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ljezRHWc7N0azdQViib7Aa5v+DagRVkKI2/93kEbtjVczLs+yTkSq6gtGmvOcx4IqyNbO3GjLt7SAQTpLkySNw==", "path": "sixlabors.fonts/1.0.1", "hashPath": "sixlabors.fonts.1.0.1.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-VcjfKbOExie3RgZGrQL1jJMI4iZ3J9B6ifDo2QpDVJUYhTZKVcKnBhpNOHqbvNjHgadAks1jzhRjB7OZet1PJA==", "path": "sixlabors.imagesharp/2.1.9", "hashPath": "sixlabors.imagesharp.2.1.9.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-EWI1olKDjFEBMJu0+3wuxwziIAdWDVMYLhuZ3Qs84rrz+DHwD00RzWPZCa+bLnHCf3oJwuFZIRsHT5p236QXww==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.4", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.4.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-inBjvSHo9UDKneGNzfUfDjK08JzlcIhn1+SP5Y3m6cgXpCxXKCJDy6Mka7LpgSV+UZmKSnC8rTwB0SQ0xKu5pA==", "path": "sqlitepclraw.core/2.1.4", "hashPath": "sqlitepclraw.core.2.1.4.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-2C9Q9eX7CPLveJA0rIhf9RXAvu+7nWZu1A2MdG6SD/NOu26TakGgL1nsbc0JAspGijFOo3HoN79xrx8a368fBg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.4", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.4.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-CSlb5dUp1FMIkez9Iv5EXzpeq7rHryVNqwJMWnpq87j9zWZexaEMdisDktMsnnrzKM6ahNrsTkjqNodTBPBxtQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.4", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.4.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-Odsvp5ZMbflxQDhJSKWk+s0HaSW36adKJlLA5yWoVpsQ557GSfdINRqMT1NfUCaNsc21TMLsjw5lcnzSDFXDSA==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JFhgCGfCMvI0/u7WdsSzK4D7ptZl1RXP8Q7KwSGpBndgUXlfnnmCfwJaz4f89XxalRLLk1h/x0RAuUei98/CmA==", "path": "sqlsugarcore.dm/1.2.0", "hashPath": "sqlsugarcore.dm.1.2.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cHqgzvPz65v6pkO61oZM2pcPKY0KXvZo2EEAbZFHmyl5X7suxzpTOz/b6DvXjmRlcHxTRKGav2wwmStqTiUacg==", "path": "sqlsugarcore.kdbndp/7.4.0", "hashPath": "sqlsugarcore.kdbndp.7.4.0.nupkg.sha512"}, "StackExchange.Redis/2.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-wM0OuRyRaZTFndFRjIOvas4jjkeclRJsmNm0eAx5tOju3SQisrLubNaSFT/dBypi4Vh1n7nYc1gWpw9L7ernOg==", "path": "stackexchange.redis/2.2.4", "hashPath": "stackexchange.redis.2.2.4.nupkg.sha512"}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "path": "system.appcontext/4.1.0", "hashPath": "system.appcontext.4.1.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.CodeDom/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JPJArwA1kdj8qDAkY2XGjSWoYnqiM7q/3yRNkt6n28Mnn95MuEGkZXUbPBf7qc3IjwrGY5ttQon7yqHZyQJmOQ==", "path": "system.codedom/5.0.0", "hashPath": "system.codedom.5.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Composition/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YL8iA3VOFxhyomn7FxtBgh3F+8XG5jOfT5UcqYLtkafSa6g6alQfKZuRwlEIWe+tzH6OVnj0Ekg5tn/DmV7SkQ==", "path": "system.componentmodel.composition/5.0.0", "hashPath": "system.componentmodel.composition.5.0.0.nupkg.sha512"}, "System.ComponentModel.Composition.Registration/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CTTPajoCKcXQ1NVTlazz6ned37MHVFf1qKfzsBIdHkaFJBnRVVh4hYsVkPP7z+RrMQU5iXdiTcsfxDb5DWOKOA==", "path": "system.componentmodel.composition.registration/5.0.0", "hashPath": "system.componentmodel.composition.registration.5.0.0.nupkg.sha512"}, "System.ComponentModel.EventBasedAsync/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fCFl8f0XdwA/BuoNrVBB5D0Y48/hv2J+w4xSDdXQitXZsR6UCSOrDVE7TCUraY802ENwcHUnUCv4En8CupDU1g==", "path": "system.componentmodel.eventbasedasync/4.3.0", "hashPath": "system.componentmodel.eventbasedasync.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-I+D26qpYdoklyAVUdqwUBrEIckMNjAYnuPJy/h9dsQItpQwVREkDFs4b4tkBza0kT2Yk48Lcfsv2QQ9hWsh9Iw==", "path": "system.composition/1.0.31", "hashPath": "system.composition.1.0.31.nupkg.sha512"}, "System.Composition.AttributedModel/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-NHWhkM3ZkspmA0XJEsKdtTt1ViDYuojgSND3yHhTzwxepiwqZf+BCWuvCbjUt4fe0NxxQhUDGJ5km6sLjo9qnQ==", "path": "system.composition.attributedmodel/1.0.31", "hashPath": "system.composition.attributedmodel.1.0.31.nupkg.sha512"}, "System.Composition.Convention/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-GLjh2Ju71k6C0qxMMtl4efHa68NmWeIUYh4fkUI8xbjQrEBvFmRwMDFcylT8/PR9SQbeeL48IkFxU/+gd0nYEQ==", "path": "system.composition.convention/1.0.31", "hashPath": "system.composition.convention.1.0.31.nupkg.sha512"}, "System.Composition.Hosting/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-fN1bT4RX4vUqjbgoyuJFVUizAl2mYF5VAb+bVIxIYZSSc0BdnX+yGAxcavxJuDDCQ1K+/mdpgyEFc8e9ikjvrg==", "path": "system.composition.hosting/1.0.31", "hashPath": "system.composition.hosting.1.0.31.nupkg.sha512"}, "System.Composition.Runtime/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0LEJN+2NVM89CE4SekDrrk5tHV5LeATltkp+9WNYrR+Huiyt0vaCqHbbHtVAjPyeLWIc8dOz/3kthRBj32wGQg==", "path": "system.composition.runtime/1.0.31", "hashPath": "system.composition.runtime.1.0.31.nupkg.sha512"}, "System.Composition.TypedParts/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0Zae/FtzeFgDBBuILeIbC/T9HMYbW4olAmi8XqqAGosSOWvXfiQLfARZEhiGd0LVXaYgXr0NhxiU1LldRP1fpQ==", "path": "system.composition.typedparts/1.0.31", "hashPath": "system.composition.typedparts.1.0.31.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "path": "system.data.datasetextensions/4.5.0", "hashPath": "system.data.datasetextensions.4.5.0.nupkg.sha512"}, "System.Data.Odbc/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-j4WsYGmcD7m1D0Tc3N7HqWqcdUHNn9+kdXh9ODTWEsOGrAvALf+BgRStd7L0/O/zDS0R4Uu9vNM8UY6EnK+WYw==", "path": "system.data.odbc/5.0.0", "hashPath": "system.data.odbc.5.0.0.nupkg.sha512"}, "System.Data.OleDb/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHCZucsidgFtUr1w5OggQNjb7M6N722QpNbkG6TV+3hCvPSLXdrm1NjJqVZB5/OW067gzuZVj9W147hrkTF/Ig==", "path": "system.data.oledb/5.0.0", "hashPath": "system.data.oledb.5.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-k4O5RrjnhJZrP4EgOklUVkcmVdAxs9+PoXCGmlNS3NPIwaSyMMLy7pUaamMHCFkduiOO/ZUzIRjyoCnvXLJpfw==", "path": "system.diagnostics.eventlog/5.0.1", "hashPath": "system.diagnostics.eventlog.5.0.1.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.Process/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-J0wOX07+QASQblsfxmIMFc9Iq7KTXYL3zs2G/Xc704Ylv3NpuVdo6gij6V3PGiptTxqsK0K7CdXenRvKUnkA2g==", "path": "system.diagnostics.process/4.3.0", "hashPath": "system.diagnostics.process.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "path": "system.diagnostics.tracesource/4.3.0", "hashPath": "system.diagnostics.tracesource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.AccountManagement/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1WevH/8ULy0iixbsZW4k8ftV9fDqkeUJfeVMsJ7SySrHsnBISkRx1JuDRRV7QXfNTCQKrHeecfqcY5pevlDwog==", "path": "system.directoryservices.accountmanagement/5.0.0", "hashPath": "system.directoryservices.accountmanagement.5.0.0.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-vDDPWwHn3/DNZ+kPkdXHoada+tKPEC9bVqDOr4hK6HBSP7hGCUTA0Zw6WU5qpGaqa5M1/V+axHMIv+DNEbIf6g==", "path": "system.directoryservices.protocols/6.0.2", "hashPath": "system.directoryservices.protocols.6.0.2.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A==", "path": "system.formats.asn1/8.0.1", "hashPath": "system.formats.asn1.8.0.1.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ebfUwKsgZF4HTwaRUj67SrJdsM4O62Fxsd6u1bSk3MNgvU8yjyfEK0xQmUFUqOYJi1IcL4HENoccl4SKVPndYw==", "path": "system.io.packaging/5.0.0", "hashPath": "system.io.packaging.5.0.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irMYm3vhVgRsYvHTU5b2gsT2CwT/SMM6LZFzuJjpIvT5Z4CshxNsaoBC1X/LltwuR3Opp8d6jOS/60WwOb7Q2Q==", "path": "system.io.pipelines/5.0.0", "hashPath": "system.io.pipelines.5.0.0.nupkg.sha512"}, "System.IO.Pipes.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-P0FIsXSFNL1AXlHO9zpJ9atRUzVyoPZCkcbkYGZfXXMx9xlGA2H3HOGBwIhpKhB+h0eL3hry/z0UcfJZ+yb2kQ==", "path": "system.io.pipes.accesscontrol/5.0.0", "hashPath": "system.io.pipes.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.Ports/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MZY/0cgRg5bcuvHR4LKHqWnlxWV7GkoTgBaOdwIoWGZKsfSBC1twDz+BzG0o1Rk46WdRhhV30E2qzsBABHwGUA==", "path": "system.io.ports/5.0.0", "hashPath": "system.io.ports.5.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Management/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MF1CHaRcC+MLFdnDthv4/bKWBZnlnSpkGqa87pKukQefgEdwtb9zFW6zs0GjPp73qtpYYg4q6PEKbzJbxCpKfw==", "path": "system.management/5.0.0", "hashPath": "system.management.5.0.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-V21eCo3u2GOzq/BOSD3hxlJQCOp71RHXI0TxzbFp8k5Gtnu/X2bBYETpiRFUsUojJz5MwUNjtW8BdwdUnwP05g==", "path": "system.private.servicemodel/4.8.0", "hashPath": "system.private.servicemodel.4.8.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Context/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gG1wxxJLcjQaUkd07K2l2MKVoW+e0w8jS8Jye7QLPXrXT7XXMmOcFV/Ek6XyTOy5Z4GVN0WY95BQNp/iHEs5mw==", "path": "system.reflection.context/5.0.0", "hashPath": "system.reflection.context.5.0.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "path": "system.reflection.emit.ilgeneration/4.7.0", "hashPath": "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-dYSKm+jBVh/I9RQP6AaBiyd92Y6Ml9vEEcTSougcAMSt9X9QQHTGJo0p/uH4uSgODZe+5cuOkgg8wg02NuM0VQ==", "path": "system.reflection.typeextensions/4.5.0", "hashPath": "system.reflection.typeextensions.4.5.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-30D6MkO8WF9jVGWZIP0hmCN8l9BTY4LCsAzLIe4xFSXzs+AjDotR7DpSmj27pFskDURzUvqYYY0ikModgBTxWw==", "path": "system.runtime.caching/5.0.0", "hashPath": "system.runtime.caching.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "path": "system.runtime.serialization.xml/4.3.0", "hashPath": "system.runtime.serialization.xml.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "path": "system.security.cryptography.xml/8.0.2", "hashPath": "system.security.cryptography.xml.8.0.2.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+LprVYRVD2exSoh4BLo5HJrXUMCoJICxeffYVSwVLhjTalcfUZU3dkGgp5oDtYU82CDV4D8hD8uvaOTNm0IogQ==", "path": "system.servicemodel.duplex/4.8.0", "hashPath": "system.servicemodel.duplex.4.8.0.nupkg.sha512"}, "System.ServiceModel.Http/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+kn7xCZjf/d02SE3OnwyU4wxggrxEhOoGLB+DUACSTXvSIGKZoHPXQ6LhkY0/m7nzdjqxmvc7cJO3UPWgIH/8Q==", "path": "system.servicemodel.http/4.8.0", "hashPath": "system.servicemodel.http.4.8.0.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-KoeXvhicGSQ0A60AruHWTNN/D17ErLkv90SmWS/sFQ+QUc4Bbb8GDFwmDoUhwsa35khiwpSUWt8OBIcoM/ZabA==", "path": "system.servicemodel.nettcp/4.8.0", "hashPath": "system.servicemodel.nettcp.4.8.0.nupkg.sha512"}, "System.ServiceModel.Primitives/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-akJiBb0EcWTXQ/dgp1a27O7UynZbqQ84M17OY/ssxfb32x5wtQ5zRw6ExsQaPOUQgia4Wl2jy4FWZddQRLLnaA==", "path": "system.servicemodel.primitives/4.8.0", "hashPath": "system.servicemodel.primitives.4.8.0.nupkg.sha512"}, "System.ServiceModel.Security/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-oeCYY/i2kFgsVsxkuF4+PdepnVjSKNZtwSKb2qD6CCisLeXfmT5g7IuCdlLlbc2Ppq+wI7KsNhf+xnh9ESnVCQ==", "path": "system.servicemodel.security/4.8.0", "hashPath": "system.servicemodel.security.4.8.0.nupkg.sha512"}, "System.ServiceModel.Syndication/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xjwRFydlevI/DMLlBcDRbOmofJTZNoJ0FCkEPdMw9i+85lDbl8Pw001LJKQbRSeHSVJCEuPfAvEuC1TAumxcmw==", "path": "system.servicemodel.syndication/5.0.0", "hashPath": "system.servicemodel.syndication.5.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-p2yX167GW1pr2DCR6cW+cBKrvhli4thckXk108faFaTPHnoudb0AYPcIPq3nmrwn7IQj9FEmjpyJlXzcOmIjjw==", "path": "system.serviceprocess.servicecontroller/5.0.0", "hashPath": "system.serviceprocess.servicecontroller.5.0.0.nupkg.sha512"}, "System.Speech/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-URJOtiuS47+aK4u8uBGgky4XuiUzoiTFB7VGyaayD4iQDC/mE0aoPIB0m73lGzt0xYc/ZnMC6ltGb49t76wXJA==", "path": "system.speech/5.0.0", "hashPath": "system.speech.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WJ9w9m4iHJVq0VoH7hZvYAccbRq95itYRhAAXd6M4kVCzLmT6NqTwmSXKwp3oQilWHhYTXgqaIXxBfg8YaqtmA==", "path": "system.threading.accesscontrol/5.0.0", "hashPath": "system.threading.accesscontrol.5.0.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jw9oHHEIVW53mHY9PgrQa98Xo2IZ0ZjrpdOTmtvk+Rvg4tq7dydmxdNqUvJ5YwjDqhn75mBXWttWjiKhWP53LQ==", "path": "system.xml.xpath.xdocument/4.3.0", "hashPath": "system.xml.xpath.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "WebGrease/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-VoyDSOOugLx3q5IyOgEcjeYZQOqV9VWZQT57UtN4szlYhAmOcEjVfKWXbtRqBuX43pgyL9E7nMSIPoryC8Cu1Q==", "path": "webgrease/1.5.2", "hashPath": "webgrease.1.5.2.nupkg.sha512"}, "Z.EntityFramework.Extensions.EFCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-btXLIn2xtJRMAM3TSKfBMIsVDxpfTplrihRuJ/3f/G0iQPTw0qgri7ByL7WorZXxP1PN6XyFjXicrsWb7GoA/Q==", "path": "z.entityframework.extensions.efcore/*********", "hashPath": "z.entityframework.extensions.efcore.*********.nupkg.sha512"}, "Z.EntityFramework.Plus.EFCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-b5<PERSON><PERSON><PERSON>Yl8tqgmRq5WahlJ64od4tUGJIs5aXyUK7QXcqnuQjatdJB7cv6Zchd6ZpQWml7hXC+iMQtbxfF9J2Xg==", "path": "z.entityframework.plus.efcore/*********", "hashPath": "z.entityframework.plus.efcore.*********.nupkg.sha512"}, "Z.Expressions.Eval/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hkEew/rXAZ84znI596dkVUB2owGcIM4hSZLRk2JURdwnpQhZhg+KeZmxIxRBayvRW15XySjaC5/6QKdQwyhO2Q==", "path": "z.expressions.eval/6.1.1", "hashPath": "z.expressions.eval.6.1.1.nupkg.sha512"}, "Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "OpenAuth.App/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "OpenAuth.Repository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Aspose.Cells/18.4.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}