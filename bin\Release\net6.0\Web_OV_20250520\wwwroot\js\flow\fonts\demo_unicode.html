
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">

    <style type="text/css">

        @font-face {font-family: "iconfont";
          src: url('iconflow.eot'); /* IE9*/
          src: url('iconflow.eot#iefix') format('embedded-opentype'), /* IE6-IE8 */
          url('iconflow.woff') format('woff'), /* chrome, firefox */
          url('iconflow.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
          url('iconflow.svg#iconfont') format('svg'); /* iOS 4.1- */
        }

        .iconfont {
          font-family:"iconfont" !important;
          font-size:16px;
          font-style:normal;
          -webkit-font-smoothing: antialiased;
          -webkit-text-stroke-width: 0.2px;
          -moz-osx-font-smoothing: grayscale;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconfont">&#xe602;</i>
                    <div class="name">指针</div>
                    <div class="code">&amp;#xe602;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe674;</i>
                    <div class="name">关闭</div>
                    <div class="code">&amp;#xe674;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61a;</i>
                    <div class="name">close</div>
                    <div class="code">&amp;#xe61a;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe659;</i>
                    <div class="name">文件添加</div>
                    <div class="code">&amp;#xe659;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6f9;</i>
                    <div class="name">tag</div>
                    <div class="code">&amp;#xe6f9;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe672;</i>
                    <div class="name">conow-redo</div>
                    <div class="code">&amp;#xe672;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe673;</i>
                    <div class="name">conow-revoke</div>
                    <div class="code">&amp;#xe673;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe63d;</i>
                    <div class="name">保存</div>
                    <div class="code">&amp;#xe63d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe607;</i>
                    <div class="name">Refresh</div>
                    <div class="code">&amp;#xe607;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6b7;</i>
                    <div class="name">右下-实心</div>
                    <div class="code">&amp;#xe6b7;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6bd;</i>
                    <div class="name">拍照按钮</div>
                    <div class="code">&amp;#xe6bd;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe611;</i>
                    <div class="name">徽章</div>
                    <div class="code">&amp;#xe611;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe600;</i>
                    <div class="name">hr工作台</div>
                    <div class="code">&amp;#xe600;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe609;</i>
                    <div class="name">停止</div>
                    <div class="code">&amp;#xe609;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe7a0;</i>
                    <div class="name">文件夹</div>
                    <div class="code">&amp;#xe7a0;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe643;</i>
                    <div class="name">声音</div>
                    <div class="code">&amp;#xe643;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe742;</i>
                    <div class="name">数据库</div>
                    <div class="code">&amp;#xe742;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe66c;</i>
                    <div class="name">插件 (1)</div>
                    <div class="code">&amp;#xe66c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe649;</i>
                    <div class="name">菜单</div>
                    <div class="code">&amp;#xe649;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe62b;</i>
                    <div class="name">网络</div>
                    <div class="code">&amp;#xe62b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe671;</i>
                    <div class="name">打印</div>
                    <div class="code">&amp;#xe671;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe6af;</i>
                    <div class="name">用户</div>
                    <div class="code">&amp;#xe6af;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe700;</i>
                    <div class="name">开始</div>
                    <div class="code">&amp;#xe700;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe606;</i>
                    <div class="name">合并</div>
                    <div class="code">&amp;#xe606;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe61b;</i>
                    <div class="name">聊天</div>
                    <div class="code">&amp;#xe61b;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe633;</i>
                    <div class="name">定时</div>
                    <div class="code">&amp;#xe633;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe605;</i>
                    <div class="name">斜箭头</div>
                    <div class="code">&amp;#xe605;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe678;</i>
                    <div class="name">配置</div>
                    <div class="code">&amp;#xe678;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe872;</i>
                    <div class="name">项目组合</div>
                    <div class="code">&amp;#xe872;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe68f;</i>
                    <div class="name">系统参数配置</div>
                    <div class="code">&amp;#xe68f;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe663;</i>
                    <div class="name">区块</div>
                    <div class="code">&amp;#xe663;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60c;</i>
                    <div class="name">分支</div>
                    <div class="code">&amp;#xe60c;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60d;</i>
                    <div class="name">01</div>
                    <div class="code">&amp;#xe60d;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe60e;</i>
                    <div class="name">02</div>
                    <div class="code">&amp;#xe60e;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe675;</i>
                    <div class="name">小虚线</div>
                    <div class="code">&amp;#xe675;</div>
                </li>
            
                <li>
                <i class="icon iconfont">&#xe624;</i>
                    <div class="name">检查工具  直线</div>
                    <div class="code">&amp;#xe624;</div>
                </li>
            
        </ul>
        <h2 id="unicode-">unicode引用</h2>
        <hr>

        <p>unicode是字体在网页端最原始的应用方式，特点是：</p>
        <ul>
        <li>兼容性最好，支持ie6+，及所有现代浏览器。</li>
        <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
        <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
        </ul>
        <blockquote>
        <p>注意：新版iconfont支持多色图标，这些多色图标在unicode模式下将不能使用，如果有需求建议使用symbol的引用方式</p>
        </blockquote>
        <p>unicode使用步骤如下：</p>
        <h3 id="-font-face">第一步：拷贝项目下面生成的font-face</h3>
        <pre><code class="lang-js hljs javascript">@font-face {
  font-family: <span class="hljs-string">'iconfont'</span>;
  src: url(<span class="hljs-string">'iconfont.eot'</span>);
  src: url(<span class="hljs-string">'iconfont.eot?#iefix'</span>) format(<span class="hljs-string">'embedded-opentype'</span>),
  url(<span class="hljs-string">'iconfont.woff'</span>) format(<span class="hljs-string">'woff'</span>),
  url(<span class="hljs-string">'iconfont.ttf'</span>) format(<span class="hljs-string">'truetype'</span>),
  url(<span class="hljs-string">'iconfont.svg#iconfont'</span>) format(<span class="hljs-string">'svg'</span>);
}
</code></pre>
        <h3 id="-iconfont-">第二步：定义使用iconfont的样式</h3>
        <pre><code class="lang-js hljs javascript">.iconfont{
  font-family:<span class="hljs-string">"iconfont"</span> !important;
  font-size:<span class="hljs-number">16</span>px;font-style:normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: <span class="hljs-number">0.2</span>px;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
        <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
        <pre><code class="lang-js hljs javascript">&lt;i <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"iconfont"</span>&gt;&amp;#x33;<span class="xml"><span class="hljs-tag">&lt;/<span class="hljs-name">i</span>&gt;</span></span></code></pre>

        <blockquote>
        <p>"iconfont"是你项目下的font-family。可以通过编辑项目查看，默认是"iconfont"。</p>
        </blockquote>
    </div>


</body>
</html>
