﻿using Infrastructure.Helpers;
using Microsoft.AspNetCore.Mvc;
using OpenAuth.App.Interface;

namespace OpenAuth.Customer.Mvc.Controllers
{
    public class HomeController : BaseController
    {
        public ActionResult Index()
        {
            var configuration = ConfigHelper.GetConfigRoot();
            var Version = configuration["AppSetting:Version"];
            ViewBag.Version = Version;
            return View();
        }

        public ActionResult Main()
        {
            return View();
        }

        public ActionResult MenuPreview()
        {
            return View();
        }

        public ActionResult DropdownTest()
        {
            return View();
        }

        public ActionResult TestPage()
        {
            return View();
        }

        public ActionResult Git()
        {
            return View();
        }


        public HomeController(IAuth authUtil) : base(authUtil)
        {
        }
    }
}