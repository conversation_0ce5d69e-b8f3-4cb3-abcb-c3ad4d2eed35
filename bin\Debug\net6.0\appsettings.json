{
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DataProtection": "temp-keys/",
  "ConnectionStrings": {
    "USWMSDBContext": "Data Source=.;Initial Catalog=HYCSSD;User=sa;Password=******",
    "OpenAuthDBContext": "Data Source=.;Initial Catalog=OpenAuthDB;User=sa;Password=******",
    "CRMDBContext": "Data Source=.;Initial Catalog=CRM;User=sa;Password=******"

  },
  "AppSetting": {
    "IdentityServerUrl": "", //IdentityServer服务器地址。如果为空，则不启用OAuth认证
    "Version": "v1.0.8", //如果为demo，则可以防止post提交
    "DbTypes": {
      "OpenAuthDBContext": "SqlServer",
      "USWMSDBContext": "SqlServer",
      "CRMDBContext": "SqlServer"

      //数据库类型：SqlServer、MySql、Oracle、PostgreSQL
    },
    "HttpHost": "http://*:1803" //启动绑定地址及端口
  },
  "EPPlus": {
    "ExcelPackage": {
      "LicenseContext": "NonCommercial",
      "Compatibility": {
        "IsWorksheets1Based": "true"
      }
    }
  }
}