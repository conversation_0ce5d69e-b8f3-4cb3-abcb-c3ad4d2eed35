﻿var commonUtil = {
    alert: function (msg, type) {
        if (typeof (type) == "undefined") { // 未传入type则默认为success类型的消息框
            type = "success";
        }
        // 创建bootstrap的alert元素
        var divElement = $("<div></div>").addClass('alert').addClass('alert-' + type).addClass('alert-dismissible').addClass('col-md-4').addClass('text-center');
        divElement.css({ // 消息框的定位样式
            "position": "fixed", // 固定定位，元素相对于浏览器窗口固定
            "top": "10%",        // 距离窗口顶部 20% 的位置
            "left": "50%",       // 水平位置在窗口的 50% 处
            "transform": "translate(-50%,-50%)", // 通过 transform 将元素水平居中
            "z-index": "9999"    // 确保层级高于其他元素
        });
        divElement.text(msg); // 设置消息框的内容
        // 消息框添加可以关闭按钮
        var closeBtn = $('<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>');
        $(divElement).append(closeBtn);
        // 消息框放入到页面中
        $('body').append(divElement);
        return divElement;
    },


    message: function (msg, type) {
        var divElement = commonUtil.alert(msg, type); // 生成Alert消息框
        var isIn = false; // 鼠标是否在消息框中

        divElement.on({ // 在setTimeout执行之前先判定鼠标是否在消息框中
            mouseover: function () { isIn = true; },
            mouseout: function () { isIn = false; }
        });

        // 短暂延时后上浮消失
        setTimeout(function () {
            var IntervalMS = 20; // 每次上浮的间隔毫秒
            var floatSpace = 60; // 上浮的空间(px)
            var nowTop = divElement.offset().top; // 获取元素当前的top值
            var stopTop = nowTop - floatSpace;    // 上浮停止时的top值
            divElement.fadeOut(IntervalMS * floatSpace); // 设置元素淡出

            var upFloat = setInterval(function () { // 开始上浮
                if (nowTop >= stopTop) { // 判断当前消息框top是否还在可上升的范围内
                    divElement.css({ "top": nowTop-- }); // 消息框的top上升1px
                } else {
                    clearInterval(upFloat); // 关闭上浮
                    divElement.remove();    // 移除元素
                }
            }, IntervalMS);

            if (isIn) { // 如果鼠标在setTimeout之前已经放在的消息框中，则停止上浮
                clearInterval(upFloat);
                divElement.stop();
            }

            divElement.hover(function () { // 鼠标悬浮时停止上浮和淡出效果，过后恢复
                clearInterval(upFloat);
                divElement.stop();
            }, function () {
                divElement.fadeOut(IntervalMS * (nowTop - stopTop)); // 这里设置元素淡出的时间应该为：间隔毫秒*剩余可以上浮空间
                upFloat = setInterval(function () { // 继续上浮
                    if (nowTop >= stopTop) {
                        divElement.css({ "top": nowTop-- });
                    } else {
                        clearInterval(upFloat); // 关闭上浮
                        divElement.remove();    // 移除元素
                    }
                }, IntervalMS);
            });
        }, 1500);
    }
};

class LargeTextViewer {
    constructor() {
        this.createModal();
    }

    init(params) {
        this.eGui = document.createElement('div');
        const text = params.value || "";
        this.eGui.innerText = text.length > 100 ? `${text.substring(0, 100)}...` : text;
        this.eGui.style.cursor = 'pointer';

        this.eGui.addEventListener('dblclick', () => {
            this.showLargeTextPopup(text);
        });
    }

    getGui() {
        return this.eGui;
    }

    createModal() {
        const modalHtml = `
      <div class="modal fade" id="largeTextModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="modalLabel">View</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body" style="white-space: pre-wrap;"></div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">close</button>
            </div>
          </div>
        </div>
      </div>
    `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    showLargeTextPopup(text) {
        const modalBody = document.querySelector('#largeTextModal .modal-body');
        modalBody.innerHTML = text || '(无内容)';

        $('#largeTextModal').modal('show');

        $('#largeTextModal').on('hidden.bs.modal', function () {
            $(this).remove();
        });
    }
};

document.querySelectorAll('input[type="text"], input[type="password"]').forEach(function (input) {
    input.setAttribute('autocomplete', 'off');
});

//通用获取列的唯一值函数
function getUniqueValues(params) {
    // 获取当前页面的行数据
    //const rowNodes = params.api.getRenderedNodes();
    const uniqueValues = new Set();
    //// 根据当前列字段收集唯一值
    //rowNodes.forEach(node => {
    //    if (node.data && node.data[params.colDef.field] !== undefined) {
    //        uniqueValues.add(node.data[params.colDef.field]);
    //    }
    //});

    //params.api.forEachNode(function (node) {
    //    if (node.data && node.data[params.colDef.field] !== undefined) {
    //        uniqueValues.add(node.data[params.colDef.field]);
    //    }
    //});


    const currentPage = params.api.paginationGetCurrentPage();
    const pageSize = params.api.paginationGetPageSize();
    const startIndex = currentPage * pageSize;
    const endIndex = startIndex + pageSize;

    for (let i = startIndex; i < endIndex; i++) {
        const rowNode = params.api.getDisplayedRowAtIndex(i);
        if (rowNode) {
            if (rowNode.data && rowNode.data[params.colDef.field] !== undefined) {
                uniqueValues.add(rowNode.data[params.colDef.field]);
            }
        }
    }

    // 将唯一值转换为数组并传递给 AG Grid
    params.success(Array.from(uniqueValues));
}

function getCurrentPageIds(gridOptions) {


    return ids;
}

function createDataSource(url, requestParamsFunc) {
    return {
        getRows: function (params) {
            // 获取动态请求参数
            const requestParams = requestParamsFunc();
            const filterModel = params.request.filterModel; // 获取当前的筛选条件
            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    ...requestParams,
                    pageSize: params.request.endRow - params.request.startRow,
                    currentPage: Math.floor(params.request.startRow / (params.request.endRow - params.request.startRow)) + 1,
                },
                success: function (data) {
                    const processedData = data.data;

                    // 排序逻辑
                    if (params.request.sortModel && params.request.sortModel.length > 0) {
                        processedData.sort((a, b) => {
                            for (let sort of params.request.sortModel) {
                                const { colId, sort: sortDirection } = sort;
                                const direction = sortDirection === 'asc' ? 1 : -1;

                                if (a[colId] < b[colId]) return -1 * direction;
                                if (a[colId] > b[colId]) return 1 * direction;
                            }
                            return 0;
                        });
                    }

                    if (Object.keys(filterModel).length) {
                        const filteredData = processedData.filter(item => {
                            // 根据 filterModel 手动筛选当前页数据
                            for (const key in filterModel) {
                                const filter = filterModel[key];
                                if (filter.values && !filter.values.includes(item[key])) {
                                    return false; // 不满足条件，排除该项
                                }
                            }
                            return true; // 满足所有条件，保留该项
                        });
                        params.success({
                            rowData: filteredData
                        });
                        return;
                    }

                    // 调用成功回调
                    params.success({
                        rowData: processedData,
                        rowCount: data.count, // 假设服务器返回总行数
                    });
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error('Error fetching data:', textStatus, errorThrown);
                    if (jqXHR.responseText) {
                        myAlert.message.error(jqXHR.responseText);
                    }
                    params.fail(); // 触发失败回调
                }
            });
        }
    };
}

function createDataSourceV2(url, requestParamsFunc, params) {
    // 获取动态请求参数
    const requestParams = requestParamsFunc;
    const filterModel = params.request.filterModel; // 获取当前的筛选条件
    $.ajax({
        url: url,
        type: 'POST',
        data: {
            ...requestParams,
            pageSize: params.request.endRow - params.request.startRow,
            currentPage: Math.floor(params.request.startRow / (params.request.endRow - params.request.startRow)) + 1,
        },
        success: function (data) {
            const processedData = data.data;

            // 排序逻辑
            if (params.request.sortModel && params.request.sortModel.length > 0) {
                processedData.sort((a, b) => {
                    for (let sort of params.request.sortModel) {
                        const { colId, sort: sortDirection } = sort;
                        const direction = sortDirection === 'asc' ? 1 : -1;

                        if (a[colId] < b[colId]) return -1 * direction;
                        if (a[colId] > b[colId]) return 1 * direction;
                    }
                    return 0;
                });
            }

            if (Object.keys(filterModel).length) {
                const filteredData = processedData.filter(item => {
                    // 根据 filterModel 手动筛选当前页数据
                    for (const key in filterModel) {
                        const filter = filterModel[key];
                        if (filter.values && !filter.values.includes(item[key])) {
                            return false; // 不满足条件，排除该项
                        }
                    }
                    return true; // 满足所有条件，保留该项
                });
                params.success({
                    rowData: filteredData
                });
                return;
            }

            // 调用成功回调
            params.success({
                rowData: processedData,
                rowCount: data.count, // 假设服务器返回总行数
            });
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.error('Error fetching data:', textStatus, errorThrown);
            if (jqXHR.responseText) {
                myAlert.message.error(jqXHR.responseText);
            }
            params.fail(); // 触发失败回调
        }
    });
}


function isDateValue(value) {
    if (value instanceof Date) {
        // 已经是 Date 类型
        return true;
    }

    // 如果是字符串类型，尝试将其转换为日期
    if (typeof value === 'string') {
        // 常见日期格式的正则表达式，可以根据需要修改
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;  // 比如 "2024-12-11"
        if (!dateRegex.test(value)) {
            return false;
        }
        // 尝试解析并验证日期的有效性
        const date = new Date(value);

        if (isNaN(date.getTime())) {
            // 解析失败或日期无效
            return false;
        }

        // 提取年、月、日部分进行进一步验证（仅适用于 YYYY-MM-DD 格式）
        if (dateRegex.exec(value)[1]) { // 检查是否匹配 YYYY-MM-DD 部分
            const [year, month, day] = dateRegex.exec(value)[1].split('-').map(Number);

            // 验证解析后的日期是否有效，并且年、月、日部分没有被改变
            if (
                date.getFullYear() !== year ||
                date.getMonth() !== month - 1 ||
                date.getDate() !== day
            ) {
                // 解析失败或日期无效
                return false;
            }
        }

        return true;

    }

    return false;
}
function toChinaStandardTime(dateString) {
    const date = new Date(dateString);
    const timeOffset = date.getTimezoneOffset() + 480; // 480 minutes is 8 hours in minutes
    const chinaDate = new Date(date.getTime() - timeOffset * 60000);
    return chinaDate;
}
//自定义列表显示
class CustomTooltip {
    eGui;
    init(params) {
        var eGui = (this.eGui = document.createElement('div'));

        eGui.classList.add('custom-tooltip');

        if (params.location === 'setFilterValue') {
            eGui.innerHTML = '<strong>Full value:</strong> ' + params.value;
        } else {
            eGui.innerHTML = params.value;
        }
    }

    getGui() {
        return this.eGui;
    }
}