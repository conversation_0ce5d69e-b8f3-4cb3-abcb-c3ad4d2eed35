@font-face {
  font-family: "agGridMaterial";
  src: url(data:font/woff2;charset=utf-8;base64,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);
  font-weight: normal;
  font-style: normal;
}
.ag-theme-material,
.ag-theme-material-dark,
.ag-theme-material-auto-dark {
  --ag-material-primary-color: #3f51b5;
  --ag-material-accent-color: #ff4081;
  --ag-foreground-color: rgba(0, 0, 0, 0.87);
  --ag-secondary-foreground-color: rgba(0, 0, 0, 0.54);
  --ag-disabled-foreground-color: rgba(0, 0, 0, 0.38);
  --ag-background-color: #fff;
  --ag-header-background-color: #fff;
  --ag-tooltip-background-color: #fff;
  --ag-subheader-background-color: #eee;
  --ag-subheader-toolbar-background-color: rgba(238, 238, 238, 0.5);
  --ag-header-cell-hover-background-color: #f2f2f2;
  --ag-chip-background-color: #e2e2e2;
  --ag-range-selection-background-color: rgba(122, 134, 203, 0.1);
  --ag-range-selection-background-color-2: rgba(122, 134, 203, 0.19);
  --ag-range-selection-background-color-3: rgba(122, 134, 203, 0.27);
  --ag-range-selection-background-color-4: rgba(122, 134, 203, 0.34);
  --ag-range-selection-highlight-color: #fce4ec;
  --ag-row-hover-color: #fafafa;
  --ag-column-hover-color: #fafafa;
  --ag-control-panel-background-color: #fafafa;
  --ag-selected-row-background-color: rgba(33, 150, 243, 0.3);
  --ag-checkbox-unchecked-color: #333;
  --ag-value-change-value-highlight-background-color: #00acc1;
  --ag-side-button-selected-background-color: transparent;
  --ag-advanced-filter-join-pill-color: #f08e8d;
  --ag-advanced-filter-column-pill-color: #a6e194;
  --ag-advanced-filter-option-pill-color: #f3c08b;
  --ag-advanced-filter-value-pill-color: #85c0e4;
  --ag-range-selection-border-color: var(--ag-material-primary-color);
  --ag-checkbox-checked-color: var(--ag-material-accent-color);
  --ag-borders: none;
  --ag-borders-critical: solid 1px;
  --ag-border-color: #e2e2e2;
  --ag-grid-size: 8px;
  --ag-icon-size: 18px;
  --ag-header-height: calc(var(--ag-grid-size) * 7);
  --ag-row-height: calc(var(--ag-grid-size) * 6);
  --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
  --ag-list-item-height: calc(var(--ag-grid-size) * 4);
  --ag-row-group-indent-size: calc(var(--ag-grid-size) * 3 + var(--ag-icon-size));
  --ag-filter-tool-panel-sub-level-row-height: calc(var(--ag-grid-size) * 4);
  --ag-checkbox-border-radius: 2px;
  --ag-toggle-button-switch-border-width: 2px;
  --ag-toggle-button-height: var(--ag-icon-size);
  --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 1.5);
  --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 2);
  --ag-widget-vertical-spacing: calc(var(--ag-grid-size) * 1.75);
  --ag-font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Oxygen-Sans, Ubuntu, Cantarell,
      "Helvetica Neue", sans-serif;
  --ag-font-size: 13px;
  --ag-icon-font-family: agGridMaterial;
  --ag-selected-tab-underline-color: var(--ag-material-primary-color);
  --ag-selected-tab-underline-width: 2px;
  --ag-input-focus-border-color: var(--ag-material-primary-color);
  --ag-input-focus-box-shadow: 0 0 0 5px rgba(32, 33, 36, 0.122);
  --ag-card-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
      0 1px 5px 0 rgba(0, 0, 0, 0.12);
  --ag-card-radius: 2px;
  --ag-invalid-color: #e02525;
}

.ag-theme-material-dark {
  --ag-material-primary-color: #3f51b5;
  --ag-material-accent-color: #bb86fcff;
  --ag-range-selection-border-color: var(--ag-material-accent-color);
  --ag-background-color: #121212ff;
  --ag-foreground-color: #ffffffff;
  --ag-data-color: #f5f5f5ff;
  --ag-header-cell-hover-background-color: #000000ff;
  --ag-advanced-filter-join-pill-color: #7a3a37ff;
  --ag-advanced-filter-column-pill-color: #355f2dff;
  --ag-advanced-filter-option-pill-color: #5a3168ff;
  --ag-advanced-filter-value-pill-color: #374c86ff;
  --ag-input-disabled-border-color: #3a434eff;
  --ag-input-disabled-background-color: #68686e12;
  --ag-selected-row-background-color: #bb86fc33;
  --ag-row-hover-color: #bb86fc33;
  --ag-column-hover-color: #f5f5f50d;
  --ag-range-selection-background-color: #bb86fc1a;
  --ag-range-selection-background-color-2: #bb86fc30;
  --ag-range-selection-background-color-3: #bb86fc45;
  --ag-range-selection-background-color-4: #bb86fc57;
  --ag-border-color: #383838ff;
  --ag-secondary-border-color: #383838ff;
  --ag-header-background-color: #121212ff;
  --ag-tooltip-background-color: #212b38ff;
  --ag-odd-row-background-color: #121212ff;
  --ag-control-panel-background-color: #2c2c2cff;
  --ag-subheader-background-color: #ffffff0d;
  --ag-subheader-toolbar-background-color: #2c2c2cff;
  --ag-invalid-color: #e02525ff;
  --ag-checkbox-unchecked-color: #797e87ff;
  --ag-checkbox-background-color: #121212ff;
  --ag-secondary-foreground-color: #f5f5f5ff;
  --ag-input-border-color: #383838ff;
  --ag-input-border-color-invalid: #e02525ff;
  --ag-disabled-foreground-color: #f5f5f580;
  --ag-chip-background-color: #22262812;
  --ag-side-button-selected-background-color: #2c2c2cff;
  --ag-selected-tab-underline-color: #3f51b5ff;
  --ag-modal-overlay-background-color: #121212a8;
  --ag-value-change-delta-up-color: #43a047a8;
  --ag-value-change-delta-down-color: #e53935ff;
  --ag-menu-background-color: #2c2c2cff;
  --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
  color-scheme: dark;
}

@media (prefers-color-scheme: dark) {
  .ag-theme-material-auto-dark {
    --ag-material-primary-color: #3f51b5;
    --ag-material-accent-color: #bb86fcff;
    --ag-range-selection-border-color: var(--ag-material-accent-color);
    --ag-background-color: #121212ff;
    --ag-foreground-color: #ffffffff;
    --ag-data-color: #f5f5f5ff;
    --ag-header-cell-hover-background-color: #000000ff;
    --ag-advanced-filter-join-pill-color: #7a3a37ff;
    --ag-advanced-filter-column-pill-color: #355f2dff;
    --ag-advanced-filter-option-pill-color: #5a3168ff;
    --ag-advanced-filter-value-pill-color: #374c86ff;
    --ag-input-disabled-border-color: #3a434eff;
    --ag-input-disabled-background-color: #68686e12;
    --ag-selected-row-background-color: #bb86fc33;
    --ag-row-hover-color: #bb86fc33;
    --ag-column-hover-color: #f5f5f50d;
    --ag-range-selection-background-color: #bb86fc1a;
    --ag-range-selection-background-color-2: #bb86fc30;
    --ag-range-selection-background-color-3: #bb86fc45;
    --ag-range-selection-background-color-4: #bb86fc57;
    --ag-border-color: #383838ff;
    --ag-secondary-border-color: #383838ff;
    --ag-header-background-color: #121212ff;
    --ag-tooltip-background-color: #212b38ff;
    --ag-odd-row-background-color: #121212ff;
    --ag-control-panel-background-color: #2c2c2cff;
    --ag-subheader-background-color: #ffffff0d;
    --ag-subheader-toolbar-background-color: #2c2c2cff;
    --ag-invalid-color: #e02525ff;
    --ag-checkbox-unchecked-color: #797e87ff;
    --ag-checkbox-background-color: #121212ff;
    --ag-secondary-foreground-color: #f5f5f5ff;
    --ag-input-border-color: #383838ff;
    --ag-input-border-color-invalid: #e02525ff;
    --ag-disabled-foreground-color: #f5f5f580;
    --ag-chip-background-color: #22262812;
    --ag-side-button-selected-background-color: #2c2c2cff;
    --ag-selected-tab-underline-color: #3f51b5ff;
    --ag-modal-overlay-background-color: #121212a8;
    --ag-value-change-delta-up-color: #43a047a8;
    --ag-value-change-delta-down-color: #e53935ff;
    --ag-menu-background-color: #2c2c2cff;
    --ag-row-loading-skeleton-effect-color: rgba(202, 203, 204, 0.4);
    color-scheme: dark;
  }
}
.ag-theme-material .ag-filter-toolpanel-header,
.ag-theme-material .ag-filter-toolpanel-search,
.ag-theme-material .ag-status-bar,
.ag-theme-material .ag-header-row,
.ag-theme-material .ag-panel-title-bar-title,
.ag-theme-material .ag-multi-filter-group-title-bar,
.ag-theme-material-dark .ag-filter-toolpanel-header,
.ag-theme-material-dark .ag-filter-toolpanel-search,
.ag-theme-material-dark .ag-status-bar,
.ag-theme-material-dark .ag-header-row,
.ag-theme-material-dark .ag-panel-title-bar-title,
.ag-theme-material-dark .ag-multi-filter-group-title-bar,
.ag-theme-material-auto-dark .ag-filter-toolpanel-header,
.ag-theme-material-auto-dark .ag-filter-toolpanel-search,
.ag-theme-material-auto-dark .ag-status-bar,
.ag-theme-material-auto-dark .ag-header-row,
.ag-theme-material-auto-dark .ag-panel-title-bar-title,
.ag-theme-material-auto-dark .ag-multi-filter-group-title-bar {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 600;
  color: var(--ag-header-foreground-color);
}
.ag-theme-material .ag-tab,
.ag-theme-material-dark .ag-tab,
.ag-theme-material-auto-dark .ag-tab {
  height: calc(var(--ag-grid-size) * 4.5);
  flex: 1 1 auto;
}
.ag-theme-material .ag-tabs-header,
.ag-theme-material .ag-column-drop-horizontal,
.ag-theme-material-dark .ag-tabs-header,
.ag-theme-material-dark .ag-column-drop-horizontal,
.ag-theme-material-auto-dark .ag-tabs-header,
.ag-theme-material-auto-dark .ag-column-drop-horizontal {
  background-color: var(--ag-subheader-background-color);
}
.ag-theme-material .ag-tabs-body,
.ag-theme-material-dark .ag-tabs-body,
.ag-theme-material-auto-dark .ag-tabs-body {
  padding: calc(var(--ag-grid-size) * 0.5) 0;
}
.ag-theme-material .ag-tabs-body .ag-menu-list,
.ag-theme-material-dark .ag-tabs-body .ag-menu-list,
.ag-theme-material-auto-dark .ag-tabs-body .ag-menu-list {
  padding-top: 0;
  padding-bottom: 0;
}
.ag-theme-material .ag-header-cell,
.ag-theme-material .ag-header-group-cell,
.ag-theme-material-dark .ag-header-cell,
.ag-theme-material-dark .ag-header-group-cell,
.ag-theme-material-auto-dark .ag-header-cell,
.ag-theme-material-auto-dark .ag-header-group-cell {
  transition: background-color 0.5s;
}
.ag-theme-material .ag-row-last:not(.ag-row-first) .ag-cell-inline-editing,
.ag-theme-material-dark .ag-row-last:not(.ag-row-first) .ag-cell-inline-editing,
.ag-theme-material-auto-dark .ag-row-last:not(.ag-row-first) .ag-cell-inline-editing {
  bottom: 0;
}
.ag-theme-material .ag-cell-inline-editing,
.ag-theme-material-dark .ag-cell-inline-editing,
.ag-theme-material-auto-dark .ag-cell-inline-editing {
  padding: var(--ag-grid-size);
  height: calc(var(--ag-row-height) + var(--ag-grid-size) * 3);
  border-color: var(--ag-border-color) !important;
}
.ag-theme-material .ag-has-focus .ag-cell-inline-editing,
.ag-theme-material-dark .ag-has-focus .ag-cell-inline-editing,
.ag-theme-material-auto-dark .ag-has-focus .ag-cell-inline-editing {
  border-color: var(--ag-input-focus-border-color) !important;
}
.ag-theme-material .ag-column-drop-vertical,
.ag-theme-material-dark .ag-column-drop-vertical,
.ag-theme-material-auto-dark .ag-column-drop-vertical {
  border-bottom: solid 1px;
  border-bottom-color: var(--ag-border-color);
  padding-top: var(--ag-grid-size);
}
.ag-theme-material .ag-column-drop-vertical.ag-last-column-drop,
.ag-theme-material-dark .ag-column-drop-vertical.ag-last-column-drop,
.ag-theme-material-auto-dark .ag-column-drop-vertical.ag-last-column-drop {
  border-bottom: none;
}
.ag-theme-material .ag-column-drop-vertical-cell,
.ag-theme-material-dark .ag-column-drop-vertical-cell,
.ag-theme-material-auto-dark .ag-column-drop-vertical-cell {
  margin-left: 0;
}
.ag-theme-material .ag-column-drop-vertical-empty-message,
.ag-theme-material-dark .ag-column-drop-vertical-empty-message,
.ag-theme-material-auto-dark .ag-column-drop-vertical-empty-message {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 600;
  color: var(--ag-disabled-foreground-color);
}
.ag-theme-material .ag-ltr .ag-column-drop-vertical-empty-message, .ag-theme-material-dark .ag-ltr .ag-column-drop-vertical-empty-message, .ag-theme-material-auto-dark .ag-ltr .ag-column-drop-vertical-empty-message {
  padding-left: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  padding-right: var(--ag-grid-size);
}

.ag-theme-material .ag-rtl .ag-column-drop-vertical-empty-message, .ag-theme-material-dark .ag-rtl .ag-column-drop-vertical-empty-message, .ag-theme-material-auto-dark .ag-rtl .ag-column-drop-vertical-empty-message {
  padding-right: calc(var(--ag-icon-size) + var(--ag-grid-size) * 2);
  padding-left: var(--ag-grid-size);
}

.ag-theme-material .ag-status-bar,
.ag-theme-material-dark .ag-status-bar,
.ag-theme-material-auto-dark .ag-status-bar {
  border: solid 1px;
  border-color: var(--ag-border-color);
}
.ag-theme-material .ag-column-panel-column-select,
.ag-theme-material-dark .ag-column-panel-column-select,
.ag-theme-material-auto-dark .ag-column-panel-column-select {
  border-top: solid 1px;
  border-top-color: var(--ag-border-color);
}
.ag-theme-material .ag-column-select,
.ag-theme-material .ag-column-select-header,
.ag-theme-material-dark .ag-column-select,
.ag-theme-material-dark .ag-column-select-header,
.ag-theme-material-auto-dark .ag-column-select,
.ag-theme-material-auto-dark .ag-column-select-header {
  border-bottom: solid 1px;
  border-bottom-color: var(--ag-border-color);
}
.ag-theme-material .ag-column-select-header,
.ag-theme-material-dark .ag-column-select-header,
.ag-theme-material-auto-dark .ag-column-select-header {
  height: var(--ag-header-height);
}
.ag-theme-material .ag-group-title-bar,
.ag-theme-material-dark .ag-group-title-bar,
.ag-theme-material-auto-dark .ag-group-title-bar {
  padding: calc(var(--ag-grid-size) * 0.75) var(--ag-grid-size);
}
.ag-theme-material .ag-charts-format-sub-level-group-title-bar,
.ag-theme-material-dark .ag-charts-format-sub-level-group-title-bar,
.ag-theme-material-auto-dark .ag-charts-format-sub-level-group-title-bar {
  padding: calc(var(--ag-grid-size) * 0.5) var(--ag-grid-size);
}
.ag-theme-material .ag-chart-data-section,
.ag-theme-material .ag-chart-format-section,
.ag-theme-material .ag-chart-advanced-settings-section,
.ag-theme-material-dark .ag-chart-data-section,
.ag-theme-material-dark .ag-chart-format-section,
.ag-theme-material-dark .ag-chart-advanced-settings-section,
.ag-theme-material-auto-dark .ag-chart-data-section,
.ag-theme-material-auto-dark .ag-chart-format-section,
.ag-theme-material-auto-dark .ag-chart-advanced-settings-section {
  padding-bottom: calc(var(--ag-grid-size) * 0.5);
}
.ag-theme-material input[class^=ag-]:not([type]),
.ag-theme-material input[class^=ag-][type=text],
.ag-theme-material input[class^=ag-][type=number],
.ag-theme-material input[class^=ag-][type=tel],
.ag-theme-material input[class^=ag-][type=date],
.ag-theme-material input[class^=ag-][type=datetime-local],
.ag-theme-material textarea[class^=ag-],
.ag-theme-material-dark input[class^=ag-]:not([type]),
.ag-theme-material-dark input[class^=ag-][type=text],
.ag-theme-material-dark input[class^=ag-][type=number],
.ag-theme-material-dark input[class^=ag-][type=tel],
.ag-theme-material-dark input[class^=ag-][type=date],
.ag-theme-material-dark input[class^=ag-][type=datetime-local],
.ag-theme-material-dark textarea[class^=ag-],
.ag-theme-material-auto-dark input[class^=ag-]:not([type]),
.ag-theme-material-auto-dark input[class^=ag-][type=text],
.ag-theme-material-auto-dark input[class^=ag-][type=number],
.ag-theme-material-auto-dark input[class^=ag-][type=tel],
.ag-theme-material-auto-dark input[class^=ag-][type=date],
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local],
.ag-theme-material-auto-dark textarea[class^=ag-] {
  background: transparent;
  color: var(--ag-foreground-color);
  font-family: inherit;
  font-size: inherit;
  padding-bottom: var(--ag-grid-size);
  border-width: 0;
  border-radius: 0;
  border-bottom: 2px solid;
  border-bottom-color: var(--ag-border-color);
}
.ag-theme-material input[class^=ag-]:not([type]):not(textarea),
.ag-theme-material input[class^=ag-][type=text]:not(textarea),
.ag-theme-material input[class^=ag-][type=number]:not(textarea),
.ag-theme-material input[class^=ag-][type=tel]:not(textarea),
.ag-theme-material input[class^=ag-][type=date]:not(textarea),
.ag-theme-material input[class^=ag-][type=datetime-local]:not(textarea),
.ag-theme-material textarea[class^=ag-]:not(textarea),
.ag-theme-material-dark input[class^=ag-]:not([type]):not(textarea),
.ag-theme-material-dark input[class^=ag-][type=text]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=number]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=tel]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=date]:not(textarea),
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:not(textarea),
.ag-theme-material-dark textarea[class^=ag-]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=text]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=number]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=date]:not(textarea),
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:not(textarea),
.ag-theme-material-auto-dark textarea[class^=ag-]:not(textarea) {
  height: calc(var(--ag-grid-size) * 5);
}
.ag-theme-material input[class^=ag-]:not([type]):focus,
.ag-theme-material input[class^=ag-][type=text]:focus,
.ag-theme-material input[class^=ag-][type=number]:focus,
.ag-theme-material input[class^=ag-][type=tel]:focus,
.ag-theme-material input[class^=ag-][type=date]:focus,
.ag-theme-material input[class^=ag-][type=datetime-local]:focus,
.ag-theme-material textarea[class^=ag-]:focus,
.ag-theme-material-dark input[class^=ag-]:not([type]):focus,
.ag-theme-material-dark input[class^=ag-][type=text]:focus,
.ag-theme-material-dark input[class^=ag-][type=number]:focus,
.ag-theme-material-dark input[class^=ag-][type=tel]:focus,
.ag-theme-material-dark input[class^=ag-][type=date]:focus,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:focus,
.ag-theme-material-dark textarea[class^=ag-]:focus,
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):focus,
.ag-theme-material-auto-dark input[class^=ag-][type=text]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=number]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=date]:focus,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:focus,
.ag-theme-material-auto-dark textarea[class^=ag-]:focus {
  border-bottom: 2px solid;
  border-bottom-color: var(--ag-material-primary-color);
  outline: none;
  box-shadow: none;
}
.ag-theme-material input[class^=ag-]:not([type])::placeholder,
.ag-theme-material input[class^=ag-][type=text]::placeholder,
.ag-theme-material input[class^=ag-][type=number]::placeholder,
.ag-theme-material input[class^=ag-][type=tel]::placeholder,
.ag-theme-material input[class^=ag-][type=date]::placeholder,
.ag-theme-material input[class^=ag-][type=datetime-local]::placeholder,
.ag-theme-material textarea[class^=ag-]::placeholder,
.ag-theme-material-dark input[class^=ag-]:not([type])::placeholder,
.ag-theme-material-dark input[class^=ag-][type=text]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=number]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=tel]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=date]::placeholder,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]::placeholder,
.ag-theme-material-dark textarea[class^=ag-]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-]:not([type])::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=text]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=number]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=date]::placeholder,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]::placeholder,
.ag-theme-material-auto-dark textarea[class^=ag-]::placeholder {
  color: var(--ag-disabled-foreground-color);
}
.ag-theme-material input[class^=ag-]:not([type]):disabled,
.ag-theme-material input[class^=ag-][type=text]:disabled,
.ag-theme-material input[class^=ag-][type=number]:disabled,
.ag-theme-material input[class^=ag-][type=tel]:disabled,
.ag-theme-material input[class^=ag-][type=date]:disabled,
.ag-theme-material input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-material textarea[class^=ag-]:disabled,
.ag-theme-material-dark input[class^=ag-]:not([type]):disabled,
.ag-theme-material-dark input[class^=ag-][type=text]:disabled,
.ag-theme-material-dark input[class^=ag-][type=number]:disabled,
.ag-theme-material-dark input[class^=ag-][type=tel]:disabled,
.ag-theme-material-dark input[class^=ag-][type=date]:disabled,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-material-dark textarea[class^=ag-]:disabled,
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=text]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=number]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=date]:disabled,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:disabled,
.ag-theme-material-auto-dark textarea[class^=ag-]:disabled {
  border-bottom: 1px solid;
  border-bottom-color: var(--ag-border-color);
}
.ag-theme-material input[class^=ag-]:not([type]):invalid,
.ag-theme-material input[class^=ag-][type=text]:invalid,
.ag-theme-material input[class^=ag-][type=number]:invalid,
.ag-theme-material input[class^=ag-][type=tel]:invalid,
.ag-theme-material input[class^=ag-][type=date]:invalid,
.ag-theme-material input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-material textarea[class^=ag-]:invalid,
.ag-theme-material-dark input[class^=ag-]:not([type]):invalid,
.ag-theme-material-dark input[class^=ag-][type=text]:invalid,
.ag-theme-material-dark input[class^=ag-][type=number]:invalid,
.ag-theme-material-dark input[class^=ag-][type=tel]:invalid,
.ag-theme-material-dark input[class^=ag-][type=date]:invalid,
.ag-theme-material-dark input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-material-dark textarea[class^=ag-]:invalid,
.ag-theme-material-auto-dark input[class^=ag-]:not([type]):invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=text]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=number]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=tel]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=date]:invalid,
.ag-theme-material-auto-dark input[class^=ag-][type=datetime-local]:invalid,
.ag-theme-material-auto-dark textarea[class^=ag-]:invalid {
  border-width: 0;
  border-bottom: 1px solid;
  border-bottom-color: var(--ag-invalid-color);
  color: var(--ag-invalid-color);
}
.ag-theme-material .ag-standard-button,
.ag-theme-material-dark .ag-standard-button,
.ag-theme-material-auto-dark .ag-standard-button {
  appearance: none;
  background-color: transparent;
  border: 0;
  color: var(--ag-material-primary-color);
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  padding: 0;
  text-transform: uppercase;
}
.ag-theme-material .ag-standard-button:disabled,
.ag-theme-material-dark .ag-standard-button:disabled,
.ag-theme-material-auto-dark .ag-standard-button:disabled {
  color: var(--ag-disabled-foreground-color);
  background-color: var(--ag-input-disabled-background-color);
  border-color: var(--ag-input-disabled-border-color);
}
.ag-theme-material.ag-dnd-ghost,
.ag-theme-material-dark.ag-dnd-ghost,
.ag-theme-material-auto-dark.ag-dnd-ghost {
  font-size: calc(var(--ag-font-size) - 1px);
  font-weight: 600;
}
.ag-theme-material .ag-filter-toolpanel-header,
.ag-theme-material-dark .ag-filter-toolpanel-header,
.ag-theme-material-auto-dark .ag-filter-toolpanel-header {
  height: calc(var(--ag-grid-size) * 4);
}
.ag-theme-material .ag-filter-toolpanel-group-level-0-header,
.ag-theme-material-dark .ag-filter-toolpanel-group-level-0-header,
.ag-theme-material-auto-dark .ag-filter-toolpanel-group-level-0-header {
  height: calc(var(--ag-grid-size) * 7);
}
.ag-theme-material .ag-ltr .ag-filter-apply-panel-button, .ag-theme-material .ag-ltr .ag-advanced-filter-apply-button, .ag-theme-material .ag-ltr .ag-advanced-filter-builder-button, .ag-theme-material-dark .ag-ltr .ag-filter-apply-panel-button, .ag-theme-material-dark .ag-ltr .ag-advanced-filter-apply-button, .ag-theme-material-dark .ag-ltr .ag-advanced-filter-builder-button, .ag-theme-material-auto-dark .ag-ltr .ag-filter-apply-panel-button, .ag-theme-material-auto-dark .ag-ltr .ag-advanced-filter-apply-button, .ag-theme-material-auto-dark .ag-ltr .ag-advanced-filter-builder-button {
  margin-left: var(--ag-grid-size);
}

.ag-theme-material .ag-rtl .ag-filter-apply-panel-button, .ag-theme-material .ag-rtl .ag-advanced-filter-apply-button, .ag-theme-material .ag-rtl .ag-advanced-filter-builder-button, .ag-theme-material-dark .ag-rtl .ag-filter-apply-panel-button, .ag-theme-material-dark .ag-rtl .ag-advanced-filter-apply-button, .ag-theme-material-dark .ag-rtl .ag-advanced-filter-builder-button, .ag-theme-material-auto-dark .ag-rtl .ag-filter-apply-panel-button, .ag-theme-material-auto-dark .ag-rtl .ag-advanced-filter-apply-button, .ag-theme-material-auto-dark .ag-rtl .ag-advanced-filter-builder-button {
  margin-right: var(--ag-grid-size);
}

.ag-theme-material .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-material .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material .ag-layout-print .ag-center-cols-viewport,
.ag-theme-material .ag-layout-print .ag-center-cols-container,
.ag-theme-material-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-material-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-material-dark .ag-layout-print .ag-center-cols-container,
.ag-theme-material-auto-dark .ag-layout-auto-height .ag-center-cols-viewport,
.ag-theme-material-auto-dark .ag-layout-auto-height .ag-center-cols-container,
.ag-theme-material-auto-dark .ag-layout-print .ag-center-cols-viewport,
.ag-theme-material-auto-dark .ag-layout-print .ag-center-cols-container {
  min-height: 150px;
}
.ag-theme-material .ag-picker-field-wrapper:focus-within,
.ag-theme-material-dark .ag-picker-field-wrapper:focus-within,
.ag-theme-material-auto-dark .ag-picker-field-wrapper:focus-within {
  box-shadow: 0 0 0 1px var(--ag-material-primary-color);
}
.ag-theme-material .ag-rich-select-list,
.ag-theme-material-dark .ag-rich-select-list,
.ag-theme-material-auto-dark .ag-rich-select-list {
  box-shadow: rgba(0, 0, 0, 0.2) 0px 5px 5px -3px, rgba(0, 0, 0, 0.14) 0px 8px 10px 1px, rgba(0, 0, 0, 0.12) 0px 3px 14px 2px;
}
.ag-theme-material .ag-advanced-filter-builder-button-label,
.ag-theme-material-dark .ag-advanced-filter-builder-button-label,
.ag-theme-material-auto-dark .ag-advanced-filter-builder-button-label {
  text-transform: uppercase;
}
.ag-theme-material .ag-filter-active .ag-icon-filter,
.ag-theme-material-dark .ag-filter-active .ag-icon-filter,
.ag-theme-material-auto-dark .ag-filter-active .ag-icon-filter {
  color: var(--ag-material-accent-color);
}
