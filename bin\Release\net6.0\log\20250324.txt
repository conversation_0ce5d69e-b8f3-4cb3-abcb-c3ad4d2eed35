2025-03-24 08:56:16,842 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (88ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 08:56:16,842 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (20ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 08:56:20,988 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (18ms) [Parameters=[@__model_Account_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 08:56:21,185 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__userInfo_user_name_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-03-24 08:56:21,444 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:21,448 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:21,509 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:26,509 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:26,510 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:29,103 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (18ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:29,126 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:31,044 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (48ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:31,079 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:32,542 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (14ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:56:32,542 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (14ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:19,836 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (155ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 08:57:20,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 08:57:20,284 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__userInfo_user_name_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-03-24 08:57:20,525 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (14ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:20,527 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:20,591 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:22,559 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:22,560 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:31,130 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (18ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:31,174 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:33,235 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (19ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:33,275 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:35,171 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 08:57:35,173 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:41,136 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (154ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 09:02:41,137 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (93ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 09:02:41,137 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (32ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 09:02:46,236 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (22ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-03-24 09:02:46,428 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__userInfo_user_name_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-03-24 09:02:46,624 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:46,625 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:46,626 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:49,679 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:49,701 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:53,574 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (18ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:53,642 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:56,496 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:02:56,497 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (19ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:03:04,103 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (19ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:03:04,130 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:03:06,561 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (20ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:03:06,589 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (17ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:03:08,104 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (19ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:03:08,104 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (19ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:10:13,316 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:10:13,339 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:10:13,375 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (14ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:10:13,523 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (15ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-03-24 09:10:13,524 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (16ms) [Parameters=[@__username_0='ADMIN' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
