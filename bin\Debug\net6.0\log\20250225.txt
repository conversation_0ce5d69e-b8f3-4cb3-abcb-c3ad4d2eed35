2025-02-25 09:10:44,756 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (46ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:10:44,756 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (46ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:10:44,756 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (46ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:10:44,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:10:44,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:10:44,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:10:45,202 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:10:45,202 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:10:45,202 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:12:38,712 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:12:38,713 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:12:38,713 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:20:31,684 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (44ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:20:31,765 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:20:31,865 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:20:31,867 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:20:31,965 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:20:31,967 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:20:31,969 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:28:06,638 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (61ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:28:06,745 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:28:07,046 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:28:07,048 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:28:07,048 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:29:31,669 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:29:31,669 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:29:31,673 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:31:10,993 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:31:10,993 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:31:10,993 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:32:28,288 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:32:28,288 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:32:28,288 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:18,778 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:18,778 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:18,779 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:32,316 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:32,316 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:32,317 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:57,393 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:57,393 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:33:57,393 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:45:46,654 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:45:46,656 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:45:46,656 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:45:51,420 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.TypeLoadException: Could not load type 'Invalid_Token.0x02000114' from assembly 'OpenAuth.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5
   at lambda_method567(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 09:46:16,013 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.TypeLoadException: Could not load type 'Invalid_Token.0x02000114' from assembly 'OpenAuth.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5
   at lambda_method567(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 09:48:00,340 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:48:00,340 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:48:00,344 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:48:21,337 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:48:21,337 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:48:21,338 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:48:23,938 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.TypeLoadException: Could not load type 'Invalid_Token.0x02000114' from assembly 'OpenAuth.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5
   at lambda_method567(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 09:50:40,373 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:50:40,373 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:50:40,374 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:50:47,628 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.TypeLoadException: Could not load type 'Invalid_Token.0x02000114' from assembly 'OpenAuth.Mvc, Version=*******, Culture=neutral, PublicKeyToken=null'.
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5
   at lambda_method567(Closure , Object , Object[] )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 09:56:22,057 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (61ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 09:56:22,157 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 09:56:22,428 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:56:22,429 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 09:56:22,432 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:04:38,518 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:04:38,519 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:04:38,521 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:09:00,846 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:09:00,846 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:09:00,863 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:10:47,172 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:10:47,172 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:10:47,174 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:11:34,562 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:11:34,563 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:11:34,563 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:12:38,860 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:12:38,860 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:12:38,868 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:14:49,296 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:14:49,296 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:14:49,302 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:20:39,770 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:20:39,771 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:20:39,774 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:21:04,638 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:21:04,638 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:21:04,638 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:28:27,917 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:28:27,917 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:28:27,920 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:44:33,583 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:44:33,585 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:44:33,588 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:45:28,085 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:45:28,087 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:45:28,087 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:45:30,826 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:45:30,826 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:45:30,826 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:46:48,492 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:46:48,535 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:46:48,579 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:46:54,617 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:46:54,620 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:46:54,620 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:47:03,122 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:47:03,122 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:47:03,129 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:47:14,266 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:47:14,266 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:47:14,268 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:15,021 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:15,025 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:15,025 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:15,156 [14] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#14.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#14.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 10:48:18,994 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:18,994 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:18,995 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:19,005 [7] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#14.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#14.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 10:48:28,027 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:28,027 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:48:28,028 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:49:20,971 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:49:20,971 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:49:20,973 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:53:57,911 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:53:57,912 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:53:57,914 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:57:36,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:57:36,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:57:36,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:58:33,704 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (50ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 10:58:33,792 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 10:58:34,032 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:58:34,034 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:58:34,037 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 10:59:20,462 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.Text.Json.JsonException: 'S' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: 'S' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5Stream() in D:\代码\Web_OV2\OpenAuth.Mvc\Controllers\AI\AIController.cs:line 84
   at lambda_method405(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 11:00:10,245 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.Text.Json.JsonException: 'S' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: 'S' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5Stream() in D:\代码\Web_OV2\OpenAuth.Mvc\Controllers\AI\AIController.cs:line 84
   at lambda_method405(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 11:00:25,347 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.Text.Json.JsonException: 'S' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: 'S' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5Stream() in D:\代码\Web_OV2\OpenAuth.Mvc\Controllers\AI\AIController.cs:line 84
   at lambda_method405(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 11:00:27,671 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:00:27,678 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (10ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:00:27,681 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (10ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:00:40,805 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.Text.Json.JsonException: 'S' is an invalid start of a value. Path: $ | LineNumber: 0 | BytePositionInLine: 0.
 ---> System.Text.Json.JsonReaderException: 'S' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   --- End of inner exception stack trace ---
   at System.Text.Json.ThrowHelper.ReThrowWithPath(ReadStack& state, JsonReaderException ex)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5Stream() in D:\代码\Web_OV2\OpenAuth.Mvc\Controllers\AI\AIController.cs:line 84
   at lambda_method405(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 11:00:59,391 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:00:59,391 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:00:59,391 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:02:38,156 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:02:38,156 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:02:38,156 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:03:01,046 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.Text.Json.JsonException: The JSON value could not be converted to OpenAuth.Mvc.Controllers.AI.AIController+ChatRequest. Path: $ | LineNumber: 0 | BytePositionInLine: 1.
   at System.Text.Json.ThrowHelper.ThrowJsonException_DeserializeUnableToConvertValue(Type propertyType)
   at System.Text.Json.Serialization.Converters.ObjectDefaultConverter`1.OnTryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value)
   at System.Text.Json.Serialization.JsonConverter`1.TryRead(Utf8JsonReader& reader, Type typeToConvert, JsonSerializerOptions options, ReadStack& state, T& value, Boolean& isPopulatedValue)
   at System.Text.Json.Serialization.JsonConverter`1.ReadCore(Utf8JsonReader& reader, JsonSerializerOptions options, ReadStack& state)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 utf8Json, JsonTypeInfo`1 jsonTypeInfo, Nullable`1 actualByteCount)
   at System.Text.Json.JsonSerializer.ReadFromSpan[TValue](ReadOnlySpan`1 json, JsonTypeInfo`1 jsonTypeInfo)
   at System.Text.Json.JsonSerializer.Deserialize[TValue](String json, JsonSerializerOptions options)
   at OpenAuth.Mvc.Controllers.AI.AIController.SendMessage5Stream()
   at lambda_method483(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 11:05:08,706 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:05:08,706 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:05:08,706 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:05:30,341 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (38ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 11:05:30,408 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 11:05:30,646 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:05:30,649 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:05:30,650 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:06:30,790 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:06:30,790 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:06:30,792 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:16:18,988 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:16:18,992 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:16:18,994 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:16:21,927 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:16:21,927 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:16:21,927 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:17:35,060 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:17:35,060 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:17:35,063 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:17:47,524 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:17:47,524 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:17:47,525 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:18:21,318 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:18:21,318 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:18:21,321 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:19:16,680 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:19:16,680 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:19:16,691 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:20:34,542 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:20:34,544 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:20:34,550 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:21:51,458 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:21:51,458 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:21:51,458 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:24:55,612 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:24:55,616 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:24:55,617 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:26:05,970 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:26:05,970 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:26:05,974 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:27:04,711 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:27:04,711 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:27:04,721 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:27:22,443 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:27:22,445 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:27:22,449 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:30:59,972 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:30:59,975 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:30:59,976 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:56:38,721 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:56:38,724 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:56:38,725 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:00,576 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:00,579 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:00,587 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:27,862 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:27,862 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:27,863 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:39,048 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:39,048 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 11:59:39,049 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:07,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:07,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:07,060 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:18,548 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:18,548 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:18,551 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:45,887 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:45,889 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:00:45,891 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:03:09,912 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:03:09,912 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:03:09,916 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:03:32,861 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (40ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 12:03:32,927 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 12:03:33,141 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:03:33,143 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:03:33,145 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:04:10,649 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:04:10,649 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:04:10,649 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:08:06,125 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:08:06,125 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:08:06,128 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:09:09,327 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:09:09,327 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:09:09,330 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:09:37,372 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:09:37,372 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:09:37,372 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:10:05,532 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:10:05,532 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:10:05,535 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:13:05,410 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:13:05,426 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:13:05,427 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:20:04,093 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:20:04,093 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:20:04,095 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:20:22,366 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:20:22,366 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:20:22,370 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:24:19,223 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:24:19,223 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:24:19,226 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:25:31,645 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:25:31,648 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:25:31,648 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:21,563 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:21,563 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:21,567 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:22,191 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:22,191 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:22,195 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:47,728 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:47,729 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:26:47,731 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:27:02,012 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:27:02,012 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:27:02,015 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:27:10,905 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:27:10,908 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 12:27:10,921 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:08:17,475 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:08:17,477 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:08:17,480 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:09:00,208 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:09:00,212 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:09:00,213 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:12:16,427 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:12:16,427 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:12:16,432 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:15:26,991 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:15:27,002 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:15:27,014 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:18:38,559 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:18:38,562 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:18:38,564 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:20:48,928 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:20:48,931 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:20:48,933 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:21:55,471 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:21:55,471 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:21:55,476 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:22:55,761 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:22:55,761 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:22:55,763 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:26:04,300 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (7ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:26:04,302 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:26:04,302 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:31:33,094 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:31:33,095 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:31:33,099 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:32:15,755 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:32:15,755 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:32:15,755 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:33:19,363 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:33:19,363 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:33:19,372 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:34:46,942 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:34:46,942 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:34:46,942 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:39,360 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:39,360 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:39,360 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:45,809 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:45,809 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:45,812 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:51,282 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:51,285 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:35:51,287 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:36:04,379 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:36:04,379 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:36:04,381 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:38:49,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:38:49,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:38:49,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:40:01,942 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:40:01,946 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:40:01,946 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:41:36,787 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:41:36,787 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:41:36,795 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:42:01,966 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:42:01,966 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:42:01,970 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:42:43,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:42:43,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:42:43,053 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:43:38,753 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:43:38,753 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:43:38,756 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:44:04,136 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:44:04,136 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:44:04,142 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:49:45,790 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:49:45,791 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:49:45,795 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:50:00,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:50:00,245 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:50:00,246 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:50:14,225 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:50:14,225 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:50:14,226 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:52:16,564 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:52:16,564 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:52:16,569 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:52:20,770 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:52:20,770 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:52:20,771 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:53:28,197 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:53:28,197 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:53:28,202 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:53:49,284 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:53:49,298 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:53:49,300 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:57:47,876 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:57:47,877 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 13:57:47,881 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:01:24,823 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:01:24,825 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:01:24,823 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:02:51,300 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:02:51,310 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:02:51,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:04:19,748 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:04:19,748 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:04:19,752 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:05:59,818 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:05:59,818 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:05:59,821 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:06:17,360 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:06:17,360 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:06:17,365 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:17,898 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:17,899 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:17,902 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:30,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:30,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:30,311 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:34,715 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:34,715 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:12:34,715 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:16:51,783 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:16:51,783 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:16:51,783 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:17:04,711 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:17:04,711 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:17:04,711 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:17:32,808 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:17:32,808 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:17:32,815 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:02,442 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:02,442 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:02,446 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:05,495 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:05,495 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:05,495 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:47,008 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:47,009 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:18:47,009 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:21:16,235 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:21:16,236 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:21:16,236 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:28:15,425 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:28:15,427 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:28:15,427 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:29:17,638 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:29:17,638 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:29:17,641 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:31:30,473 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:31:30,473 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:31:30,477 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:31:50,553 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:31:50,553 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:31:50,557 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:32:13,175 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:32:13,176 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:32:13,177 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:32:51,021 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:32:51,021 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:32:51,021 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:41,914 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:41,914 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:41,918 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:49,826 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (6ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:49,829 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:49,829 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:34:49,959 [222] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:36:47,121 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:47,126 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:47,121 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:47,193 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:36:48,652 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:48,652 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:48,653 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:48,663 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:36:49,294 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:49,294 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:49,296 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:49,312 [191] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:36:49,743 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:49,743 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:49,744 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:49,751 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:36:50,157 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:50,157 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:50,158 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:36:50,177 [143] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:40:55,568 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:40:55,568 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:40:55,568 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:40:55,580 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:40:59,790 [.NET ThreadPool Worker] ERROR Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware [(null)] - An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_AI_Index+<ExecuteAsync>d__7..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_AI_Index#50.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|6_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware.Invoke(HttpContext context)
2025-02-25 14:41:14,228 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (46ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 14:41:14,308 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 14:41:14,536 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:14,549 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:14,551 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:41,788 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:41,788 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:41,788 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:56,085 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:56,085 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:41:56,088 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:42:11,218 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:42:11,218 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:42:11,218 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:42:16,898 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:42:16,898 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:42:16,899 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:43:20,143 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (6ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:43:20,144 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:43:20,155 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:44:49,975 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:44:49,975 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:44:49,979 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:44:53,011 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:44:53,011 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:44:53,011 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:00,146 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:00,146 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:00,147 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:06,577 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:06,577 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:06,578 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:21,052 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (45ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 14:45:21,127 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 14:45:21,377 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:21,389 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:45:21,390 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:51:18,220 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (38ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 14:51:18,287 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 14:51:18,521 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:51:18,522 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:51:18,523 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:55:35,850 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (39ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 14:55:35,850 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (39ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-02-25 14:55:35,918 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 14:55:35,918 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-02-25 14:55:36,157 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:55:36,157 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:55:36,158 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:56:51,795 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:56:51,795 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:56:51,796 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:03,381 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:03,381 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:03,382 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:08,758 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:08,758 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:08,758 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:58,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:58,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:58,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:59,734 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:59,734 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:57:59,736 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:11,623 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:11,623 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:11,626 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:18,682 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:18,682 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:18,684 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:39,737 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:39,737 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:39,741 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:53,239 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:53,240 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:53,240 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:54,576 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:54,576 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:58:54,578 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:59:05,428 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:59:05,432 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 14:59:05,432 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:00:25,101 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:00:25,101 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:00:25,101 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:25,135 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:25,135 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:25,135 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:32,795 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:32,795 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:32,798 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:43,530 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:43,532 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:43,533 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:51,820 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:51,820 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:01:51,821 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:02:02,815 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:02:02,815 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:02:02,818 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:02:54,156 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:02:54,156 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:02:54,160 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:03:14,772 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:03:14,772 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:03:14,776 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:03:37,675 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:03:37,675 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:03:37,675 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:22,851 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:22,851 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:22,851 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:30,619 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (4ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:30,621 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (5ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:30,622 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:36,908 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:04:36,908 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:06,008 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:06,008 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:06,009 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:23,860 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:23,860 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:23,861 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:27,613 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:27,613 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:27,614 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:28,390 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:28,390 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:28,392 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:47,813 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:47,813 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:05:47,813 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:06:37,038 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:06:37,038 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:06:37,039 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:04,018 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:04,018 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:04,020 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:09,497 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:09,497 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:09,499 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:16,417 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:16,417 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:16,419 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:23,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:23,050 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:07:23,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:08:06,595 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:08:06,595 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:08:06,596 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:08:46,169 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:08:46,169 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:08:46,170 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:47,655 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:47,655 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:47,657 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:56,148 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:56,148 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:56,148 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:57,376 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:57,376 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:09:57,377 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:10:00,876 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:10:00,877 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:10:00,877 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:12:30,562 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:12:30,562 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:12:30,562 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:12:40,008 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:12:40,008 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:12:40,008 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:30,761 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:30,761 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:30,763 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:37,849 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:37,849 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:37,852 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:42,225 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:42,225 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:42,230 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:42,693 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:42,693 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:15:42,694 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:10,728 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:10,728 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:10,742 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:11,959 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:11,959 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:11,963 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:34,403 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:34,403 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:34,403 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:35,504 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:35,504 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:35,505 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:56,963 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:56,963 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:16:56,965 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:17:47,043 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:17:47,058 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:17:47,070 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:04,028 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:04,028 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:04,029 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:04,587 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:04,587 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:04,589 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:09,049 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:09,049 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:09,051 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:09,731 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:09,742 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:09,754 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:11,955 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:11,955 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:11,955 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:22,590 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:22,590 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:22,591 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:27,652 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:27,652 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:27,654 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:36,509 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:36,510 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:36,510 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:37,419 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:37,422 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:18:37,419 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:27:16,801 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:27:16,804 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:54:14,234 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:54:14,242 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:54:14,254 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:54:14,600 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:54:14,600 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:58:15,191 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:58:15,429 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:58:15,436 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:58:42,121 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:58:42,343 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-02-25 15:58:42,343 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
