<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OpenAuth.App</name>
    </assembly>
    <members>
        <member name="T:OpenAuth.App.AppManager">
            <summary>
            应用管理
            </summary>
        </member>
        <member name="T:OpenAuth.App.Request.IdRequest`1">
            <summary>
            请求参数中只有Id
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:OpenAuth.App.Request.IdRequest`1.Id">
            <summary>
            操作Id
            </summary>
        </member>
        <member name="P:OpenAuth.App.Request.PageBean.currentPage">
            <summary>
            页码
            </summary>
            <example>1</example>
        </member>
        <member name="P:OpenAuth.App.Request.PageBean.pageSize">
            <summary>
            每页条数
            </summary>
            <example>10</example>
        </member>
        <member name="P:OpenAuth.App.Request.PageReq.page">
            <summary>
            页码
            </summary>
            <example>1</example>
        </member>
        <member name="P:OpenAuth.App.Request.PageReq.limit">
            <summary>
            每页条数
            </summary>
            <example>10</example>
        </member>
        <member name="P:OpenAuth.App.Request.PageReq.currentPage">
            <summary>
            每页条数
            </summary>
            <example>10</example>
        </member>
        <member name="T:OpenAuth.App.AuthContextFactory">
            <summary>
             加载用户所有可访问的资源/机构/模块
            <para>wzw 2020-07-19 10:53:30</para>
            </summary>
        </member>
        <member name="T:OpenAuth.App.AuthStrategyContext">
            <summary>
             授权策略上下文，一个典型的策略模式
            </summary>
        </member>
        <member name="T:OpenAuth.App.NormalAuthStrategy">
            <summary>
            普通用户授权策略
            </summary>
        </member>
        <member name="T:OpenAuth.App.SystemAuthStrategy">
            <summary>
            领域服务
            <para>超级管理员权限</para>
            <para>超级管理员使用guid.empty为ID，可以根据需要修改</para>
            </summary>
        </member>
        <member name="M:OpenAuth.App.AutofacExt.InitDependency(Autofac.ContainerBuilder)">
            <summary>
            注入所有继承了IDependency接口
            </summary>
            <param name="builder"></param>
        </member>
        <member name="F:OpenAuth.App.BaseApp`2.Repository">
            <summary>
            用于普通的数据库操作
            </summary>
        </member>
        <member name="F:OpenAuth.App.BaseApp`2.UnitWork">
            <summary>
            用于事务操作
            <para>使用详见：http://doc.openauth.net.cn/core/unitwork.html</para>
            </summary>
        </member>
        <member name="M:OpenAuth.App.BaseApp`2.GetDataPrivilege(System.String)">
            <summary>
             获取当前登录用户的数据访问权限
            </summary>
            <param name="parametername">linq表达式参数的名称，如u=>u.name中的"u"</param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.BaseApp`2.CaculateCascade``1(``0)">
            <summary>
            计算实体更新的层级信息
            </summary>
            <typeparam name="U">U必须是一个继承TreeEntity的结构</typeparam>
            <param name="entity"></param>
        </member>
        <member name="T:OpenAuth.App.BaseIntAutoGenApp`2">
            <summary>
            数据库Id为numberic且为数据库自动生成的业务类型
            <para>该场景通常为SqlServer的自动增长类型和Oracle自带的Sequence</para>
            业务层基类，UnitWork用于事务操作，Repository用于普通的数据库操作
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TDbContext"></typeparam>
        </member>
        <member name="M:OpenAuth.App.BaseIntAutoGenApp`2.Delete(System.Int32[])">
            <summary>
            按id批量删除
            </summary>
            <param name="ids"></param>
        </member>
        <member name="T:OpenAuth.App.BaseLongApp`2">
            <summary>
            ⭐⭐数据库Id为numberic类型的数据表相关业务使用该基类⭐⭐
            业务层基类，UnitWork用于事务操作，Repository用于普通的数据库操作
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TDbContext"></typeparam>
        </member>
        <member name="M:OpenAuth.App.BaseLongApp`2.Delete(System.Decimal[])">
            <summary>
            按id批量删除
            </summary>
            <param name="ids"></param>
        </member>
        <member name="T:OpenAuth.App.BaseStringApp`2">
            <summary>
            业务层基类，UnitWork用于事务操作，Repository用于普通的数据库操作
            <para>如用户管理：Class UserManagerApp:BaseApp&lt;User&gt;</para>
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TDbContext"></typeparam>
        </member>
        <member name="M:OpenAuth.App.BaseStringApp`2.Delete(System.String[])">
            <summary>
            按id批量删除
            </summary>
            <param name="ids"></param>
        </member>
        <member name="T:OpenAuth.App.BaseTreeApp`2">
            <summary>
            树状结构处理
            </summary>
            <typeparam name="T"></typeparam>
             /// <typeparam name="TDbContext"></typeparam>
        </member>
        <member name="M:OpenAuth.App.BaseTreeApp`2.UpdateTreeObj``1(``0)">
            <summary>
            更新树状结构实体
            </summary>
            <param name="obj"></param>
            <typeparam name="U"></typeparam>
        </member>
        <member name="T:OpenAuth.App.SqlSugarBaseApp`1">
            <summary>
            SqlSugar基础类
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:OpenAuth.App.SqlSugarBaseApp`1.GetDataPrivilege(System.String)">
            <summary>
             获取当前登录用户的数据访问权限
            </summary>
            <param name="parametername">linq表达式参数的名称，如u=>u.name中的"u"</param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.SqlSugarBaseApp`1.CaculateCascade``1(``0)">
            <summary>
            计算实体更新的层级信息
            </summary>
            <typeparam name="U">U必须是一个继承TreeEntity的结构</typeparam>
            <param name="entity"></param>
        </member>
        <member name="T:OpenAuth.App.Response.TableData">
            <summary>
            table的返回数据
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableData.code">
            <summary>
            状态码
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableData.msg">
            <summary>
            操作消息
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableData.count">
            <summary>
            总记录条数
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableData.data">
            <summary>
            数据内容
            </summary>
        </member>
        <member name="T:OpenAuth.App.Response.TableResp`1">
            <summary>
            返回确定类型的表格数据，可以为swagger提供精准的注释
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableResp`1.code">
            <summary>
            状态码
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableResp`1.msg">
            <summary>
            操作消息
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableResp`1.count">
            <summary>
            总记录条数
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableResp`1.data">
            <summary>
            数据内容
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.TableResp`1.columnHeaders">
            <summary>
             返回的列表头信息
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.UserView.real_name">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.UserView.password">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="P:OpenAuth.App.Response.UserView.department_id">
            <summary>
            性别
            </summary>
        </member>
        <member name="M:OpenAuth.App.DbExtension.GetDbEntityNames">
            <summary>
            获取数据库DbContext中所有的实体名称。
            <para>注意！并不能获取数据库中的所有表</para>
            </summary>
        </member>
        <member name="M:OpenAuth.App.DbExtension.GetDbTableStructure(System.String)">
            <summary>
            获取数据库表结构信息
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.DbExtension.GetOracleStructure(System.String)">
            <summary>
            获取Oracle表结构
            </summary>
        </member>
        <member name="M:OpenAuth.App.DbExtension.GetMySqlStructure(System.String)">
            <summary>
            获取Mysql表结构信息
            </summary>
        </member>
        <member name="M:OpenAuth.App.DbExtension.GetPostgreStructure(System.String)">
            <summary>
            获取Mysql表结构信息
            </summary>
        </member>
        <member name="M:OpenAuth.App.DbExtension.GetSqlServerStructure(System.String)">
            <summary>
            获取SqlServer表结构信息
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="T:OpenAuth.App.FileApp">
            <summary>
            文件管理
            </summary>
        </member>
        <member name="M:OpenAuth.App.FileApp.UploadChunk(OpenAuth.App.Request.UploadChunkRequest)">
            <summary>
            文件上传
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.FileApp.CreateDir(OpenAuth.App.Request.DirRequest)">
            <summary>
            新建文件夹/目录
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.FileApp.CheckFolder(OpenAuth.App.Request.DirRequest)">
            <summary>
            检查文件夹是否存在，不存在进行创建
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.Interface.IAuth.CheckLogin(System.String,System.String)">
            <summary>
            检验token是否有效
            </summary>
            <param name="token">token值</param>
            <param name="otherInfo"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.Interface.IAuth.Login(System.String,System.String,System.String)">
            <summary>
            登录接口
            </summary>
            <param name="appKey">登录的应用appkey</param>
            <param name="username">用户名</param>
            <param name="pwd">密码</param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.Interface.IAuth.Logout">
            <summary>
            退出登录
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.Interface.IAuth.LoginCus(System.String,System.String,System.String)">
            <summary>
            客户登录接口
            </summary>
            <param name="appKey">登录的应用appkey</param>
            <param name="username">用户名</param>
            <param name="pwd">密码</param>
            <returns></returns>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.Request.TruckingInput.currentPage">
            <summary>
            每页条数
            </summary>
            <example>10</example>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrderOverViewTUSApp.EditTUSReport(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderEmailDto)">
            <summary>
            OrderOverVIew TUS清关文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.WarehouseAgent">
            <summary>
            仓库代理
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.WarehouseLocation">
            <summary>
            所在仓库
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.InventoryqQty">
            <summary>
            库存数量
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.Packing">
            <summary>
            包装
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.IndexNo">
            <summary>
            索引号
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.BoxNo">
            <summary>
            集装箱箱号
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.SKUNO">
            <summary>
            SKUNO
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.GoodsName">
            <summary>
            商品名称
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.Qty1">
            <summary>
            数量1
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.Power">
            <summary>
            瓦数
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.GoodsClass">
            <summary>
            商品分类
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.BillNo">
            <summary>
            提单号
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.TotalPower">
            <summary>
            总瓦数
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.SingleTonQty">
            <summary>
            单托数量
            </summary>
        </member>
        <member name="P:OpenAuth.App.OrderManagement.OrderOverView.Dto.InventoryDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOrderListByExcel(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            导出Excel数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOrderDetail(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            根据订单号获取订单详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOrderInfo(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            根据订单号获取订单详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.AddOutBoundOrder(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            新增出库订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.AddInBoundOrder(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            新增入库订单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOutNoticeOrderNo(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            根据订单号获取出库通知单号
            </summary>
            <param name="Input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetGoodsNoByOutNoticeNo(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            根据出库通知单号获取商品明细
            </summary>
            <param name="DocNo"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetInventoryData(OpenAuth.App.OrderManagement.OrderOverView.Input.InventoryInput)">
            <summary>
            获取已入库的库存数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOutOrderNo(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            根据订单号获取出库单号
            </summary>
            <param name="Input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetGoodsNoByOutNo(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            根据出库单号获取商品明细
            </summary>
            <param name="DocNo"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOutboundData(OpenAuth.App.OrderManagement.OrderOverView.Input.InventoryInput)">
            <summary>
            获取需要还未审核确认商品编号信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetSearchData(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            获取查询列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.CreateOrEdidOutNotice(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            新增或者修改更新出库通知表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.CreateOrEdidOutBound(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            新增或跟更新出库表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetWarehouseData">
            <summary>
            根据客户代号获取仓库名称
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.DeleteOutboundNotice(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            出库通知单删除，注意检查是否又对应的库存信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.DeleteOutbound(OpenAuth.App.OrderManagement.OrderOverView.Input.OutNoticeInput)">
            <summary>
            出库单删除，注意检查是否又对应的库存信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetInvNoticeOrderNo(OpenAuth.App.OrderManagement.OrderOverView.Input.InvNoticeInput)">
            <summary>
            根据订单号获取入库通知单号
            </summary>
            <param name="Input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetInvOrderNo(OpenAuth.App.OrderManagement.OrderOverView.Input.InvNoticeInput)">
            <summary>
            根据订单号获取入库通知单号
            </summary>
            <param name="Input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.CreateOrEdidInvBound(OpenAuth.App.OrderManagement.OrderOverView.Input.InvNoticeInput)">
            <summary>
            新增或跟更新出库表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetInvbountNoticeDetail(OpenAuth.App.OrderManagement.OrderOverView.Input.InvNoticeInput)">
            <summary>
            根据订单号获取对应入库通知单号的对应库存信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.DeleteInvbound(OpenAuth.App.OrderManagement.OrderOverView.Input.InvNoticeInput)">
            <summary>
            入库单删除，注意检查是否又对应的库存信息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.CreateInboundEmail(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderEmailDto)">
            <summary>
            云程-入库通知单邮件推送
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.CreateOutboundEmail(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderEmailDto)">
            <summary>
            云程-出库通知单邮件推送
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.ConvertExcelToPdf(System.Byte[])">
            <summary>
            将 Excel 文件转换为 PDF
            </summary>
            <param name="excelBytes"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.AutoExcelByReport(System.Data.DataTable,System.Data.DataTable,System.String,System.String)">
            <summary>
            根据模版获取到Excel
            </summary>
            <param name="DT"></param>
            <param name="detailDT"></param>
            <param name="type"></param>
            <param name="reportName"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.CteateHtmlEmail(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderEmailDto,System.String)">
            <summary>
            创建邮件的HTML
            </summary>
            <param name="detailDT"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetDataByExcel(OpenAuth.App.OrderManagement.OrderOverView.Dto.InvNoticeExcelInput)">
            <summary>
            获取导入的Excel的数据内容
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetCellString(NPOI.SS.UserModel.IRow,System.Int32)">
            <summary>
            辅助方法：获取单元格数值（转 string）
            </summary>
            <param name="row"></param>
            <param name="col"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetCellDouble(NPOI.SS.UserModel.IRow,System.Int32)">
            <summary>
            辅助方法：获取单元格数值（转 double）
            </summary>
            <param name="row"></param>
            <param name="col"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.ImportInventory(OpenAuth.App.OrderManagement.OrderOverView.Input.ImportInvenroryInput)">
            <summary>
            保存导入的库存表，以及对应的入库通知单号
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.GetOrderEditDetail(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            OrderOverView获取可修改的数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.OrdersOverViewApp.EditOrderSubDetail(OpenAuth.App.OrderManagement.OrderOverView.Input.OrderInput)">
            <summary>
            OrderOverView获取可修改的数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.TruckingApp.CreateOrEditZCD(OpenAuth.App.OrderManagement.Request.TruckingInput)">
            <summary>
            DLVdetail保存
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.TruckingApp.GetTruckingByOrder(OpenAuth.App.OrderManagement.Request.TruckingInput)">
            <summary>
            DLV模态窗新增明细查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.OrderManagement.TruckingApp.GetOrderTypeDropdown(OpenAuth.App.OrderManagement.Request.TruckingInput)">
            <summary>
            获取orderOverView的订单状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:OpenAuth.App.SSO.LocalAuth">
            <summary>
            使用本地登录。这个注入IAuth时，只需要OpenAuth.Mvc一个项目即可，无需webapi的支持
            </summary>
        </member>
        <member name="M:OpenAuth.App.SSO.LocalAuth.GetToken">
            <summary>
            如果是Identity，则返回信息为用户账号
            </summary>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.SSO.LocalAuth.GetCurrentUser">
            <summary>
            获取当前登录的用户信息
            <para>通过URL中的Token参数或Cookie中的Token</para>
            </summary>
            <param name="account">The account.</param>
            <returns>LoginUserVM.</returns>
        </member>
        <member name="M:OpenAuth.App.SSO.LocalAuth.GetUserName(System.String)">
            <summary>
            获取当前登录的用户名
            <para>通过URL中的Token参数或Cookie中的Token</para>
            </summary>
            <param name="otherInfo">The account.</param>
            <returns>System.String.</returns>
        </member>
        <member name="M:OpenAuth.App.SSO.LocalAuth.Login(System.String,System.String,System.String)">
            <summary>
            登录接口
            </summary>
            <param name="appKey">应用程序key.</param>
            <param name="username">用户名</param>
            <param name="pwd">密码</param>
            <returns>System.String.</returns>
        </member>
        <member name="M:OpenAuth.App.SSO.LocalAuth.Logout">
            <summary>
            注销
            </summary>
        </member>
        <member name="M:OpenAuth.App.SSO.LocalAuth.LoginCus(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="appKey"></param>
            <param name="username"></param>
            <param name="pwd"></param>
            <returns></returns>
        </member>
        <member name="P:OpenAuth.App.SSO.PassportLoginRequest.Account">
            <example>System</example>
        </member>
        <member name="P:OpenAuth.App.SSO.PassportLoginRequest.Password">
            <example>123456</example>
        </member>
        <member name="P:OpenAuth.App.SSO.PassportLoginRequest.AppKey">
            <summary>
            应用的AppSecrect，目前没判定可以随便填一个。如果需要判定请根据注释调整LoginParse.Do方法
            </summary>
            <example>openauth</example>
        </member>
        <member name="P:OpenAuth.App.SSO.UserAuthSession.Account">
            <summary>
            用户账号
            </summary>
        </member>
        <member name="P:OpenAuth.App.SSO.UserAuthSession.Name">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:OpenAuth.App.SSO.UserAuthSession.CsCode">
            <summary>
            客户代号
            </summary>
        </member>
        <member name="P:OpenAuth.App.SSO.UserAuthSession.RoleView">
            <summary>
            用户查看权限
            </summary>
        </member>
        <member name="T:OpenAuth.App.Test.TestAccessObjs">
            <summary>
            测试分配权限
            </summary>
        </member>
        <member name="M:OpenAuth.App.Test.TestBase.GetService">
            <summary>
            测试框架默认只注入了缓存Cache，配置Option；
            如果在测试的过程中需要模拟登录用户，cookie等信息，需要重写该方法，可以参考TestFlow的写法
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.State">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.Operator">
            <summary>
            操作人
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.IssueType">
            <summary>
            问题类型
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.CurrentProcess">
            <summary>
            当前流程
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.FirstDate">
            <summary>
            第一接收时间
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.IsFiles">
            <summary>
            是否有破损证明文件
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.TrackingDate">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.Notes">
            <summary>
            问题描述
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Dto.IssueTrackDto.CreateUser">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.State">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.Operator">
            <summary>
            操作人
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.IssueType">
            <summary>
            问题类型
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.CurrentProcess">
            <summary>
            当前流程
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.FirstDate">
            <summary>
            第一接收时间
            </summary>
            
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.IsFiles">
            <summary>
            是否有破损证明文件
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.TrackingDate">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.Notes">
            <summary>
            问题描述
            </summary>
        </member>
        <member name="P:OpenAuth.App.WarehouseManagement.Input.IssueTrackInput.CreateUser">
            <summary>
            创建人
            </summary>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.InsertIssueTracking(OpenAuth.App.WarehouseManagement.Input.IssueTrackInput)">
            <summary>
            问题跟踪新增
            </summary>
            <param name="input"></param>    
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.EditIssueTracking(OpenAuth.App.WarehouseManagement.Input.IssueTrackInput)">
            <summary>
            问题跟踪修改
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.GetIssueTrackingDetail(OpenAuth.App.WarehouseManagement.Input.IssueTrackInput)">
            <summary>
            问题跟踪详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.GetSelectIssueDetailList(OpenAuth.App.WarehouseManagement.Input.IssueDetailInput)">
            <summary>
            获取可新增的库存明细
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.ReleaseIssueTracking(OpenAuth.App.WarehouseManagement.Input.IssueTrackInput)">
            <summary>
            问题跟踪状态更新：进行中
            </summary>
            <param name="input"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.GetIssueProcess(OpenAuth.App.WarehouseManagement.Input.IssueProcessInput)">
            <summary>
            问题进程管理跟踪列表查询
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.InsertIssueProcess(OpenAuth.App.WarehouseManagement.Input.IssueProcessInput)">
            <summary>
            问题进程管理跟踪新增
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.InsertProcessLog(OpenAuth.App.WarehouseManagement.Input.IssueProcessLogInput)">
            <summary>
            问题进程管理跟踪新增
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.GetIssueProcessDetail(OpenAuth.App.WarehouseManagement.Input.IssueProcessInput)">
            <summary>
            问题跟踪详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:OpenAuth.App.WarehouseApp.DeleteProcess(OpenAuth.App.WarehouseManagement.Input.IssueProcessInput)">
            <summary>
            进程管理删除
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
    </members>
</doc>
