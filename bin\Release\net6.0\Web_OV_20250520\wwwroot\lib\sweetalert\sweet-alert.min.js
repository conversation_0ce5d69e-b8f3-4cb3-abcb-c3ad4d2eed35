!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sweetalert2=e()}(this,function(){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function c(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t}function s(){return(s=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n,o=arguments[e];for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(t[n]=o[n])}return t}).apply(this,arguments)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,e){return(l=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function d(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}function i(t,e,n){return(i=d()?Reflect.construct:function(t,e,n){var o=[null];o.push.apply(o,e);o=new(Function.bind.apply(t,o));return n&&l(o,n.prototype),o}).apply(null,arguments)}function p(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function f(t,e,n){return(f="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=u(t)););return t}(t,e);if(t){e=Object.getOwnPropertyDescriptor(t,e);return e.get?e.get.call(n):e.value}})(t,e,n||t)}function m(e){return Object.keys(e).map(function(t){return e[t]})}function e(t,e){e='"'.concat(t,'" is deprecated and will be removed in the next major release. Please use "').concat(e,'" instead.'),-1===S.indexOf(e)&&(S.push(e),A(e))}function h(t){return t instanceof Element||q(t)}function t(t){return H(".".concat(t))}function g(){var t=N().filter(function(t){return yt(t)});return t.length?t[0]:null}function v(){return t(I.title)}function b(){return t(I.image)}function y(){return t(I.header)}function w(){return t(I.footer)}function C(){var t=P(D().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(function(t,e){return t=parseInt(t.getAttribute("tabindex")),(e=parseInt(e.getAttribute("tabindex")))<t?1:t<e?-1:0}),e=P(D().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter(function(t){return"-1"!==t.getAttribute("tabindex")});return function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(t.concat(e)).filter(function(t){return yt(t)})}function k(){return!Q()&&!document.body.classList.contains(I["no-backdrop"])}function x(){return D().hasAttribute("data-loading")}var n="SweetAlert2:",P=function(t){return Array.prototype.slice.call(t)},A=function(t){console.warn("".concat(n," ").concat(t))},B=function(t){console.error("".concat(n," ").concat(t))},S=[],E=function(t){return"function"==typeof t?t():t},O=function(t){return t&&"function"==typeof t.toPromise},T=function(t){return O(t)?t.toPromise():Promise.resolve(t)},L=function(t){return t&&Promise.resolve(t)===t},j=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),q=function(t){return"object"===r(t)&&t.jquery},V=function(t){var e,n={};for(e in t)n[t[e]]="swal2-"+t[e];return n},I=V(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","toast-column","show","hide","close","title","header","content","html-container","actions","confirm","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),M=V(["success","warning","info","question","error"]),R=function(){return document.body.querySelector(".".concat(I.container))},H=function(t){var e=R();return e?e.querySelector(t):null},D=function(){return t(I.popup)},N=function(){var t=D();return P(t.querySelectorAll(".".concat(I.icon)))},U=function(){return t(I.content)},_=function(){return t(I["progress-steps"])},F=function(){return t(I["validation-message"])},z=function(){return H(".".concat(I.actions," .").concat(I.confirm))},W=function(){return H(".".concat(I.actions," .").concat(I.cancel))},K=function(){return t(I.actions)},Y=function(){return t(I["timer-progress-bar"])},Z=function(){return t(I.close)},Q=function(){return document.body.classList.contains(I["toast-shown"])},J={previousBodyPadding:null},X=function(e,t){e.textContent="",t&&(t=(new DOMParser).parseFromString(t,"text/html"),P(t.querySelector("head").childNodes).forEach(function(t){e.appendChild(t)}),P(t.querySelector("body").childNodes).forEach(function(t){e.appendChild(t)}))},G=function(t,e){if(!e)return!1;for(var n=e.split(/\s+/),o=0;o<n.length;o++)if(!t.classList.contains(n[o]))return!1;return!0},tt=function(e,n){P(e.classList).forEach(function(t){-1===m(I).indexOf(t)&&-1===m(M).indexOf(t)&&-1===m(n.showClass).indexOf(t)&&e.classList.remove(t)})},et=function(t,e,n){if(tt(t,e),e.customClass&&e.customClass[n]){if("string"!=typeof e.customClass[n]&&!e.customClass[n].forEach)return A("Invalid type of customClass.".concat(n,'! Expected string or iterable object, got "').concat(r(e.customClass[n]),'"'));ht(t,e.customClass[n])}};function nt(t,e){if(!e)return null;switch(e){case"select":case"textarea":case"file":return ot(t,I[e]);case"checkbox":return t.querySelector(".".concat(I.checkbox," input"));case"radio":return t.querySelector(".".concat(I.radio," input:checked"))||t.querySelector(".".concat(I.radio," input:first-child"));case"range":return t.querySelector(".".concat(I.range," input"));default:return ot(t,I.input)}}function ot(t,e){for(var n=0;n<t.childNodes.length;n++)if(G(t.childNodes[n],e))return t.childNodes[n]}function it(t,e,n){n||0===parseInt(n)?t.style[e]="number"==typeof n?"".concat(n,"px"):n:t.style.removeProperty(e)}function rt(t,e,n){e?vt(t,n):bt(t)}function at(t){return!!(t.scrollHeight>t.clientHeight)}function ct(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=Y();yt(n)&&(e&&(n.style.transition="none",n.style.width="100%"),setTimeout(function(){n.style.transition="width ".concat(t/1e3,"s linear"),n.style.width="0%"},10))}function st(){return"undefined"==typeof window||"undefined"==typeof document}function ut(t){En.isVisible()&&pt!==t.target.value&&En.resetValidationMessage(),pt=t.target.value}function lt(t,e){t instanceof HTMLElement?e.appendChild(t):"object"===r(t)?dt(t,e):t&&X(e,t)}function dt(t,e){t.jquery?xt(e,t):X(e,t.toString())}var pt,ft=function(t){var e;t.focus(),"file"!==t.type&&(e=t.value,t.value="",t.value=e)},mt=function(t,e,n){t&&e&&(e="string"==typeof e?e.split(/\s+/).filter(Boolean):e).forEach(function(e){t.forEach?t.forEach(function(t){n?t.classList.add(e):t.classList.remove(e)}):n?t.classList.add(e):t.classList.remove(e)})},ht=function(t,e){mt(t,e,!0)},gt=function(t,e){mt(t,e,!1)},vt=function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"flex";t.style.opacity="",t.style.display=e},bt=function(t){t.style.opacity="",t.style.display="none"},yt=function(t){return!(!t||!(t.offsetWidth||t.offsetHeight||t.getClientRects().length))},wt=function(t){var e=window.getComputedStyle(t),t=parseFloat(e.getPropertyValue("animation-duration")||"0"),e=parseFloat(e.getPropertyValue("transition-duration")||"0");return 0<t||0<e},Ct='\n <div aria-labelledby="'.concat(I.title,'" aria-describedby="').concat(I.content,'" class="').concat(I.popup,'" tabindex="-1">\n   <div class="').concat(I.header,'">\n     <ul class="').concat(I["progress-steps"],'"></ul>\n     <div class="').concat(I.icon," ").concat(M.error,'"></div>\n     <div class="').concat(I.icon," ").concat(M.question,'"></div>\n     <div class="').concat(I.icon," ").concat(M.warning,'"></div>\n     <div class="').concat(I.icon," ").concat(M.info,'"></div>\n     <div class="').concat(I.icon," ").concat(M.success,'"></div>\n     <img class="').concat(I.image,'" />\n     <h2 class="').concat(I.title,'" id="').concat(I.title,'"></h2>\n     <button type="button" class="').concat(I.close,'"></button>\n   </div>\n   <div class="').concat(I.content,'">\n     <div id="').concat(I.content,'" class="').concat(I["html-container"],'"></div>\n     <input class="').concat(I.input,'" />\n     <input type="file" class="').concat(I.file,'" />\n     <div class="').concat(I.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(I.select,'"></select>\n     <div class="').concat(I.radio,'"></div>\n     <label for="').concat(I.checkbox,'" class="').concat(I.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(I.label,'"></span>\n     </label>\n     <textarea class="').concat(I.textarea,'"></textarea>\n     <div class="').concat(I["validation-message"],'" id="').concat(I["validation-message"],'"></div>\n   </div>\n   <div class="').concat(I.actions,'">\n     <button type="button" class="').concat(I.confirm,'">OK</button>\n     <button type="button" class="').concat(I.cancel,'">Cancel</button>\n   </div>\n   <div class="').concat(I.footer,'"></div>\n   <div class="').concat(I["timer-progress-bar-container"],'">\n     <div class="').concat(I["timer-progress-bar"],'"></div>\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),kt=function(t){var e,n,o,i,r,a=!!(r=R())&&(r.parentNode.removeChild(r),gt([document.documentElement,document.body],[I["no-backdrop"],I["toast-shown"],I["has-column"]]),!0);st()?B("SweetAlert2 requires document to initialize"):((e=document.createElement("div")).className=I.container,a&&ht(e,I["no-transition"]),X(e,Ct),(r="string"==typeof(i=t.target)?document.querySelector(i):i).appendChild(e),a=t,(i=D()).setAttribute("role",a.toast?"alert":"dialog"),i.setAttribute("aria-live",a.toast?"polite":"assertive"),a.toast||i.setAttribute("aria-modal","true"),"rtl"===window.getComputedStyle(r).direction&&ht(R(),I.rtl),e=U(),t=ot(e,I.input),a=ot(e,I.file),n=e.querySelector(".".concat(I.range," input")),o=e.querySelector(".".concat(I.range," output")),i=ot(e,I.select),r=e.querySelector(".".concat(I.checkbox," input")),e=ot(e,I.textarea),t.oninput=ut,a.onchange=ut,i.onchange=ut,r.onchange=ut,e.oninput=ut,n.oninput=function(t){ut(t),o.value=n.value},n.onchange=function(t){ut(t),n.nextSibling.value=n.value})},xt=function(t,e){if(t.textContent="",0 in e)for(var n=0;n in e;n++)t.appendChild(e[n].cloneNode(!0));else t.appendChild(e.cloneNode(!0))},Pt=function(){if(st())return!1;var t,e=document.createElement("div"),n={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&void 0!==e.style[t])return n[t];return!1}(),At=function(t,e){var n=K(),o=z(),i=W();e.showConfirmButton||e.showCancelButton||bt(n),et(n,e,"actions"),Bt(o,"confirm",e),Bt(i,"cancel",e),e.buttonsStyling?function(t,e,n){ht([t,e],I.styled),n.confirmButtonColor&&(t.style.backgroundColor=n.confirmButtonColor);n.cancelButtonColor&&(e.style.backgroundColor=n.cancelButtonColor);x()||(n=window.getComputedStyle(t).getPropertyValue("background-color"),t.style.borderLeftColor=n,t.style.borderRightColor=n)}(o,i,e):(gt([o,i],I.styled),o.style.backgroundColor=o.style.borderLeftColor=o.style.borderRightColor="",i.style.backgroundColor=i.style.borderLeftColor=i.style.borderRightColor=""),e.reverseButtons&&o.parentNode.insertBefore(i,o)};function Bt(t,e,n){var o;rt(t,n["show".concat((o=e).charAt(0).toUpperCase()+o.slice(1),"Button")],"inline-block"),X(t,n["".concat(e,"ButtonText")]),t.setAttribute("aria-label",n["".concat(e,"ButtonAriaLabel")]),t.className=I[e],et(t,n,"".concat(e,"Button")),ht(t,n["".concat(e,"ButtonClass")])}function St(t){if(!Mt[t.input])return B('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(t.input,'"'));var e=It(t.input),n=Mt[t.input](e,t);vt(n),setTimeout(function(){ft(n)})}function Et(t,e){var n=nt(U(),t);if(n)for(var o in Vt(n),e)"range"===t&&"placeholder"===o||n.setAttribute(o,e[o])}function Ot(t){var e=It(t.input);t.customClass&&ht(e,t.customClass.input)}function Tt(t,e){t.placeholder&&!e.inputPlaceholder||(t.placeholder=e.inputPlaceholder)}var Lt=function(t,e){var n,o,i=R();i&&(o=i,"string"==typeof(n=e.backdrop)?o.style.background=n:n||ht([document.documentElement,document.body],I["no-backdrop"]),!e.backdrop&&e.allowOutsideClick&&A('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),o=i,(n=e.position)in I?ht(o,I[n]):(A('The "position" parameter is not valid, defaulting to "center"'),ht(o,I.center)),n=i,!(o=e.grow)||"string"!=typeof o||(o="grow-".concat(o))in I&&ht(n,I[o]),et(i,e,"container"),(e=document.body.getAttribute("data-swal2-queue-step"))&&(i.setAttribute("data-queue-step",e),document.body.removeAttribute("data-swal2-queue-step")))},jt={promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap},qt=["input","file","range","select","radio","checkbox","textarea"],Vt=function(t){for(var e=0;e<t.attributes.length;e++){var n=t.attributes[e].name;-1===["type","value","style"].indexOf(n)&&t.removeAttribute(n)}},It=function(t){t=I[t]||I.input;return ot(U(),t)},Mt={};Mt.text=Mt.email=Mt.password=Mt.number=Mt.tel=Mt.url=function(t,e){return"string"==typeof e.inputValue||"number"==typeof e.inputValue?t.value=e.inputValue:L(e.inputValue)||A('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(r(e.inputValue),'"')),Tt(t,e),t.type=e.input,t},Mt.file=function(t,e){return Tt(t,e),t},Mt.range=function(t,e){var n=t.querySelector("input"),o=t.querySelector("output");return n.value=e.inputValue,n.type=e.input,o.value=e.inputValue,t},Mt.select=function(t,e){var n;return t.textContent="",e.inputPlaceholder&&(n=document.createElement("option"),X(n,e.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,t.appendChild(n)),t},Mt.radio=function(t){return t.textContent="",t},Mt.checkbox=function(t,e){var n=nt(U(),"checkbox");n.value=1,n.id=I.checkbox,n.checked=Boolean(e.inputValue);n=t.querySelector("span");return X(n,e.inputPlaceholder),t},Mt.textarea=function(e,t){var n,o;return e.value=t.inputValue,Tt(e,t),"MutationObserver"in window&&(n=parseInt(window.getComputedStyle(D()).width),o=parseInt(window.getComputedStyle(D()).paddingLeft)+parseInt(window.getComputedStyle(D()).paddingRight),new MutationObserver(function(){var t=e.offsetWidth+o;D().style.width=n<t?"".concat(t,"px"):null}).observe(e,{attributes:!0,attributeFilter:["style"]})),e};function Rt(){for(var t=N(),e=0;e<t.length;e++)bt(t[e])}function Ht(){for(var t=D(),e=window.getComputedStyle(t).getPropertyValue("background-color"),n=t.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),o=0;o<n.length;o++)n[o].style.backgroundColor=e}function Dt(t,e){t.textContent="",e.iconHtml?X(t,Kt(e.iconHtml)):"success"===e.icon?X(t,'\n      <div class="swal2-success-circular-line-left"></div>\n      <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n      <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n      <div class="swal2-success-circular-line-right"></div>\n    '):"error"===e.icon?X(t,'\n      <span class="swal2-x-mark">\n        <span class="swal2-x-mark-line-left"></span>\n        <span class="swal2-x-mark-line-right"></span>\n      </span>\n    '):X(t,Kt({question:"?",warning:"!",info:"i"}[e.icon]))}function Nt(){return R()&&R().getAttribute("data-queue-step")}function Ut(t,o){var i=_();if(!o.progressSteps||0===o.progressSteps.length)return bt(i);vt(i),i.textContent="";var r=parseInt(void 0===o.currentProgressStep?Nt():o.currentProgressStep);r>=o.progressSteps.length&&A("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),o.progressSteps.forEach(function(t,e){var n,t=(n=t,t=document.createElement("li"),ht(t,I["progress-step"]),X(t,n),t);i.appendChild(t),e===r&&ht(t,I["active-progress-step"]),e!==o.progressSteps.length-1&&(t=o,e=document.createElement("li"),ht(e,I["progress-step-line"]),t.progressStepsDistance&&(e.style.width=t.progressStepsDistance),i.appendChild(e))})}function _t(t,e){t.className="".concat(I.popup," ").concat(yt(t)?e.showClass.popup:""),e.toast?(ht([document.documentElement,document.body],I["toast-shown"]),ht(t,I.toast)):ht(t,I.modal),et(t,e,"popup"),"string"==typeof e.customClass&&ht(t,e.customClass),e.icon&&ht(t,I["icon-".concat(e.icon)])}function Ft(t,e){Qt(t,e),Lt(t,e),Zt(t,e),zt(t,e),At(t,e),Wt(t,e),"function"==typeof e.onRender&&e.onRender(D())}var zt=function(t,e){var o,i,r,n=U().querySelector("#".concat(I.content));e.html?(lt(e.html,n),vt(n,"block")):e.text?(n.textContent=e.text,vt(n,"block")):bt(n),t=t,o=e,i=U(),t=jt.innerParams.get(t),r=!t||o.input!==t.input,qt.forEach(function(t){var e=I[t],n=ot(i,e);Et(t,o.inputAttributes),n.className=e,r&&bt(n)}),o.input&&(r&&St(o),Ot(o)),et(U(),e,"content")},Wt=function(t,e){var n=w();rt(n,e.footer),e.footer&&lt(e.footer,n),et(n,e,"footer")},Kt=function(t){return'<div class="'.concat(I["icon-content"],'">').concat(t,"</div>")},Yt=[],Zt=function(t,e){var n=y();et(n,e,"header"),Ut(0,e),function(t,e){t=jt.innerParams.get(t);t&&e.icon===t.icon&&g()?et(g(),e,"icon"):(Rt(),e.icon&&(-1!==Object.keys(M).indexOf(e.icon)?(t=H(".".concat(I.icon,".").concat(M[e.icon])),vt(t),Dt(t,e),Ht(),et(t,e,"icon"),ht(t,e.showClass.icon)):B('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(e.icon,'"'))))}(t,e),function(t){var e=b();if(!t.imageUrl)return bt(e);vt(e,""),e.setAttribute("src",t.imageUrl),e.setAttribute("alt",t.imageAlt),it(e,"width",t.imageWidth),it(e,"height",t.imageHeight),e.className=I.image,et(e,t,"image")}(e),n=e,t=v(),rt(t,n.title||n.titleText),n.title&&lt(n.title,t),n.titleText&&(t.innerText=n.titleText),et(t,n,"title"),n=e,e=Z(),X(e,n.closeButtonHtml),et(e,n,"closeButton"),rt(e,n.showCloseButton),e.setAttribute("aria-label",n.closeButtonAriaLabel)},Qt=function(t,e){var n=D();it(n,"width",e.width),it(n,"padding",e.padding),e.background&&(n.style.background=e.background),_t(n,e)},$t=function(){return z()&&z().click()};function Jt(){return new Promise(function(t){var e=window.scrollX,n=window.scrollY;ie.restoreFocusTimeout=setTimeout(function(){re(),t()},oe),void 0!==e&&void 0!==n&&window.scrollTo(e,n)})}function Xt(){if(ie.timeout)return function(){var t=Y(),e=parseInt(window.getComputedStyle(t).width);t.style.removeProperty("transition"),t.style.width="100%";var n=parseInt(window.getComputedStyle(t).width),n=parseInt(e/n*100);t.style.removeProperty("transition"),t.style.width="".concat(n,"%")}(),ie.timeout.stop()}function Gt(){if(ie.timeout){var t=ie.timeout.start();return ct(t),t}}function te(t){return Object.prototype.hasOwnProperty.call(ae,t)}function ee(t){return se[t]}var ne=function(){(t=D())||En.fire();var t=D(),e=K(),n=z();vt(e),vt(n,"inline-block"),ht([t,e],I.loading),n.disabled=!0,t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()},oe=100,ie={},re=function(){ie.previousActiveElement&&ie.previousActiveElement.focus?(ie.previousActiveElement.focus(),ie.previousActiveElement=null):document.body&&document.body.focus()},ae={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconHtml:void 0,toast:!1,animation:!0,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:void 0,target:"body",backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showCancelButton:!1,preConfirm:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusCancel:!1,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",showLoaderOnConfirm:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,onBeforeOpen:void 0,onOpen:void 0,onRender:void 0,onClose:void 0,onAfterClose:void 0,onDestroy:void 0,scrollbarPadding:!0},ce=["allowEscapeKey","allowOutsideClick","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","footer","hideClass","html","icon","imageAlt","imageHeight","imageUrl","imageWidth","onAfterClose","onClose","onDestroy","progressSteps","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","text","title","titleText"],se={animation:'showClass" and "hideClass'},ue=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusCancel","heightAuto","keydownListenerCapture"],le=function(t){te(t)||A('Unknown parameter "'.concat(t,'"'))},de=function(t){-1!==ue.indexOf(t)&&A('The parameter "'.concat(t,'" is incompatible with toasts'))},pe=function(t){ee(t)&&e(t,ee(t))},fe=Object.freeze({isValidParameter:te,isUpdatableParameter:function(t){return-1!==ce.indexOf(t)},isDeprecatedParameter:ee,argsToParams:function(n){var o={};return"object"!==r(n[0])||h(n[0])?["title","html","icon"].forEach(function(t,e){e=n[e];"string"==typeof e||h(e)?o[t]=e:void 0!==e&&B("Unexpected type of ".concat(t,'! Expected "string" or "Element", got ').concat(r(e)))}):s(o,n[0]),o},isVisible:function(){return yt(D())},clickConfirm:$t,clickCancel:function(){return W()&&W().click()},getContainer:R,getPopup:D,getTitle:v,getContent:U,getHtmlContainer:function(){return t(I["html-container"])},getImage:b,getIcon:g,getIcons:N,getCloseButton:Z,getActions:K,getConfirmButton:z,getCancelButton:W,getHeader:y,getFooter:w,getTimerProgressBar:Y,getFocusableElements:C,getValidationMessage:F,isLoading:x,fire:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return i(this,e)},mixin:function(r){return function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&l(t,e)}(i,t);var n,o,e=(n=i,o=d(),function(){var t,e=u(n);return p(this,o?(t=u(this).constructor,Reflect.construct(e,arguments,t)):e.apply(this,arguments))});function i(){return a(this,i),e.apply(this,arguments)}return c(i,[{key:"_main",value:function(t){return f(u(i.prototype),"_main",this).call(this,s({},r,t))}}]),i}(this)},queue:function(t){var r=this;Yt=t;function a(t,e){Yt=[],t(e)}var c=[];return new Promise(function(i){!function e(n,o){n<Yt.length?(document.body.setAttribute("data-swal2-queue-step",n),r.fire(Yt[n]).then(function(t){void 0!==t.value?(c.push(t.value),e(n+1,o)):a(i,{dismiss:t.dismiss})})):a(i,{value:c})}(0)})},getQueueStep:Nt,insertQueueStep:function(t,e){return e&&e<Yt.length?Yt.splice(e,0,t):Yt.push(t)},deleteQueueStep:function(t){void 0!==Yt[t]&&Yt.splice(t,1)},showLoading:ne,enableLoading:ne,getTimerLeft:function(){return ie.timeout&&ie.timeout.getTimerLeft()},stopTimer:Xt,resumeTimer:Gt,toggleTimer:function(){var t=ie.timeout;return t&&(t.running?Xt:Gt)()},increaseTimer:function(t){if(ie.timeout){t=ie.timeout.increase(t);return ct(t,!0),t}},isTimerRunning:function(){return ie.timeout&&ie.timeout.isRunning()}});function me(){var t,e=jt.innerParams.get(this);e&&(t=jt.domCache.get(this),e.showConfirmButton||(bt(t.confirmButton),e.showCancelButton||bt(t.actions)),gt([t.popup,t.actions],I.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.cancelButton.disabled=!1)}function he(){null!==J.previousBodyPadding&&(document.body.style.paddingRight="".concat(J.previousBodyPadding,"px"),J.previousBodyPadding=null)}function ge(){navigator.userAgent.match(/(CriOS|FxiOS|EdgiOS|YaBrowser|UCBrowser)/i)||D().scrollHeight>window.innerHeight-44&&(R().style.paddingBottom="".concat(44,"px"))}function ve(){var e,t=R();t.ontouchstart=function(t){e=xe(t.target)},t.ontouchmove=function(t){e&&(t.preventDefault(),t.stopPropagation())}}function be(){var t;G(document.body,I.iosfix)&&(t=parseInt(document.body.style.top,10),gt(document.body,I.iosfix),document.body.style.top="",document.body.scrollTop=-1*t)}function ye(){"undefined"!=typeof window&&Pe()&&window.removeEventListener("resize",Ae)}function we(){P(document.body.children).forEach(function(t){t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})}var Ce=function(){null===J.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(J.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(J.previousBodyPadding+function(){var t=document.createElement("div");t.className=I["scrollbar-measure"],document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e}(),"px"))},ke=function(){var t;(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints)&&!G(document.body,I.iosfix)&&(t=document.body.scrollTop,document.body.style.top="".concat(-1*t,"px"),ht(document.body,I.iosfix),ve(),ge())},xe=function(t){var e=R();return t===e||!(at(e)||"INPUT"===t.tagName||at(U())&&U().contains(t))},Pe=function(){return!!window.MSInputMethodContext&&!!document.documentMode},Ae=function(){var t=R(),e=D();t.style.removeProperty("align-items"),e.offsetTop<0&&(t.style.alignItems="flex-start")},Be=function(){"undefined"!=typeof window&&Pe()&&(Ae(),window.addEventListener("resize",Ae))},Se={swalPromiseResolve:new WeakMap};function Ee(t,e,n,o){n?Le(t,o):(Jt().then(function(){return Le(t,o)}),ie.keydownTarget.removeEventListener("keydown",ie.keydownHandler,{capture:ie.keydownListenerCapture}),ie.keydownHandlerAdded=!1),e.parentNode&&!document.body.getAttribute("data-swal2-queue-step")&&e.parentNode.removeChild(e),k()&&(he(),be(),ye(),we()),gt([document.documentElement,document.body],[I.shown,I["height-auto"],I["no-backdrop"],I["toast-shown"],I["toast-column"]])}function Oe(t){var e,n,o,i=D();!i||(e=jt.innerParams.get(this))&&!G(i,e.hideClass.popup)&&(n=Se.swalPromiseResolve.get(this),gt(i,e.showClass.popup),ht(i,e.hideClass.popup),o=R(),gt(o,e.showClass.backdrop),ht(o,e.hideClass.backdrop),Te(this,i,e),void 0!==t?(t.isDismissed=void 0!==t.dismiss,t.isConfirmed=void 0===t.dismiss):t={isDismissed:!0,isConfirmed:!1},n(t||{}))}function Te(t,e,n){var o=R(),i=Pt&&wt(e),r=n.onClose,n=n.onAfterClose;null!==r&&"function"==typeof r&&r(e),i?je(t,e,o,n):Ee(t,o,Q(),n)}function Le(t,e){setTimeout(function(){"function"==typeof e&&e(),t._destroy()})}var je=function(t,e,n,o){ie.swalCloseEventFinishedCallback=Ee.bind(null,t,n,Q(),o),e.addEventListener(Pt,function(t){t.target===e&&(ie.swalCloseEventFinishedCallback(),delete ie.swalCloseEventFinishedCallback)})};function qe(t,e,n){var o=jt.domCache.get(t);e.forEach(function(t){o[t].disabled=n})}function Ve(t,e){if(!t)return!1;if("radio"===t.type)for(var n=t.parentNode.parentNode.querySelectorAll("input"),o=0;o<n.length;o++)n[o].disabled=e;else t.disabled=e}var Ie=function(){function n(t,e){a(this,n),this.callback=t,this.remaining=e,this.running=!1,this.start()}return c(n,[{key:"start",value:function(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}},{key:"stop",value:function(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date-this.started),this.remaining}},{key:"increase",value:function(t){var e=this.running;return e&&this.stop(),this.remaining+=t,e&&this.start(),this.remaining}},{key:"getTimerLeft",value:function(){return this.running&&(this.stop(),this.start()),this.remaining}},{key:"isRunning",value:function(){return this.running}}]),n}(),Me={email:function(t,e){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid email address")},url:function(t,e){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid URL")}};function Re(t){var e,n;(e=t).inputValidator||Object.keys(Me).forEach(function(t){e.input===t&&(e.inputValidator=Me[t])}),t.showLoaderOnConfirm&&!t.preConfirm&&A("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),t.animation=E(t.animation),(n=t).target&&("string"!=typeof n.target||document.querySelector(n.target))&&("string"==typeof n.target||n.target.appendChild)||(A('Target parameter is not valid, defaulting to "body"'),n.target="body"),"string"==typeof t.title&&(t.title=t.title.split("\n").join("<br />")),kt(t)}var He=function(t){var e=R(),n=D();"function"==typeof t.onBeforeOpen&&t.onBeforeOpen(n);var o=window.getComputedStyle(document.body).overflowY;_e(e,n,t),Ne(e,n),k()&&(Ue(e,t.scrollbarPadding,o),P(document.body.children).forEach(function(t){t===R()||function(t,e){if("function"==typeof t.contains)return t.contains(e)}(t,R())||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")),t.setAttribute("aria-hidden","true"))})),Q()||ie.previousActiveElement||(ie.previousActiveElement=document.activeElement),"function"==typeof t.onOpen&&setTimeout(function(){return t.onOpen(n)}),gt(e,I["no-transition"])};function De(t){var e=D();t.target===e&&(t=R(),e.removeEventListener(Pt,De),t.style.overflowY="auto")}function Ne(t,e){Pt&&wt(e)?(t.style.overflowY="hidden",e.addEventListener(Pt,De)):t.style.overflowY="auto"}function Ue(t,e,n){ke(),Be(),e&&"hidden"!==n&&Ce(),setTimeout(function(){t.scrollTop=0})}function _e(t,e,n){ht(t,n.showClass.backdrop),vt(e),ht(e,n.showClass.popup),ht([document.documentElement,document.body],I.shown),n.heightAuto&&n.backdrop&&!n.toast&&ht([document.documentElement,document.body],I["height-auto"])}function Fe(t){return t.checked?1:0}function ze(t){return t.checked?t.value:null}function We(t){return t.files.length?null!==t.getAttribute("multiple")?t.files:t.files[0]:null}function Ke(e,n){function o(t){return on[n.input](i,rn(t),n)}var i=U();O(n.inputOptions)||L(n.inputOptions)?(ne(),T(n.inputOptions).then(function(t){e.hideLoading(),o(t)})):"object"===r(n.inputOptions)?o(n.inputOptions):B("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(r(n.inputOptions)))}function Ye(e,n){var o=e.getInput();bt(o),T(n.inputValue).then(function(t){o.value="number"===n.input?parseFloat(t)||0:"".concat(t),vt(o),o.focus(),e.hideLoading()}).catch(function(t){B("Error in inputValue promise: ".concat(t)),o.value="",vt(o),o.focus(),e.hideLoading()})}function Ze(e,n){var o=nn(e,n);n.inputValidator?(e.disableInput(),Promise.resolve().then(function(){return T(n.inputValidator(o,n.validationMessage))}).then(function(t){e.enableButtons(),e.enableInput(),t?e.showValidationMessage(t):un(e,n,o)})):e.getInput().checkValidity()?un(e,n,o):(e.enableButtons(),e.showValidationMessage(n.validationMessage))}function Qe(t,e,n){var o=C(),i=0;if(i<o.length)return(e+=n)===o.length?e=0:-1===e&&(e=o.length-1),o[e].focus();D().focus()}function $e(t,e,n){(t=jt.innerParams.get(t)).stopKeydownPropagation&&e.stopPropagation(),"Enter"===e.key?(0<$("button.swal2-cancel:focus").length?hn(e,t,n):$t(),e.preventDefault()):"Tab"===e.key?fn(e,t):-1!==dn.indexOf(e.key)?mn():-1!==pn.indexOf(e.key)&&hn(e,t,n)}function Je(e,t,n){t.popup.onclick=function(){var t=jt.innerParams.get(e);t.showConfirmButton||t.showCancelButton||t.showCloseButton||t.input||n(j.close)}}function Xe(e){e.popup.onmousedown=function(){e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(vn=!0)}}}function Ge(e){e.container.onmousedown=function(){e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,t.target!==e.popup&&!e.popup.contains(t.target)||(vn=!0)}}}function tn(n,o,i){o.container.onclick=function(t){var e=jt.innerParams.get(n);vn?vn=!1:t.target===o.container&&E(e.allowOutsideClick)&&i(j.backdrop)}}var en=function(t,e){"select"===e.input||"radio"===e.input?Ke(t,e):-1!==["text","email","number","tel","textarea"].indexOf(e.input)&&(O(e.inputValue)||L(e.inputValue))&&Ye(t,e)},nn=function(t,e){var n=t.getInput();if(!n)return null;switch(e.input){case"checkbox":return Fe(n);case"radio":return ze(n);case"file":return We(n);default:return e.inputAutoTrim?n.value.trim():n.value}},on={select:function(t,e,i){function o(t,e,n){var o=document.createElement("option");o.value=n,X(o,e),i.inputValue.toString()===n.toString()&&(o.selected=!0),t.appendChild(o)}var r=ot(t,I.select);e.forEach(function(t){var e,n=t[0],t=t[1];Array.isArray(t)?((e=document.createElement("optgroup")).label=n,e.disabled=!1,r.appendChild(e),t.forEach(function(t){return o(e,t[1],t[0])})):o(r,t,n)}),r.focus()},radio:function(t,e,i){var r=ot(t,I.radio);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("input"),t=document.createElement("label");o.type="radio",o.name=I.radio,o.value=e,i.inputValue.toString()===e.toString()&&(o.checked=!0);e=document.createElement("span");X(e,n),e.className=I.label,t.appendChild(o),t.appendChild(e),r.appendChild(t)});e=r.querySelectorAll("input");e.length&&e[0].focus()}},rn=function n(o){var i=[];return"undefined"!=typeof Map&&o instanceof Map?o.forEach(function(t,e){"object"===r(t)&&(t=n(t)),i.push([e,t])}):Object.keys(o).forEach(function(t){var e=o[t];"object"===r(e)&&(e=n(e)),i.push([t,e])}),i},an=function(t,e){t.disableButtons(),e.input?Ze(t,e):un(t,e,!0)},cn=function(t,e){t.disableButtons(),e(j.cancel)},sn=function(t,e){t.closePopup({value:e})},un=function(e,t,n){t.showLoaderOnConfirm&&ne(),t.preConfirm?(e.resetValidationMessage(),Promise.resolve().then(function(){return T(t.preConfirm(n,t.validationMessage))}).then(function(t){yt(F())||!1===t?e.hideLoading():sn(e,void 0===t?n:t)})):sn(e,n)},ln=function(e,t,n,o){t.keydownTarget&&t.keydownHandlerAdded&&(t.keydownTarget.removeEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!1),n.toast||(t.keydownHandler=function(t){return $e(e,t,o)},t.keydownTarget=n.keydownListenerCapture?window:D(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)},dn=["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Left","Right","Up","Down"],pn=["Escape","Esc"],fn=function(t,e){for(var n=t.target,o=C(),i=-1,r=0;r<o.length;r++)if(n===o[r]){i=r;break}t.shiftKey?Qe(0,i,-1):Qe(0,i,1),t.stopPropagation(),t.preventDefault()},mn=function(){var t=z(),e=W();document.activeElement===t&&yt(e)?e.focus():document.activeElement===e&&yt(t)&&t.focus()},hn=function(t,e,n){E(e.allowEscapeKey)&&(t.preventDefault(),n(j.esc))},gn=function(t,e,n){jt.innerParams.get(t).toast?Je(t,e,n):(Xe(e),Ge(e),tn(t,e,n))},vn=!1;function bn(t){var e=s({},ae.showClass,t.showClass),n=s({},ae.hideClass,t.hideClass),o=s({},ae,t);return o.showClass=e,o.hideClass=n,!1===t.animation&&(o.showClass={popup:"swal2-noanimation",backdrop:"swal2-noanimation"},o.hideClass={}),o}function yn(n,o,i){return new Promise(function(t){function e(t){n.closePopup({dismiss:t})}Se.swalPromiseResolve.set(n,t),o.confirmButton.onclick=function(){return an(n,i)},o.cancelButton.onclick=function(){return cn(n,e)},o.closeButton.onclick=function(){return e(j.close)},gn(n,o,e),ln(n,ie,i,e),(i.toast&&(i.input||i.footer||i.showCloseButton)?ht:gt)(document.body,I["toast-column"]),en(n,i),He(i),kn(ie,i,e),xn(o,i),setTimeout(function(){o.container.scrollTop=0})})}function wn(t){var e={popup:D(),container:R(),content:U(),actions:K(),confirmButton:z(),cancelButton:W(),closeButton:Z(),validationMessage:F(),progressSteps:_()};return jt.domCache.set(t,e),e}function Cn(){document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()}var kn=function(t,e,n){var o=Y();bt(o),e.timer&&(t.timeout=new Ie(function(){n("timer"),delete t.timeout},e.timer),e.timerProgressBar&&(vt(o),setTimeout(function(){t.timeout.running&&ct(e.timer)})))},xn=function(t,e){if(!e.toast)return E(e.allowEnterKey)?e.focusCancel&&yt(t.cancelButton)?t.cancelButton.focus():e.focusConfirm&&yt(t.confirmButton)?t.confirmButton.focus():void Qe(0,-1,1):Cn()};function Pn(t){delete t.params,delete ie.keydownHandler,delete ie.keydownTarget,Bn(jt),Bn(Se)}var An,Bn=function(t){for(var e in t)t[e]=new WeakMap},V=Object.freeze({hideLoading:me,disableLoading:me,getInput:function(t){var e=jt.innerParams.get(t||this);return(t=jt.domCache.get(t||this))?nt(t.content,e.input):null},close:Oe,closePopup:Oe,closeModal:Oe,closeToast:Oe,enableButtons:function(){qe(this,["confirmButton","cancelButton"],!1)},disableButtons:function(){qe(this,["confirmButton","cancelButton"],!0)},enableInput:function(){return Ve(this.getInput(),!1)},disableInput:function(){return Ve(this.getInput(),!0)},showValidationMessage:function(t){var e=jt.domCache.get(this);X(e.validationMessage,t),t=window.getComputedStyle(e.popup),e.validationMessage.style.marginLeft="-".concat(t.getPropertyValue("padding-left")),e.validationMessage.style.marginRight="-".concat(t.getPropertyValue("padding-right")),vt(e.validationMessage),(e=this.getInput())&&(e.setAttribute("aria-invalid",!0),e.setAttribute("aria-describedBy",I["validation-message"]),ft(e),ht(e,I.inputerror))},resetValidationMessage:function(){var t=jt.domCache.get(this);t.validationMessage&&bt(t.validationMessage),(t=this.getInput())&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedBy"),gt(t,I.inputerror))},getProgressSteps:function(){return jt.domCache.get(this).progressSteps},_main:function(t){!function(t){for(var e in t)le(e),t.toast&&de(e),pe(e)}(t),ie.currentInstance&&ie.currentInstance._destroy(),ie.currentInstance=this;var e=bn(t);return Re(e),Object.freeze(e),ie.timeout&&(ie.timeout.stop(),delete ie.timeout),clearTimeout(ie.restoreFocusTimeout),t=wn(this),Ft(this,e),jt.innerParams.set(this,e),yn(this,t,e)},update:function(e){var t=D(),n=jt.innerParams.get(this);if(!t||G(t,n.hideClass.popup))return A("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");var o={};Object.keys(e).forEach(function(t){En.isUpdatableParameter(t)?o[t]=e[t]:A('Invalid parameter to update: "'.concat(t,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js'))}),n=s({},n,o),Ft(this,n),jt.innerParams.set(this,n),Object.defineProperties(this,{params:{value:s({},this.params,e),writable:!1,enumerable:!0}})},_destroy:function(){var t=jt.domCache.get(this),e=jt.innerParams.get(this);e&&(t.popup&&ie.swalCloseEventFinishedCallback&&(ie.swalCloseEventFinishedCallback(),delete ie.swalCloseEventFinishedCallback),ie.deferDisposalTimer&&(clearTimeout(ie.deferDisposalTimer),delete ie.deferDisposalTimer),"function"==typeof e.onDestroy&&e.onDestroy(),Pn(this))}}),Sn=function(){function i(){if(a(this,i),"undefined"!=typeof window){"undefined"==typeof Promise&&B("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)"),An=this;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=Object.freeze(this.constructor.argsToParams(e));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0,configurable:!0}});o=this._main(this.params);jt.promise.set(this,o)}}return c(i,[{key:"then",value:function(t){return jt.promise.get(this).then(t)}},{key:"finally",value:function(t){return jt.promise.get(this).finally(t)}}]),i}();s(Sn.prototype,V),s(Sn,fe),Object.keys(V).forEach(function(t){Sn[t]=function(){if(An)return An[t].apply(An,arguments)}}),Sn.DismissReason=j,Sn.version="9.17.2";var En=Sn;return En.default=En}),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2);