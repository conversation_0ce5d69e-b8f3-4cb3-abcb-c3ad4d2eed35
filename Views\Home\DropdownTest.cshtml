@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>下拉菜单测试</title>
    <link rel="stylesheet" href="/lib/bootstrap-3.4.1-dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="/lib/fontawesome/font-awesome.min.css" />
    <link rel="stylesheet" href="/css/bootstrap-nav.css" />
    <style>
        body {
            padding: 50px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>下拉菜单功能测试</h1>

        <div class="test-section">
            <h3>1. 用户菜单测试</h3>
            <div class="modern-user-menu" style="display: inline-block;">
                <div class="modern-user-info" style="background: #2c3e50; color: white; padding: 10px 15px; border-radius: 4px; cursor: pointer;">
                    <i class="fa fa-user-circle"></i>
                    <span>测试用户</span>
                    <i class="fa fa-chevron-down"></i>
                </div>
                <div class="modern-user-dropdown">
                    <a href="javascript:;" class="modern-dropdown-item">
                        <i class="fa fa-key"></i>
                        <span>修改密码</span>
                    </a>
                    <a href="javascript:;" class="modern-dropdown-item">
                        <i class="fa fa-sign-out"></i>
                        <span>退出登录</span>
                    </a>
                </div>
            </div>
            <p><small>点击用户名应该显示下拉菜单</small></p>
        </div>

        <div class="test-section">
            <h3>2. 操作菜单测试</h3>
            <div class="modern-action-dropdown" style="display: inline-block;">
                <button class="modern-action-btn">
                    <i class="fa fa-cog"></i>
                    <span>页面操作</span>
                    <i class="fa fa-chevron-down"></i>
                </button>
                <div class="modern-action-menu">
                    <a href="javascript:;" class="modern-action-item">
                        <i class="fa fa-refresh"></i>
                        <span>刷新当前</span>
                    </a>
                    <a href="javascript:;" class="modern-action-item">
                        <i class="fa fa-times-circle"></i>
                        <span>关闭其他</span>
                    </a>
                    <a href="javascript:;" class="modern-action-item">
                        <i class="fa fa-times"></i>
                        <span>关闭全部</span>
                    </a>
                </div>
            </div>
            <p><small>点击操作按钮应该显示下拉菜单</small></p>
        </div>

        <div class="test-section">
            <h3>3. 侧边栏菜单测试</h3>
            <ul class="modern-nav" style="background: #2c3e50; border-radius: 6px; padding: 10px; width: 250px;">
                <li class="modern-nav-item">
                    <a href="javascript:;" class="modern-nav-link" data-toggle="submenu" style="position: relative;">
                        <i class="modern-nav-icon fa fa-th-large"></i>
                        <span class="modern-nav-text">仓库管理</span>
                        <i class="modern-nav-toggle fa fa-chevron-down"></i>
                    </a>
                    <ul class="modern-submenu">
                        <li class="modern-submenu-item">
                            <a href="javascript:;" class="modern-submenu-link">
                                <i class="fa fa-table" style="margin-right: 8px;"></i>
                                <span>库存管理</span>
                            </a>
                        </li>
                        <li class="modern-submenu-item">
                            <a href="javascript:;" class="modern-submenu-link">
                                <i class="fa fa-cog" style="margin-right: 8px;"></i>
                                <span>仓库配置</span>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="modern-nav-item">
                    <a href="javascript:;" class="modern-nav-link" data-toggle="submenu" style="position: relative;">
                        <i class="modern-nav-icon fa fa-edit"></i>
                        <span class="modern-nav-text">订单管理</span>
                        <i class="modern-nav-toggle fa fa-chevron-down"></i>
                    </a>
                    <ul class="modern-submenu">
                        <li class="modern-submenu-item">
                            <a href="javascript:;" class="modern-submenu-link">
                                <i class="fa fa-bar-chart" style="margin-right: 8px;"></i>
                                <span>订单总览</span>
                            </a>
                        </li>
                        <li class="modern-submenu-item">
                            <a href="javascript:;" class="modern-submenu-link">
                                <i class="fa fa-pencil" style="margin-right: 8px;"></i>
                                <span>订单处理</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
            <p><small>点击仓库管理或订单管理应该展开子菜单</small></p>
            <button id="testSubmenuBtn" class="btn btn-primary">测试子菜单功能</button>
        </div>

        <div class="test-section">
            <h3>4. 调试信息</h3>
            <button id="debugBtn" class="btn btn-info">检查JavaScript状态</button>
            <div id="debugInfo" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; display: none;">
                <p><strong>jQuery版本:</strong> <span id="jqueryVersion"></span></p>
                <p><strong>事件绑定状态:</strong> <span id="eventStatus"></span></p>
                <p><strong>CSS类状态:</strong> <span id="cssStatus"></span></p>
            </div>
        </div>
    </div>

    <script src="/lib/jquery/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('页面加载完成，开始绑定事件...');

            // 用户菜单点击效果
            $(document).on('click', '.modern-user-info', function(e) {
                console.log('用户菜单被点击');
                e.preventDefault();
                e.stopPropagation();
                var dropdown = $(this).next('.modern-user-dropdown');
                dropdown.toggleClass('show');
                console.log('下拉框状态:', dropdown.hasClass('show'));
            });

            // 操作菜单点击效果
            $(document).on('click', '.modern-action-btn', function(e) {
                console.log('操作菜单被点击');
                e.preventDefault();
                e.stopPropagation();
                var menu = $(this).next('.modern-action-menu');
                menu.toggleClass('show');
                console.log('操作菜单状态:', menu.hasClass('show'));
            });

            // 子菜单展开/收缩
            $(document).on('click', '[data-toggle="submenu"]', function(e) {
                console.log('子菜单被点击');
                e.preventDefault();
                var $this = $(this);
                var $submenu = $this.next('.modern-submenu');
                var $toggle = $this.find('.modern-nav-toggle');

                $submenu.toggleClass('show');
                $toggle.toggleClass('rotated');
                console.log('子菜单状态:', $submenu.hasClass('show'));
            });

            // 点击其他地方关闭下拉框
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.modern-user-menu').length) {
                    $('.modern-user-dropdown').removeClass('show');
                }
                if (!$(e.target).closest('.modern-action-dropdown').length) {
                    $('.modern-action-menu').removeClass('show');
                }
            });

            // 测试子菜单按钮
            $('#testSubmenuBtn').on('click', function() {
                console.log('开始测试子菜单...');
                var submenuLinks = $('[data-toggle="submenu"]');
                console.log('找到的子菜单链接数量:', submenuLinks.length);

                submenuLinks.each(function(index) {
                    console.log('子菜单链接 ' + index + ':', $(this).find('.modern-nav-text').text());
                });

                if (submenuLinks.length > 0) {
                    console.log('模拟点击第一个子菜单...');
                    submenuLinks.first().trigger('click');
                }
            });

            // 调试按钮
            $('#debugBtn').on('click', function() {
                $('#jqueryVersion').text($.fn.jquery || '未知');
                $('#eventStatus').text('事件已绑定');
                $('#cssStatus').text($('.modern-user-dropdown').length + ' 个下拉框元素，' +
                                   $('.modern-submenu').length + ' 个子菜单');
                $('#debugInfo').show();
            });

            console.log('所有事件绑定完成');
        });
    </script>
</body>
</html>
