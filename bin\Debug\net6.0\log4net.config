﻿<log4net>
  <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
    <!--定义文件存放位置-->
    <file value="log/" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="yyyyMMdd'.txt'" />
    <staticLogFileName value="false" />
    <param name="MaxSizeRollBackups" value="100" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
    </layout>
  </appender>

  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout" value="%date [%thread] %-5level %logger - %message%newline" />
  </appender>
  <root>
    <level value="INFO" />
    <appender-ref ref="ConsoleAppender" />
    <!--文件形式记录日志-->
    <appender-ref ref="RollingLogFileAppender" />
  </root>
</log4net>