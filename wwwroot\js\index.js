var $, tab, skyconsWeather;
layui.config({
    base: "/js/"
}).use(['bodyTab', 'form', 'element', 'layer', 'jquery', 'cookie'], function () {
    var form = layui.form,
        layer = layui.layer,
        element = layui.element;
    $ = layui.jquery;
    tab = layui.bodyTab({
        openTabNum: "50",  //最大可打开窗口数量
        url: "/UserSession/GetModulesTree" //获取菜单json地址
    });
    $("body").addClass("cyan");

    // 现代化导航菜单事件处理
    initModernNavEvents();

    $(".menu_three").on("click", function () {
        $(this).next().toggle();
        $.each($(this).parent().siblings(), function (i, e) {
            $(e).find("ol").hide();;
        });
    })

    $("ol").on("click", "li a", function () {
        $.each($(this).parent().siblings(), function (i, e) {
            $(e).find("a").removeClass('three_this')
        });
        $(this).addClass('three_this');                            // 添加当前元素的样式
    })

    // 初始化现代化导航功能
    function initModernNavEvents() {
        // 侧边栏切换功能
        $(document).on('click', '#sidebarToggle', function() {
            $('#modernSidebar').toggleClass('collapsed');
            $('#mainContent').toggleClass('sidebar-collapsed');
        });

        // 现代化用户菜单点击效果
        $(document).on('click', '.modern-user-info', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).next('.modern-user-dropdown').toggleClass('show');
        });

        // 现代化操作菜单点击效果
        $(document).on('click', '.modern-action-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).next('.modern-action-menu').toggleClass('show');
        });

        // 点击其他地方关闭下拉框
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.modern-user-menu').length) {
                $('.modern-user-dropdown').removeClass('show');
            }
            if (!$(e.target).closest('.modern-action-dropdown').length) {
                $('.modern-action-menu').removeClass('show');
            }
        });

        // 子菜单展开/收缩
        $(document).on('click', '[data-toggle="submenu"]', function(e) {
            console.log('子菜单被点击了！');
            e.preventDefault();
            e.stopPropagation();
            var $this = $(this);
            var $submenu = $this.next('.modern-submenu');
            var $toggle = $this.find('.modern-nav-toggle');

            console.log('子菜单元素:', $submenu.length);
            console.log('箭头元素:', $toggle.length);

            $submenu.toggleClass('show');
            $toggle.toggleClass('rotated');

            console.log('子菜单现在的状态:', $submenu.hasClass('show'));
        });

        // 菜单项点击事件
        $(document).on('click', '.modern-nav-link[data-url], .modern-submenu-link[data-url]', function(e) {
            if (!$(this).attr('data-toggle')) {
                // 移除其他活动状态
                $('.modern-nav-link, .modern-submenu-link').removeClass('active');
                // 添加当前活动状态
                $(this).addClass('active');

                // 调用原有的标签页添加功能
                tab.tabAdd($(this));
            }
        });

        // 导航搜索功能
        $(document).on('input', '#navSearch', function() {
            var searchTerm = $(this).val().toLowerCase();
            var navItems = $('.modern-nav-item, .modern-submenu-item');

            if (searchTerm === '') {
                navItems.show();
                $('.modern-submenu').removeClass('show');
            } else {
                navItems.hide();

                navItems.each(function() {
                    var text = $(this).find('.modern-nav-text, span').text().toLowerCase();
                    if (text.indexOf(searchTerm) !== -1) {
                        $(this).show();
                        // 如果是子菜单项，也显示父菜单
                        if ($(this).hasClass('modern-submenu-item')) {
                            $(this).closest('.modern-submenu').addClass('show').prev('.modern-nav-link').parent().show();
                        }
                    }
                });
            }
        });

        // 移动端适配
        $(window).on('resize', function() {
            if ($(window).width() <= 768) {
                $('#modernSidebar').removeClass('collapsed');
                $('#mainContent').removeClass('sidebar-collapsed');
            }
        });

        // 移动端点击遮罩关闭菜单
        $(document).on('click', function(e) {
            if ($(window).width() <= 768 && !$(e.target).closest('#modernSidebar, #sidebarToggle').length) {
                $('#modernSidebar').removeClass('mobile-show');
            }
        });

        // 移动端菜单按钮
        $(document).on('click', '#sidebarToggle', function() {
            if ($(window).width() <= 768) {
                $('#modernSidebar').toggleClass('mobile-show');
            }
        });
    }

    $.get('/UserSession/GetUserName',
        function (data) {
            //console.log(data);
            $("#username").html(data);
            $("#usernametop").html(data);
        }
    );
    $.post('/UserSession/GetUserAuth', function (data) {
        $("#csCode").html(data.csCode);
    });

    //todo:默认登录后取第一个机构的id作为默认，可以在【个人中心】界面修改默认
    //在大型业务系统中，应该让用户登录成功后弹出选择框选择操作的机构
    //$.get('/UserSession/GetOrgs',
    //	function(data) {
    //	    var orgs = JSON.parse(data).Result;
    //		var defaultorgId = orgs[0].Id;
    //		$.cookie('defaultorgid', defaultorgId,{path: '/'});
    //	});


    //更换皮肤
    function skins() {
        var skin = window.sessionStorage.getItem("skin");
        if (skin) { //如果更换过皮肤
            if (window.sessionStorage.getItem("skinValue") != "自定义") {
                $("body").addClass(window.sessionStorage.getItem("skinBlue"));
            } else {
                $(".layui-layout-admin .layui-header").css("background-color", skin.split(',')[0]);
                $(".layui-bg-black").css("background-color", skin.split(',')[1]);
                $(".hideMenu").css("background-color", skin.split(',')[2]);
            }
        } else {
            $("body").addClass("cyan");
        }
    }
    skins();
    //$(".changeSkin").click(function(){
    //	layer.open({
    //		title : "更换皮肤",
    //		area : ["310px","280px"],
    //		type : "1",
    //		content : '<div class="skins_box">'+
    //					'<form class="layui-form">'+
    //						'<div class="layui-form-item">'+
    //							'<input type="radio" name="skin" value="默认" title="默认" lay-filter="default" checked="">'+
    //							'<input type="radio" name="skin" value="藏青" title="藏青" lay-filter="cyan">'+
    //							'<input type="radio" name="skin" value="蓝色" title="蓝色" lay-filter="blue">'+
    //							'<input type="radio" name="skin" value="自定义" title="自定义" lay-filter="custom">'+
    //							'<div class="skinCustom">'+
    //								'<input type="text" class="layui-input topColor" name="topSkin" placeholder="顶部颜色" />'+
    //								'<input type="text" class="layui-input leftColor" name="leftSkin" placeholder="左侧颜色" />'+
    //								'<input type="text" class="layui-input menuColor" name="btnSkin" placeholder="顶部菜单按钮" />'+
    //							'</div>'+
    //						'</div>'+
    //						'<div class="layui-form-item skinBtn">'+
    //							'<a href="javascript:;" class="layui-btn layui-btn-small layui-btn-normal" lay-submit="" lay-filter="changeSkin">确定更换</a>'+
    //							'<a href="javascript:;" class="layui-btn layui-btn-small layui-btn-primary" lay-submit="" lay-filter="noChangeSkin">我再想想</a>'+
    //						'</div>'+
    //					'</form>'+
    //				'</div>',
    //		success : function(index, layero){
    //			var skin = window.sessionStorage.getItem("skin");
    //			if(window.sessionStorage.getItem("skinValue")){
    //				$(".skins_box input[value="+window.sessionStorage.getItem("skinValue")+"]").attr("checked","checked");
    //			};
    //			if($(".skins_box input[value=自定义]").attr("checked")){
    //				$(".skinCustom").css("visibility","inherit");
    //				$(".topColor").val(skin.split(',')[0]);
    //				$(".leftColor").val(skin.split(',')[1]);
    //				$(".menuColor").val(skin.split(',')[2]);
    //			};
    //			form.render();
    //			$(".skins_box").removeClass("layui-hide");
    //			$(".skins_box .layui-form-radio").on("click",function(){
    //				var skinColor;
    //				if($(this).find("span").text() == "藏青"){
    //					skinColor = "cyan";
    //				}else if($(this).find("span").text() == "蓝色"){
    //					skinColor = "blue";
    //				}else if($(this).find("span").text() == "默认"){
    //					skinColor = "";
    //				}
    //				if($(this).find("span").text() != "自定义"){
    //					$(".topColor,.leftColor,.menuColor").val('');
    //					$("body").removeAttr("class").addClass("main_body "+skinColor+"");
    //					$(".skinCustom").removeAttr("style");
    //					$(".layui-bg-black,.hideMenu,.layui-layout-admin .layui-header").removeAttr("style");
    //				}else{
    //					$(".skinCustom").css("visibility","inherit");
    //				}
    //			})
    //			var skinStr,skinColor;
    //			$(".topColor").blur(function(){
    //				$(".layui-layout-admin .layui-header").css("background-color",$(this).val());
    //			})
    //			$(".leftColor").blur(function(){
    //				$(".layui-bg-black").css("background-color",$(this).val());
    //			})
    //			$(".menuColor").blur(function(){
    //				$(".hideMenu").css("background-color",$(this).val());
    //			})

    //			form.on("submit(changeSkin)",function(data){
    //				if(data.field.skin != "自定义"){
    //					if(data.field.skin == "藏青"){
    //						skinColor = "cyan";
    //					}else if(data.field.skin == "蓝色"){
    //						skinColor = "blue";
    //					}else if(data.field.skin == "默认"){
    //						skinColor = "";
    //					}
    //					window.sessionStorage.setItem("skin",skinColor);
    //				}else{
    //					skinStr = $(".topColor").val()+','+$(".leftColor").val()+','+$(".menuColor").val();
    //					window.sessionStorage.setItem("skin",skinStr);
    //					$("body").removeAttr("class").addClass("main_body");
    //				}
    //				window.sessionStorage.setItem("skinValue",data.field.skin);
    //				layer.closeAll("page");
    //			});
    //			form.on("submit(noChangeSkin)",function(){
    //				$("body").removeAttr("class").addClass("main_body "+window.sessionStorage.getItem("skin")+"");
    //				$(".layui-bg-black,.hideMenu,.layui-layout-admin .layui-header").removeAttr("style");
    //				skins();
    //				layer.closeAll("page");
    //			});
    //		},
    //		cancel : function(){
    //			$("body").removeAttr("class").addClass("main_body "+window.sessionStorage.getItem("skin")+"");
    //			$(".layui-bg-black,.hideMenu,.layui-layout-admin .layui-header").removeAttr("style");
    //			skins();
    //		}
    //	})
    //})

    //退出



    $(".changePasswordLink").click(function (e) {
        const changePasswordFormHtml = `
<style>
    /* 自定义样式 */
    .is-invalid {
        border-color: red !important; /* 设置边框颜色为红色 */
    }

    /* 错误消息的样式 */
    .invalid-feedback {
        display: none; /* 默认隐藏错误消息 */
        color: red; /* 错误消息的颜色 */
    }

    /* 当输入框无效时显示错误消息 */
    .is-invalid + .invalid-feedback {
        display: block; /* 显示错误消息 */
    }
</style>
<div>
    <div class="layui-row">
        <div class="layui-col-md6 layui-col-md-offset3">
            <form class="form" id="changePasswordForm" action="">
                <div class="layui-form-item">
                <div class="layui-input-block">
                    <label class="layui-form-label" style="width: 200px;text-align: left;font-weight:bold;">Current Password:</label>\
                        <input type="password" id="oldPassword" required lay-verify="required" placeholder="Enter current password" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">

                    <div class="layui-input-block">
                        <label class="layui-form-label"  style="width: 200px;text-align: left;font-weight:bold;">New Password:</label>
                        <input type="password" id="newPassword" required lay-verify="required"" placeholder="Enter new password" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">

                    <div class="layui-input-block">
                        <label class="layui-form-label"  style="width: 200px;text-align: left;font-weight:bold;">Confirm New Password:</label>
                        <input type="password" id="confirmNewPassword" required lay-verify="required" placeholder="Confirm new password" autocomplete="off" class="layui-input">
                        <div class="invalid-feedback" id="passwordMismatch">Password mismatch!</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" type="button" onclick="changePassword()">Commit Changes</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    const oldPassword = document.getElementById('oldPassword');
    const newPassword = document.getElementById('newPassword');
    const confirmNewPassword = document.getElementById('confirmNewPassword');
    const passwordMismatchFeedback = document.getElementById('passwordMismatch');
    newPassword.addEventListener('input', checkPasswords);
    confirmNewPassword.addEventListener('input', checkPasswords);

    function checkPasswords() {

        if (newPassword.value !== confirmNewPassword.value) {
            confirmNewPassword.classList.add('is-invalid'); // 添加无效样式
            passwordMismatchFeedback.style.display = 'block'; // 显示错误消息
            return false;
        } else {
            confirmNewPassword.classList.remove('is-invalid'); // 移除无效样式
            passwordMismatchFeedback.style.display = 'none'; // 隐藏错误消息
            return true;
        }
    }
    function changePassword() {
        if (!newPassword.value) {
            return;
        }
        if (newPassword.value !== confirmNewPassword.value) {
            return;
        }
        layer.confirm('Are you sure you want to change your password?', {
        title: 'Confirmation',
        btn: ['Yes', 'No'] // 按钮
      }, function(index){
          $.ajax({
              method: 'POST',
              url: '/UserManage/ChangePassword',
              data: {
                  oldPassword: oldPassword.value,
                  newPassword: newPassword.value,
                  confirmPassword: confirmNewPassword.value,
              }
              }).then(function (response) {
                  if (response.code === 200) {
                      layer.msg(response.msg, {icon: 1, time: 2000}); // 成功提示
                      // 重新进入网页
                      setTimeout(function () {
                            location.reload();
                          }, 3000);
                  } else {
                       layer.msg(response.msg, {icon: 2, time: 2000}); // 成功提示
                  }
             });

      }, function(index){
        // 用户点击了“否”
        layer.close();
      });

    }
</script>
`;
        // 使用layer.open打开模态对话框
        layer.open({
            title: "Change Password",
            type: 1, // 页面层
            area: ["900px", "500px"],
            content: changePasswordFormHtml, // 加载的内容
            success: function (layero, index) {
                // 模态对话框打开后的初始化代码

            }
        });
    });


    //退出


    $(".signOut").click(function () {
        window.sessionStorage.removeItem("menu");
        menu = [];
        window.sessionStorage.removeItem("curmenu");
    })

    //隐藏左侧导航
    $(".hideMenu").click(function () {
        $(".layui-layout-admin").toggleClass("showMenu");
        //渲染顶部窗口
        tab.tabMove();
    })

    //渲染左侧菜单
    tab.render();

    //锁屏
    function lockPage() {
        layer.open({
            title: false,
            type: 1,
            content: '	<div class="admin-header-lock" id="lock-box">' +
                '<div class="admin-header-lock-img"><img src="/images/face.jpg"/></div>' +
                /*'<div class="admin-header-lock-name" id="lockUserName">admin</div>'+*/
                '<div class="input_btn">' +
                '<input type="password" class="admin-header-lock-input layui-input" autocomplete="off" placeholder="请输入密码解锁.." name="lockPwd" id="lockPwd" />' +
                '<button class="layui-btn" id="unlock">解锁</button>' +
                '</div>' +
                '<p>请输入“123456”，否则不会解锁成功哦！！！</p>' +
                '</div>',
            closeBtn: 0,
            shade: 0.9
        })
        $(".admin-header-lock-input").focus();
    }
    $(".lockcms").on("click", function () {
        window.sessionStorage.setItem("lockcms", true);
        lockPage();
    })
    // 判断是否显示锁屏
    if (window.sessionStorage.getItem("lockcms") == "true") {
        lockPage();
    }
    // 解锁
    $("body").on("click", "#unlock", function () {
        if ($(this).siblings(".admin-header-lock-input").val() == '') {
            layer.msg("请输入解锁密码！");
            $(this).siblings(".admin-header-lock-input").focus();
        } else {
            if ($(this).siblings(".admin-header-lock-input").val() == "123456") {
                window.sessionStorage.setItem("lockcms", false);
                $(this).siblings(".admin-header-lock-input").val('');
                layer.closeAll("page");
            } else {
                layer.msg("密码错误，请重新输入！");
                $(this).siblings(".admin-header-lock-input").val('').focus();
            }
        }
    });

    //手机设备的简单适配
    var treeMobile = $('.site-tree-mobile'),
        shadeMobile = $('.site-mobile-shade')

    treeMobile.on('click', function () {
        $('body').addClass('site-mobile');
    });

    shadeMobile.on('click', function () {
        $('body').removeClass('site-mobile');
    });

    // 添加新窗口
    $("body").on("click", ".layui-nav .layui-nav-item a", function () {
        //如果不存在子级
        if ($(this).siblings().length == 0) {
            addTab($(this));
            $('body').removeClass('site-mobile');  //移动端点击菜单关闭菜单层
        }
        $(this).parent("li").siblings().removeClass("layui-nav-itemed");
    })

    //公告层
    function showNotice() {
        layer.open({
            type: 1,
            title: "System Announcement",
            closeBtn: false,
            area: '310px',
            shade: 0.8,
            id: 'LAY_layuipro',
            btn: ['火速围观'],
            moveType: 1,
            content: '<div style="padding:15px 20px; text-align:justify; line-height: 22px; text-indent:2em;border-bottom:1px solid #e2e2e2;">' +
                '<p>系统开发中</p>' +
                '<p>系统开发中！</p></div>',
            success: function (layero) {
                var btn = layero.find('.layui-layer-btn');
                btn.css('text-align', 'center');
                btn.on("click", function () {
                    window.sessionStorage.setItem("showNotice", "true");
                })
                if ($(window).width() > 432) {  //如果页面宽度不足以显示顶部“系统公告”按钮，则不提示
                    btn.on("click", function () {
                        layer.tips('系统公告躲在了这里', '#showNotice', {
                            tips: 3
                        });
                    })
                }
            }
        });
    }
    //判断是否处于锁屏状态(如果关闭以后则未关闭浏览器之前不再显示)
    if (window.sessionStorage.getItem("lockcms") != "true" && window.sessionStorage.getItem("showNotice") != "true") {
        //showNotice();
    }
    $(".showNotice").on("click", function () {
        showNotice();
    })

    //刷新后还原打开的窗口
    if (window.sessionStorage.getItem("menu") != null) {
        menu = JSON.parse(window.sessionStorage.getItem("menu"));
        curmenu = window.sessionStorage.getItem("curmenu");
        var openTitle = '';
        for (var i = 0; i < menu.length; i++) {
            openTitle = '';
            if (menu[i].icon) {
                if (menu[i].icon.split("-")[0] == 'icon') {
                    openTitle += '<i class="iconfont ' + menu[i].icon + '"></i>';
                } else {
                    openTitle += '<i class="layui-icon">' + menu[i].icon + '</i>';
                }
            }
            openTitle += '<cite>' + menu[i].title + '</cite>';
            openTitle += '<i class="layui-icon layui-unselect layui-tab-close" data-id="' + menu[i].layId + '">&#x1006;</i>';
            element.tabAdd("bodyTab", {
                title: openTitle,
                content: "<iframe src='" + menu[i].href + "' data-id='" + menu[i].layId + "'></frame>",
                id: menu[i].layId
            })
            //定位到刷新前的窗口
            if (curmenu != "undefined") {
                if (curmenu == '' || curmenu == "null") {  //定位到后台首页
                    element.tabChange("bodyTab", '');
                } else if (JSON.parse(curmenu).title == menu[i].title) {  //定位到刷新前的页面
                    element.tabChange("bodyTab", menu[i].layId);
                }
            } else {
                element.tabChange("bodyTab", menu[menu.length - 1].layId);
            }
        }
        //渲染顶部窗口
        tab.tabMove();
    }

    //刷新当前
    $(".refresh").on("click", function () {  //此处添加禁止连续点击刷新一是为了降低服务器压力，另外一个就是为了防止超快点击造成chrome本身的一些js文件的报错(不过貌似这个问题还是存在，不过概率小了很多)
        if ($(this).hasClass("refreshThis")) {
            $(this).removeClass("refreshThis");
            $(".clildFrame .layui-tab-item.layui-show").find("iframe")[0].contentWindow.location.reload(true);
        } else {
            layer.msg("您点击的速度超过了服务器的响应速度，还是等两秒再刷新吧！");
            setTimeout(function () {
                $(".refresh").addClass("refreshThis");
            }, 2000)
        }
    })

    //关闭其他
    $(".closePageOther").on("click", function () {
        if ($("#top_tabs li").length > 2 && $("#top_tabs li.layui-this cite").text() != "后台首页") {
            var menu = JSON.parse(window.sessionStorage.getItem("menu"));
            $("#top_tabs li").each(function () {
                if ($(this).attr("lay-id") != '' && !$(this).hasClass("layui-this")) {
                    element.tabDelete("bodyTab", $(this).attr("lay-id")).init();
                    //此处将当前窗口重新获取放入session，避免一个个删除来回循环造成的不必要工作量
                    for (var i = 0; i < menu.length; i++) {
                        if ($("#top_tabs li.layui-this cite").text() == menu[i].title) {
                            menu.splice(0, menu.length, menu[i]);
                            window.sessionStorage.setItem("menu", JSON.stringify(menu));
                        }
                    }
                }
            })
        } else if ($("#top_tabs li.layui-this cite").text() == "后台首页" && $("#top_tabs li").length > 1) {
            $("#top_tabs li").each(function () {
                if ($(this).attr("lay-id") != '' && !$(this).hasClass("layui-this")) {
                    element.tabDelete("bodyTab", $(this).attr("lay-id")).init();
                    window.sessionStorage.removeItem("menu");
                    menu = [];
                    window.sessionStorage.removeItem("curmenu");
                }
            })
        } else {
            layer.msg("没有可以关闭的窗口了@_@");
        }
        //渲染顶部窗口
        tab.tabMove();
    })
    //关闭全部
    $(".closePageAll").on("click", function () {
        if ($("#top_tabs li").length > 1) {
            $("#top_tabs li").each(function () {
                if ($(this).attr("lay-id") != '') {
                    element.tabDelete("bodyTab", $(this).attr("lay-id")).init();
                    window.sessionStorage.removeItem("menu");
                    menu = [];
                    window.sessionStorage.removeItem("curmenu");
                }
            })
        } else {
            layer.msg("没有可以关闭的窗口了@_@");
        }
        //渲染顶部窗口
        tab.tabMove();
    })
})

//打开新窗口
function addTab(_this) {
    tab.tabAdd(_this);
}

//弹窗
function donation() {
    layer.tab({
        area: ['260px', '367px'],
        tab: [{
            title: "微信",
            content: "<div style='padding:30px;overflow:hidden;background:#d2d0d0;'></div>"
        }, {
            title: "支付宝",
            content: "<div style='padding:30px;overflow:hidden;background:#d2d0d0;'></div>"
        }]
    })
}

