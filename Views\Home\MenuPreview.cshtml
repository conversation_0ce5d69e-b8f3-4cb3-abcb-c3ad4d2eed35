@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>现代化菜单预览 - OWMS-OV</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/lib/bootstrap-3.4.1-dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="/lib/fontawesome/font-awesome.min.css" />
    <link rel="stylesheet" href="/css/bootstrap-nav.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ecf0f1;
        }



        .demo-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li i {
            color: #27ae60;
            margin-right: 10px;
        }

       @@media (max-width: 768px) {
            .preview-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 现代化顶部导航 -->
    <div class="modern-header">
        <div class="modern-header-content">
            <div class="modern-header-left">
                <button type="button" class="modern-sidebar-toggle" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </button>
                <a href="#" class="modern-logo">OWMS-OV</a>
                <span class="modern-version">现代化预览版 v1.0</span>
            </div>
            <div class="modern-header-right">
                <div class="modern-customer-code">
                    <span>Customer Code: </span>
                    <span class="modern-code-value">DEMO001</span>
                </div>
                <div class="modern-user-menu">
                    <div class="modern-user-info">
                        <i class="fa fa-user-circle"></i>
                        <span>演示用户</span>
                        <i class="fa fa-chevron-down"></i>
                    </div>
                    <div class="modern-user-dropdown">
                        <a href="javascript:;" class="modern-dropdown-item">
                            <i class="fa fa-key"></i>
                            <span>修改密码</span>
                        </a>
                        <a href="javascript:;" class="modern-dropdown-item">
                            <i class="fa fa-sign-out"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 左侧导航 -->
    <div class="modern-sidebar" id="modernSidebar">
        <!-- 用户信息区域 -->
        <div class="modern-nav-user">
            <div class="modern-nav-user-name">管理员</div>
            <div class="modern-nav-user-role">OWMS-OV System</div>
        </div>

        <!-- 搜索框 -->
        <div class="modern-nav-search">
            <input type="text" placeholder="搜索菜单..." id="navSearch">
        </div>

        <!-- 导航菜单 -->
        <ul class="modern-nav">
            <li class="modern-nav-item">
                <a href="javascript:;" class="modern-nav-link active">
                    <i class="modern-nav-icon fa fa-home"></i>
                    <span class="modern-nav-text">仪表盘</span>
                </a>
            </li>

            <li class="modern-nav-item">
                <a href="javascript:;" class="modern-nav-link" data-toggle="submenu">
                    <i class="modern-nav-icon fa fa-th-large"></i>
                    <span class="modern-nav-text">仓库管理</span>
                    <i class="modern-nav-toggle fa fa-chevron-down"></i>
                </a>
                <ul class="modern-submenu">
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-table" style="margin-right: 8px;"></i>
                            <span>库存管理</span>
                        </a>
                    </li>
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-cog" style="margin-right: 8px;"></i>
                            <span>仓库配置</span>
                        </a>
                    </li>
                </ul>
            </li>

            <li class="modern-nav-item">
                <a href="javascript:;" class="modern-nav-link" data-toggle="submenu">
                    <i class="modern-nav-icon fa fa-edit"></i>
                    <span class="modern-nav-text">订单管理</span>
                    <i class="modern-nav-toggle fa fa-chevron-down"></i>
                </a>
                <ul class="modern-submenu">
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-bar-chart" style="margin-right: 8px;"></i>
                            <span>订单总览</span>
                        </a>
                    </li>
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-pencil" style="margin-right: 8px;"></i>
                            <span>订单处理</span>
                        </a>
                    </li>
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link" data-toggle="submenu">
                            <i class="fa fa-map-marker" style="margin-right: 8px;"></i>
                            <span>运输管理</span>
                            <i class="modern-nav-toggle fa fa-chevron-down"></i>
                        </a>
                        <ul class="modern-submenu" style="margin-left: 20px;">
                            <li class="modern-submenu-item">
                                <a href="javascript:;" class="modern-submenu-link">
                                    <i class="fa fa-shopping-cart" style="margin-right: 8px;"></i>
                                    <span>卡车运输</span>
                                </a>
                            </li>
                            <li class="modern-submenu-item">
                                <a href="javascript:;" class="modern-submenu-link">
                                    <i class="fa fa-cube" style="margin-right: 8px;"></i>
                                    <span>配送管理</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </li>

            <li class="modern-nav-item">
                <a href="javascript:;" class="modern-nav-link" data-toggle="submenu">
                    <i class="modern-nav-icon fa fa-bar-chart"></i>
                    <span class="modern-nav-text">报表分析</span>
                    <i class="modern-nav-toggle fa fa-chevron-down"></i>
                </a>
                <ul class="modern-submenu">
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-line-chart" style="margin-right: 8px;"></i>
                            <span>快速报表</span>
                        </a>
                    </li>
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-file-text" style="margin-right: 8px;"></i>
                            <span>自定义报表</span>
                        </a>
                    </li>
                </ul>
            </li>

            <li class="modern-nav-item">
                <a href="javascript:;" class="modern-nav-link" data-toggle="submenu">
                    <i class="modern-nav-icon fa fa-cog"></i>
                    <span class="modern-nav-text">系统管理</span>
                    <i class="modern-nav-toggle fa fa-chevron-down"></i>
                </a>
                <ul class="modern-submenu">
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-user" style="margin-right: 8px;"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-file" style="margin-right: 8px;"></i>
                            <span>文件管理</span>
                        </a>
                    </li>
                    <li class="modern-submenu-item">
                        <a href="javascript:;" class="modern-submenu-link">
                            <i class="fa fa-cogs" style="margin-right: 8px;"></i>
                            <span>系统配置</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>

    <!-- 现代化主内容区域 -->
    <div class="modern-main-content" id="modernMainContent">
        <div class="modern-tabs-container">
            <!-- 标签页标题栏 -->
            <div class="modern-tabs-header">
                <ul class="modern-tabs-nav">
                    <li class="modern-tab-item active">
                        <i class="fa fa-home"></i>
                        <span>现代化预览</span>
                    </li>
                </ul>

                <!-- 标签页操作菜单 -->
                <div class="modern-tabs-actions">
                    <div class="modern-action-dropdown">
                        <button class="modern-action-btn">
                            <i class="fa fa-cog"></i>
                            <span>页面操作</span>
                            <i class="fa fa-chevron-down"></i>
                        </button>
                        <div class="modern-action-menu">
                            <a href="javascript:;" class="modern-action-item">
                                <i class="fa fa-refresh"></i>
                                <span>刷新当前</span>
                            </a>
                            <a href="javascript:;" class="modern-action-item">
                                <i class="fa fa-times-circle"></i>
                                <span>关闭其他</span>
                            </a>
                            <a href="javascript:;" class="modern-action-item">
                                <i class="fa fa-times"></i>
                                <span>关闭全部</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标签页内容区域 -->
            <div class="modern-tabs-content">
                <div class="modern-tab-pane active" style="padding: 20px; overflow-y: auto;">
        <div class="demo-card">
            <h2><i class="fa fa-star text-warning"></i> 现代化菜单系统特性</h2>
            <ul class="feature-list">
                <li><i class="fa fa-check"></i> 现代化的视觉设计，使用渐变色和阴影效果</li>
                <li><i class="fa fa-check"></i> 流畅的动画过渡，提升用户体验</li>
                <li><i class="fa fa-check"></i> 智能搜索功能，快速定位菜单项</li>
                <li><i class="fa fa-check"></i> 响应式设计，完美适配各种设备</li>
                <li><i class="fa fa-check"></i> 可折叠侧边栏，节省屏幕空间</li>
                <li><i class="fa fa-check"></i> 支持多级菜单结构</li>
                <li><i class="fa fa-check"></i> FontAwesome图标系统</li>
                <li><i class="fa fa-check"></i> 向后兼容现有系统</li>
            </ul>
        </div>

        <div class="demo-card">
            <h3><i class="fa fa-info-circle text-info"></i> 使用说明</h3>
            <p><strong>菜单切换：</strong> 点击左上角的 <i class="fa fa-bars"></i> 按钮可以折叠/展开侧边栏</p>
            <p><strong>子菜单：</strong> 点击带有 <i class="fa fa-chevron-down"></i> 箭头的菜单项可以展开子菜单</p>
            <p><strong>搜索功能：</strong> 在侧边栏顶部的搜索框中输入关键词可以快速查找菜单</p>
            <p><strong>响应式：</strong> 在移动设备上，侧边栏会自动适配为覆盖模式</p>
        </div>

        <div class="demo-card">
            <h3><i class="fa fa-palette text-success"></i> 设计亮点</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>配色方案</h4>
                    <p>采用深色渐变背景，营造专业的视觉效果</p>
                    <p>悬停时使用蓝色渐变，提供清晰的交互反馈</p>
                </div>
                <div class="col-md-6">
                    <h4>动画效果</h4>
                    <p>使用CSS3过渡动画，所有交互都有流畅的视觉反馈</p>
                    <p>采用cubic-bezier缓动函数，提供自然的动画体验</p>
                </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/lib/jquery/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 侧边栏切换功能
            $('#sidebarToggle').on('click', function() {
                $('#modernSidebar').toggleClass('collapsed');
                $('#modernMainContent').toggleClass('sidebar-collapsed');
            });

            // 子菜单展开/收缩
            $(document).on('click', '[data-toggle="submenu"]', function(e) {
                e.preventDefault();
                var $this = $(this);
                var $submenu = $this.next('.modern-submenu');
                var $toggle = $this.find('.modern-nav-toggle');

                $submenu.toggleClass('show');
                $toggle.toggleClass('rotated');
            });

            // 菜单项点击事件
            $(document).on('click', '.modern-nav-link:not([data-toggle]), .modern-submenu-link', function(e) {
                e.preventDefault();
                // 移除其他活动状态
                $('.modern-nav-link, .modern-submenu-link').removeClass('active');
                // 添加当前活动状态
                $(this).addClass('active');
            });

            // 导航搜索功能
            $('#navSearch').on('input', function() {
                var searchTerm = $(this).val().toLowerCase();
                var navItems = $('.modern-nav-item, .modern-submenu-item');

                if (searchTerm === '') {
                    navItems.show();
                    $('.modern-submenu').removeClass('show');
                } else {
                    navItems.hide();

                    navItems.each(function() {
                        var text = $(this).find('.modern-nav-text, span').text().toLowerCase();
                        if (text.indexOf(searchTerm) !== -1) {
                            $(this).show();
                            // 如果是子菜单项，也显示父菜单
                            if ($(this).hasClass('modern-submenu-item')) {
                                $(this).closest('.modern-submenu').addClass('show').prev('.modern-nav-link').parent().show();
                            }
                        }
                    });
                }
            });

            // 移动端适配
            $(window).on('resize', function() {
                if ($(window).width() <= 768) {
                    $('#modernSidebar').removeClass('collapsed');
                    $('#modernMainContent').removeClass('sidebar-collapsed');
                }
            });
        });
    </script>
</body>
</html>
