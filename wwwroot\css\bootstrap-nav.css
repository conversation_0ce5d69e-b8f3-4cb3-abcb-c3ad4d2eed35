/* Modern Bootstrap Navigation Styles */

/* 侧边栏基础样式 */
.modern-sidebar {
    position: fixed;
    top: 50px;
    left: 0;
    width: 250px;
    height: calc(100vh - 50px);
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    box-shadow: 2px 0 15px rgba(0,0,0,0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow-y: auto;
    overflow-x: hidden;
    border-right: 1px solid rgba(255,255,255,0.1);
}

.modern-sidebar.collapsed {
    width: 60px;
}

/* 导航菜单样式 */
.modern-nav {
    padding: 0;
    margin: 0;
    list-style: none;
}

.modern-nav-item {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modern-nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-nav-link:hover {
    background: linear-gradient(90deg, rgba(52, 152, 219, 0.8), rgba(41, 128, 185, 0.8));
    color: #fff;
    text-decoration: none;
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.modern-nav-link.active {
    background: linear-gradient(90deg, #3498db, #2980b9);
    color: #fff;
    border-left: 4px solid #ecf0f1;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.modern-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.modern-nav-link:hover::before {
    left: 100%;
}

/* 图标样式 */
.modern-nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 15px;
    font-size: 18px;
    text-align: center;
    flex-shrink: 0;
}

.modern-sidebar.collapsed .modern-nav-icon {
    margin-right: 0;
}

/* 文本样式 */
.modern-nav-text {
    font-size: 14px;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.modern-sidebar.collapsed .modern-nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* 子菜单样式 */
.modern-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(0,0,0,0.2);
}

.modern-submenu.show {
    max-height: 500px;
}

.modern-submenu-item {
    border-bottom: 1px solid rgba(255,255,255,0.05);
}

.modern-submenu-link {
    display: flex;
    align-items: center;
    padding: 12px 20px 12px 50px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 13px;
}

.modern-submenu-link:hover {
    background: linear-gradient(90deg, rgba(46, 204, 113, 0.7), rgba(39, 174, 96, 0.7));
    color: #fff;
    text-decoration: none;
    padding-left: 55px;
    box-shadow: 0 2px 10px rgba(46, 204, 113, 0.3);
}

.modern-submenu-link.active {
    background: linear-gradient(90deg, #2ecc71, #27ae60);
    color: #fff;
    border-left: 3px solid #ecf0f1;
    box-shadow: 0 2px 10px rgba(46, 204, 113, 0.4);
}

/* 展开/收缩按钮 */
.modern-nav-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    font-size: 12px;
}

.modern-nav-toggle.rotated {
    transform: translateY(-50%) rotate(180deg);
}

/* 滚动条样式 */
.modern-sidebar::-webkit-scrollbar {
    width: 6px;
}

.modern-sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.modern-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.modern-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modern-sidebar {
        transform: translateX(-100%);
    }

    .modern-sidebar.mobile-show {
        transform: translateX(0);
    }
}

/* 主内容区域调整 */
.main-content {
    margin-left: 250px;
    transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
    margin-left: 60px;
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
}

/* 菜单切换按钮样式 */
.sidebar-toggle {
    background: rgba(255,255,255,0.1);
    border: none;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

/* 动画效果 */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.modern-nav-item {
    animation: slideInLeft 0.3s ease forwards;
}

.modern-nav-item:nth-child(1) { animation-delay: 0.1s; }
.modern-nav-item:nth-child(2) { animation-delay: 0.2s; }
.modern-nav-item:nth-child(3) { animation-delay: 0.3s; }
.modern-nav-item:nth-child(4) { animation-delay: 0.4s; }
.modern-nav-item:nth-child(5) { animation-delay: 0.5s; }

/* 徽章样式 */
.modern-nav-badge {
    background: #ff4757;
    color: #fff;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
}

/* 搜索框样式 */
.modern-nav-search {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modern-nav-search input {
    width: 100%;
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
}

.modern-nav-search input::placeholder {
    color: rgba(255,255,255,0.6);
}

.modern-nav-search input:focus {
    outline: none;
    border-color: rgba(255,255,255,0.4);
    background: rgba(255,255,255,0.15);
}

/* 用户信息区域 */
.modern-nav-user {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.modern-nav-user img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
    margin-bottom: 10px;
}

.modern-nav-user-name {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

.modern-nav-user-role {
    color: rgba(255,255,255,0.7);
    font-size: 12px;
}

/* 现代化顶部导航栏样式 */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1001;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modern-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 20px;
}

.modern-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.modern-sidebar-toggle {
    background: rgba(255,255,255,0.1);
    border: none;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.modern-sidebar-toggle:hover {
    background: rgba(255,255,255,0.2);
    color: #fff;
}

.modern-logo {
    color: #fff;
    font-size: 20px;
    font-weight: bold;
    text-decoration: none;
    transition: color 0.3s ease;
}

.modern-logo:hover {
    color: #3498db;
    text-decoration: none;
}

.modern-version {
    color: rgba(255,255,255,0.8);
    font-size: 14px;
    margin-left: 10px;
}

.modern-header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.modern-customer-code {
    color: rgba(255,255,255,0.9);
    font-size: 14px;
}

.modern-code-value {
    background: rgba(255,255,255,0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    color: #fff;
}

/* 现代化用户菜单 */
.modern-user-menu {
    position: relative;
}

.modern-user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modern-user-info:hover {
    background: rgba(255,255,255,0.1);
}

.modern-user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.modern-user-menu:hover .modern-user-dropdown,
.modern-user-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

.modern-dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.modern-dropdown-item:last-child {
    border-bottom: none;
}

.modern-dropdown-item:hover {
    background: #f8f9fa;
    color: #2c3e50;
    text-decoration: none;
}

/* 现代化主内容区域 */
.modern-main-content {
    margin-left: 250px;
    margin-top: 50px;
    transition: margin-left 0.3s ease;
    height: calc(100vh - 50px);
    background: #f8f9fa;
}

.modern-main-content.sidebar-collapsed {
    margin-left: 60px;
}

.modern-tabs-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modern-tabs-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 50px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.modern-tabs-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2px;
}

.modern-tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
    position: relative;
}

.modern-tab-item.active {
    background: #fff;
    color: #2c3e50;
    border-color: #e9ecef;
    box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
}

.modern-tab-item:hover:not(.active) {
    background: #e9ecef;
    color: #495057;
}

.modern-tabs-actions {
    display: flex;
    align-items: center;
}

.modern-action-dropdown {
    position: relative;
}

.modern-action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-action-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.modern-action-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.modern-action-dropdown:hover .modern-action-menu,
.modern-action-menu.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
}

.modern-action-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 14px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f0f0f0;
}

.modern-action-item:last-child {
    border-bottom: none;
}

.modern-action-item:hover {
    background: #f8f9fa;
    color: #2c3e50;
    text-decoration: none;
}

.modern-tabs-content {
    flex: 1;
    background: #fff;
    position: relative;
}

.modern-tab-pane {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modern-tab-pane.active {
    opacity: 1;
    visibility: visible;
}

.modern-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background: #fff;
}

/* 响应式设计 - 现代化组件 */
@media (max-width: 768px) {
    .modern-main-content {
        margin-left: 0;
    }

    .modern-header-left {
        gap: 10px;
    }

    .modern-version {
        display: none;
    }

    .modern-customer-code {
        display: none;
    }

    .modern-tabs-header {
        padding: 0 10px;
    }

    .modern-tab-item {
        padding: 8px 12px;
        font-size: 14px;
    }

    .modern-action-btn {
        padding: 6px 10px;
        font-size: 14px;
    }
}
