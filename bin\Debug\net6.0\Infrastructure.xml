<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Infrastructure</name>
    </assembly>
    <members>
        <member name="T:Infrastructure.AppSetting">
            <summary>
            配置项
            </summary>
        </member>
        <member name="P:Infrastructure.AppSetting.SSOPassport">
            <summary>
            SSO地址
            </summary>
        </member>
        <member name="P:Infrastructure.AppSetting.Version">
            <summary>
            版本信息
            如果为demo,则屏蔽Post请求
            </summary>
        </member>
        <member name="P:Infrastructure.AppSetting.DbTypes">
            <summary>
            数据库类型 SqlServer、MySql
            </summary>
        </member>
        <member name="P:Infrastructure.AppSetting.UploadPath">
            <summary> 附件上传路径</summary>
        </member>
        <member name="P:Infrastructure.AppSetting.RedisConf">
            <summary>
            Redis服务器配置
            </summary>
        </member>
        <member name="M:Infrastructure.AutoMapperExt.MapTo``1(System.Object)">
            <summary>
             类型映射
            </summary>
        </member>
        <member name="M:Infrastructure.AutoMapperExt.MapToList``1(System.Collections.IEnumerable)">
            <summary>
            集合列表类型映射
            </summary>
        </member>
        <member name="M:Infrastructure.AutoMapperExt.MapToList``2(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            集合列表类型映射
            </summary>
        </member>
        <member name="M:Infrastructure.AutoMapperExt.MapTo``2(``0,``1)">
            <summary>
            类型映射
            </summary>
        </member>
        <member name="M:Infrastructure.Cache.EnyimMemcachedContext.Get``1(System.String)">
            <summary>
            
            </summary>
            <param name="key"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="T:Infrastructure.Cache.ICacheContext">
            <summary>
            缓存接口
            </summary>
        </member>
        <member name="M:Infrastructure.Cache.ICacheContext.Get``1(System.String)">
            <summary>
            获取缓存项
            </summary>
            <typeparam name="T">缓存对象类型</typeparam>
            <param name="key">键</param>
            <returns>缓存对象</returns>
        </member>
        <member name="M:Infrastructure.Cache.ICacheContext.Set``1(System.String,``0,System.DateTime)">
            <summary>
            设置缓存项
            </summary>
            <typeparam name="T">缓存对象类型</typeparam>
            <param name="key">键</param>
            <param name="t">缓存对象</param>
            <returns>true成功,false失败</returns>
        </member>
        <member name="M:Infrastructure.Cache.ICacheContext.Remove(System.String)">
            <summary>
            移除一个缓存项
            </summary>
            <param name="key">缓存项key</param>
            <returns>true成功,false失败</returns>
        </member>
        <member name="T:Infrastructure.Cache.RedisCacheContext">
            <summary>
            缓存redis实现
            </summary>
        </member>
        <member name="T:Infrastructure.Const.JobStatus">
            <summary>
            定时任务状态
            </summary>
        </member>
        <member name="F:Infrastructure.Const.JobStatus.NotRun">
            <summary>
            未启动
            </summary>
        </member>
        <member name="F:Infrastructure.Const.JobStatus.Running">
            <summary>
            正在运行
            </summary>
        </member>
        <member name="T:Infrastructure.Database.DbDataConvertExtensions">
            <summary>
            数据库数据转换拓展
            </summary>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``1(System.Data.DataTable)">
            <summary>
            将 DataTable 转 List 集合
            </summary>
            <typeparam name="T">返回值类型</typeparam>
            <param name="dataTable">DataTable</param>
            <returns>List{T}</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToListAsync``1(System.Data.DataTable)">
            <summary>
            将 DataTable 转 List 集合
            </summary>
            <typeparam name="T">返回值类型</typeparam>
            <param name="dataTable">DataTable</param>
            <returns>List{T}</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``1(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``2(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``3(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <typeparam name="T3">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``4(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <typeparam name="T3">元组元素类型</typeparam>
            <typeparam name="T4">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``5(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <typeparam name="T3">元组元素类型</typeparam>
            <typeparam name="T4">元组元素类型</typeparam>
            <typeparam name="T5">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``6(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <typeparam name="T3">元组元素类型</typeparam>
            <typeparam name="T4">元组元素类型</typeparam>
            <typeparam name="T5">元组元素类型</typeparam>
            <typeparam name="T6">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``7(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <typeparam name="T3">元组元素类型</typeparam>
            <typeparam name="T4">元组元素类型</typeparam>
            <typeparam name="T5">元组元素类型</typeparam>
            <typeparam name="T6">元组元素类型</typeparam>
            <typeparam name="T7">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList``8(System.Data.DataSet)">
            <summary>
            将 DataSet 转 元组
            </summary>
            <typeparam name="T1">元组元素类型</typeparam>
            <typeparam name="T2">元组元素类型</typeparam>
            <typeparam name="T3">元组元素类型</typeparam>
            <typeparam name="T4">元组元素类型</typeparam>
            <typeparam name="T5">元组元素类型</typeparam>
            <typeparam name="T6">元组元素类型</typeparam>
            <typeparam name="T7">元组元素类型</typeparam>
            <typeparam name="T8">元组元素类型</typeparam>
            <param name="dataSet">DataSet</param>
            <returns>元组类型</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList(System.Data.DataSet,System.Type[])">
            <summary>
            将 DataSet 转 特定类型
            </summary>
            <param name="dataSet">DataSet</param>
            <param name="returnTypes">特定类型集合</param>
            <returns>List{object}</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToListAsync(System.Data.DataSet,System.Type[])">
            <summary>
            将 DataSet 转 特定类型
            </summary>
            <param name="dataSet">DataSet</param>
            <param name="returnTypes">特定类型集合</param>
            <returns>object</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToList(System.Data.DataTable,System.Type)">
            <summary>
            将 DataTable 转 特定类型
            </summary>
            <param name="dataTable">DataTable</param>
            <param name="returnType">返回值类型</param>
            <returns>object</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToListAsync(System.Data.DataTable,System.Type)">
            <summary>
            将 DataTable 转 特定类型
            </summary>
            <param name="dataTable">DataTable</param>
            <param name="returnType">返回值类型</param>
            <returns>object</returns>
        </member>
        <member name="M:Infrastructure.Database.DbDataConvertExtensions.ToValueTuple(System.Data.DataSet,System.Type)">
            <summary>
            处理元组类型返回值
            </summary>
            <param name="dataSet">数据集</param>
            <param name="tupleType">返回值类型</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.DynamicLinq.GenerateBody``1(System.Linq.Expressions.ParameterExpression,Infrastructure.Filter)">
            <summary>
            创建linq表达示的body部分
            </summary>
        </member>
        <member name="M:Infrastructure.DynamicLinq.GenerateLambda(System.Linq.Expressions.ParameterExpression,System.Linq.Expressions.Expression)">
            <summary>
            创建完整的lambda
            </summary>
        </member>
        <member name="M:Infrastructure.DynamicLinq.GenerateFilter``1(System.Linq.IQueryable{``0},System.String,Infrastructure.FilterGroup)">
            <summary>
            转换FilterGroup为Lambda表达式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="query"></param>
            <param name="parametername"></param>
            <param name="filterGroup"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.DynamicLinq.GenerateFilter``1(SqlSugar.ISugarQueryable{``0},System.String,Infrastructure.FilterGroup)">
            <summary>
            转换FilterGroup为Lambda表达式
            </summary>
            <param name="query"></param>
            <param name="parametername"></param>
            <param name="filterGroup"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.DynamicLinq.ConvertGroup``1(Infrastructure.FilterGroup,System.Linq.Expressions.ParameterExpression)">
            <summary>
            转换filtergroup为表达式
            </summary>
            <param name="filterGroup"></param>
            <param name="param"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.DynamicLinq.ConvertGroup``1(Infrastructure.FilterGroup[],System.Linq.Expressions.ParameterExpression,System.String)">
            <summary>
            转换FilterGroup[]为表达式，不管FilterGroup里面的Filters
            </summary>
            <param name="groups"></param>
            <param name="param"></param>
            <param name="operation"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.DynamicLinq.ConvertFilters``1(Infrastructure.Filter[],System.Linq.Expressions.ParameterExpression,System.String)">
            <summary>
            转换Filter数组为表达式
            </summary>
            <param name="filters"></param>
            <param name="param"></param>
            <param name="operation"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Encryption.Encrypt(System.String)">
            <summary>
            加密
            </summary>
            <param name="encryptString"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Encryption.Decrypt(System.String)">
            <summary>
            解密
            </summary>
            <param name="decryptString"></param>
            <returns></returns>
        </member>
        <member name="T:Infrastructure.Extensions.AutofacManager.AutofacContainerModule">
            <summary>
            提供全局静态获取服务的能力。
            <para>例：AutofacContainerModule.GetService&lt;IPathProvider&gt;()</para>
            </summary>
        </member>
        <member name="T:Infrastructure.Extensions.AutofacManager.IDependency">
            <summary>
            所有AutoFac注入的基类
            </summary>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.String2Json(System.String)">
            <summary>
            过滤特殊字符
            </summary>
            <param name="s">字符串</param>
            <returns>json字符串</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.StringFormat(System.String,System.Type)">
            <summary>
            格式化字符型、日期型、布尔型
            </summary>
            <param name="str"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ListToJson``1(System.Collections.Generic.IList{``0})">
            <summary>
            list转换为Json
            </summary>
            <typeparam name="T"></typeparam>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ListToJson``1(System.Collections.Generic.IList{``0},System.String)">
            <summary>
            list转换为json
            </summary>
            <typeparam name="T"></typeparam>
            <param name="list"></param>
            <param name="p"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ToJson(System.Object)">
            <summary>
            对象转换为json
            </summary>
            <param name="jsonObject">json对象</param>
            <returns>json字符串</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ToJson(System.Collections.IEnumerable)">
            <summary>
            对象集合转换为json
            </summary>
            <param name="array">对象集合</param>
            <returns>json字符串</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ToArrayString(System.Collections.IEnumerable)">
            <summary>    
            普通集合转换Json   
            </summary>   
            <param name="array">集合对象</param> 
            <returns>Json字符串</returns>  
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ToJson(System.Data.DataSet)">
            <summary>    
            DataSet转换为Json   
            </summary>    
            <param name="dataSet">DataSet对象</param>   
            <returns>Json字符串</returns>    
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ToJson(System.Data.DataTable)">
            <summary>     
            Datatable转换为Json     
            </summary>    
            <param name="table">Datatable对象</param>     
            <returns>Json字符串</returns>     
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ToJson(System.Data.DataTable,System.String)">
            <summary>    
            DataTable转换为Json     
            </summary>    
        </member>
        <member name="M:Infrastructure.Extensions.ConvertJsonExtension.ReaderJson(System.Data.IDataReader)">
            <summary>     
            DataReader转换为Json     
            </summary>     
            <param name="dataReader">DataReader对象</param>     
            <returns>Json字符串</returns>  
        </member>
        <member name="M:Infrastructure.Extensions.DateTimeExtension.UnixTicks(System.DateTime)">
            <summary>
            实现由C# 的时间到 Javascript 的时间的转换
            returns the number of milliseconds since Jan 1, 1970 (useful for converting C# dates to JS dates)  
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.DateTimeExtension.ConvertTime(System.Int64)">
            <summary>
            将毫秒值转成 C#  DateTime 类型
            </summary>
            <param name="time"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetExpressionProperty``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            获取对象里指定成员名称
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="properties"> <![CDATA[格式 Expression<Func<entityt, object>> exp = x => new { x.字段1, x.字段2 };或x=>x.Name]]></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetAtrrNames(System.Type)">
            <summary>
            获取所有字段的名称 
            </summary>
            <param name="typeinfo"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetColumnType(System.Reflection.PropertyInfo,System.Boolean)">
            <summary>
            返回属性的字段及数据库类型
            </summary>
            <param name="property"></param>
            <param name="lenght">是否包括后字段具体长度:nvarchar(100)</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetArraySql(System.Object[],Infrastructure.Extensions.FieldType)">
            <summary>
            
            </summary>
            <param name="array">将数组转换成sql语句</param>
            <param name="fieldType">指定FieldType数据库字段类型</param>
            <param name="sql"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetArraySql(System.Object[],Infrastructure.Extensions.FieldType,System.String)">
             <summary>
            <param name="sql">要执行的sql语句如：通过EntityToSqlTempName.Temp_Insert0.ToString()字符串占位，生成的的sql语句会把EntityToSqlTempName.Temp_Insert0.ToString()替换成生成的sql临时表数据
                string sql = " ;DELETE FROM " + typeEntity.Name + " where " + typeEntity.GetKeyName() +
                  " in (select * from " + EntityToSqlTempName.Temp_Insert0.ToString() + ")";
             </param>
             </summary>
             <param name="array"></param>
             <param name="fieldType">指定生成的数组值的类型</param>
             <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetFieldType(System.Type)">
            <summary>
            根据实体获取key的类型，用于update或del操作
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetDataTableSql(System.Data.DataTable)">
             <summary>
            此方法适用于数据量少,只有几列数据，不超过1W行，或几十列数据不超过1000行的情况下使用
             大批量的数据考虑其他方式
             將datatable生成sql語句，替換datatable作為參數傳入存儲過程
             </summary>
             <param name="table"></param>
             <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetKeyName(System.Reflection.PropertyInfo[],System.Boolean)">
            <summary>
            获取key列名
            </summary>
            <param name="properties"></param>
            <param name="keyType">true获取key对应类型,false返回对象Key的名称</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetKeyProperty(System.Type)">
            <summary>
            获取主键字段
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ContainsCustomAttributes(System.Reflection.PropertyInfo,System.Type)">
            <summary>
            判断是否包含某个属性
            <para>public string MO {get; set; }包含Editable</para>
            </summary>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetTypeCustomAttributes(System.Reflection.PropertyInfo,System.Type,System.Boolean@)">
            <summary>
            获取PropertyInfo指定属性
            </summary>
            <param name="propertyInfo"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidationEntityList``1(System.Collections.Generic.List{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            验证集合的属性
            </summary>
            <typeparam name="T"></typeparam>
            <param name="entityList"></param>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidationEntity``1(``0,System.Linq.Expressions.Expression{System.Func{``0,System.Object}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            指定需要验证的字段
            </summary>
            <typeparam name="T"></typeparam>
            <param name="entity"></param>
            <param name="expression">对指定属性进行验证x=>{x.Name,x.Size}</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidationEntity``1(``0,System.String[],System.String[])">
            <summary>
            specificProperties=null并且validateProperties=null，对所有属性验证，只验证其是否合法，不验证是否为空(除属性标识指定了不能为空外)
            specificProperties!=null，对指定属性校验，并且都必须有值
            null并且validateProperties!=null，对指定属性校验，不判断是否有值
            </summary>
            <typeparam name="T"></typeparam>
            <param name="entity"></param>
            <param name="specificProperties">验证指定的属性，并且非空判断</param>
            <param name="validateProperties">验证指定属性，只对字段合法性判断，不验证是否为空</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetSqlDbType(System.Reflection.PropertyInfo)">
            <summary>
            获取数据库类型，不带长度，如varchar(100),只返回的varchar
            </summary>
            <param name="propertyInfo"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidationValueForDbType(System.Reflection.PropertyInfo,System.Object[])">
            <summary>
            验证数据库字段类型与值是否正确，
            </summary>
            <param name="propertyInfo">propertyInfo为当字段，当前字段必须有ColumnAttribute属性,
            如字段:标识为数据库int类型[Column(TypeName="int")]  public int Id { get; set; }
            如果是小数float或Decimal必须对propertyInfo字段加DisplayFormatAttribute属性
            </param>
            <param name="value"></param>
            <returns>IEnumerable&lt;(bool, string, object)&gt; bool成否校验成功,string校验失败信息,object,当前校验的值</returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidationVal(System.String,System.Object,System.Reflection.PropertyInfo)">
            <summary>
            验证数据库字段类型与值是否正确，
            </summary>
            <param name="dbType">数据库字段类型(如varchar,nvarchar,decimal,不要带后面长度如:varchar(50))</param>
            <param name="value">值</param>
            <param name="propertyInfo">要验证的类的属性，若不为null，则会判断字符串的长度是否正确</param>
            <returns>(bool, string, object)bool成否校验成功,string校验失败信息,object,当前校验的值</returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidationProperty(System.Reflection.PropertyInfo,System.Object,System.Boolean)">
            <summary>
            验证每个属性的值是否正确
            </summary>
            <param name="propertyInfo"></param>
            <param name="objectVal">属性的值</param>
            <param name="required">是否指定当前属性必须有值</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetTypeCustomAttributes(System.Reflection.MemberInfo,System.Type)">
            <summary>
            获取属性的指定属性
            </summary>
            <param name="propertyInfo"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetTypeCustomAttributes(System.Type,System.Type)">
            <summary>
            获取类的指定属性
            </summary>
            <param name="propertyInfo"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetTypeCustomValues``1(System.Reflection.MemberInfo,System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            获取类的多个指定属性的值
            </summary>
            <param name="member">当前类</param>
            <param name="type">指定的类</param>
            <param name="expression">指定属性的值 格式 Expression&lt;Func&lt;entityt, object&gt;&gt; exp = x =&gt; new { x.字段1, x.字段2 };</param>
            <returns>返回的是字段+value</returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.GetTypeCustomValue``1(System.Reflection.MemberInfo,System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            获取类的单个指定属性的值(只会返回第一个属性的值)
            </summary>
            <param name="member">当前类</param>
            <param name="type">指定的类</param>
            <param name="expression">指定属性的值  格式 Expression&lt;Func&lt;entityt, object&gt;&gt; exp = x =&gt; new { x.字段1, x.字段2 };</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidateDicInEntity(System.Type,System.Collections.Generic.Dictionary{System.String,System.Object},System.Boolean,System.String[])">
            <summary>
            判断hash的列是否为对应的实体，并且值是否有效
            </summary>
            <param name="typeinfo"></param>
            <param name="dic"></param>
            <param name="removeNotContains">移除不存在字段</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.ValidateDicInEntity(System.Type,System.Collections.Generic.Dictionary{System.String,System.Object},System.Reflection.PropertyInfo[],System.Boolean,System.Boolean,System.String[])">
            <summary>
            判断hash的列是否为对应的实体，并且值是否有效
            </summary>
            <param name="typeinfo"></param>
            <param name="dic"></param>
            <param name="removeNotContains">移除不存在字段</param>
            <param name="removerKey">移除主键</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.MapToObject``2(``0,System.Linq.Expressions.Expression{System.Func{``1,System.Object}},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            将数据源映射到新的数据中,目前只支持List&lt;TSource&gt;映射到List&lt;TResult&gt;或TSource映射到TResult
            目前只支持Dictionary或实体类型
            </summary>
            <typeparam name="TSource"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="source"></param>
            <param name="resultExpression">只映射返回对象的指定字段,若为null则默认为全部字段</param>
            <param name="sourceExpression">只映射数据源对象的指定字段,若为null则默认为全部字段</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.EntityProperties.MapValueToEntity``2(``0,``1,System.Linq.Expressions.Expression{System.Func{``1,System.Object}})">
            <summary>
            将一个实体的赋到另一个实体上,应用场景：
            两个实体，a a1= new a();b b1= new b();  a1.P=b1.P; a1.Name=b1.Name;
            </summary>
            <typeparam name="TSource"></typeparam>
            <typeparam name="TResult"></typeparam>
            <param name="source"></param>
            <param name="result"></param>
            <param name="expression">指定对需要的字段赋值,格式x=>new {x.Name,x.P},返回的结果只会对Name与P赋值</param>
        </member>
        <member name="T:Infrastructure.Extensions.GenericExtension">
            <summary>
            泛型扩展
            </summary>
        </member>
        <member name="M:Infrastructure.Extensions.GenericExtension.ToDictionary``1(``0,System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            将实体指定的字段写入字典
            </summary>
            <typeparam name="T"></typeparam>
            <param name="t"></param>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.TakePage``1(System.Linq.IQueryable{``0},System.Int32,System.Int32)">
            <summary>
            分页查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="queryable"></param>
            <param name="page"></param>
            <param name="size"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.TakeOrderByPage``1(System.Linq.IQueryable{``0},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.Dictionary{System.Object,Infrastructure.Const.QueryOrderBy}}})">
            <summary>
            分页查询
            </summary>
            <typeparam name="T"></typeparam>
            <param name="queryable"></param>
            <param name="page"></param>
            <param name="size"></param>
            <param name="orderBy"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.True``1">
            <summary>
            创建lambda表达式：p=>true
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.False``1">
            <summary>
            创建lambda表达式：p=>false
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.GetExpression``2(System.String)">
            <summary>
            创建lambda表达式：p=>p.propertyName
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TKey"></typeparam>
            <param name="sort"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.GetFun``2(System.String)">
            <summary>
            创建委托有返回值的表达式：p=>p.propertyName
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TKey"></typeparam>
            <param name="sort"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.GetExpression``2(System.String,System.Linq.Expressions.ParameterExpression)" -->
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.GetExpression``1(System.String)" -->
        <member name="M:Infrastructure.Extensions.LambdaExtensions.CreateExpression``1(System.String,System.Object,Infrastructure.Const.LinqExpressionType)">
            <summary>
            
            </summary>
            <typeparam name="T"></typeparam>
            <param name="propertyName">字段名</param>
            <param name="propertyValue">表达式的值</param>
            <param name="expressionType">创建表达式的类型,如:p=>p.propertyName != propertyValue 
            p=>p.propertyName.Contains(propertyValue)</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.CreateExpression``1(System.String,System.Object,System.Linq.Expressions.ParameterExpression,Infrastructure.Const.LinqExpressionType)">
            <summary>
            
            </summary>
            <typeparam name="T"></typeparam>
            <param name="propertyName">字段名</param>
            <param name="propertyValue">表达式的值</param>
            <param name="expressionType">创建表达式的类型,如:p=>p.propertyName != propertyValue 
            p=>p.propertyName.Contains(propertyValue)</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.GetExpressionToPair``1(System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.Dictionary{System.Object,Infrastructure.Const.QueryOrderBy}}})" -->
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.GetExpressionToDic``1(System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.Dictionary{System.Object,Infrastructure.Const.QueryOrderBy}}})" -->
        <member name="M:Infrastructure.Extensions.LambdaExtensions.GetIQueryableOrderBy``1(System.Linq.IQueryable{``0},System.Collections.Generic.Dictionary{System.String,Infrastructure.Const.QueryOrderBy})">
            <summary>
            解析多字段排序
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="queryable"></param>
            <param name="orderBySelector">string=排序的字段,bool=true降序/false升序</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.GetExpressionToArray``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}})" -->
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.And``1(System.Collections.Generic.List{Infrastructure.Extensions.ExpressionParameters})" -->
        <member name="M:Infrastructure.Extensions.LambdaExtensions.Or``1(System.Collections.Generic.List{Infrastructure.Extensions.ExpressionParameters})">
            <summary>
            同上面and用法相同
            </summary>
            <typeparam name="T"></typeparam>
            <param name="listExpress"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.And``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            https://blogs.msdn.microsoft.com/meek/2008/05/02/linq-to-entities-combining-predicates/
            表达式合并(合并生产的sql语句有性能问题)
            合并两个where条件，如：多个查询条件时，判断条件成立才where
            </summary>
            <typeparam name="T"></typeparam>
            <param name="first"></param>
            <param name="second"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Infrastructure.Extensions.LambdaExtensions.CreateMemberInitExpression``2(System.Type)" -->
        <member name="M:Infrastructure.Extensions.LambdaExtensions.GetGenericProperties(System.Type)">
            <summary>
            属性判断待完
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.LambdaExtensions.GetGenericProperties(System.Collections.Generic.IEnumerable{System.Reflection.PropertyInfo})">
            <summary>
            属性判断待完
            </summary>
            <param name="properties"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ReaderToDictionaryList(System.Data.IDataReader)">
            <summary>
            IDataReader转换成DictionaryList
            </summary>
            <param name="Reader"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ReaderToList``1(System.Data.IDataReader)">
            <summary>
            IDataReader转换成List
            </summary>
            <typeparam name="T"></typeparam>
            <param name="Reader"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToDataSet``1(System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            将集合转换为数据集。
            </summary>
            <typeparam name="T">转换的元素类型。</typeparam>
            <param name="list">集合。</param>
            <param name="generic">是否生成泛型数据集。</param>
            <returns>数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToDataSet(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            将集合转换为数据集。
            </summary>
            <param name="list">集合。</param>
            <param name="generic">是否生成泛型数据集。</param>
            <returns>数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToDataSet``1(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            将集合转换为数据集。
            </summary>
            <typeparam name="T">转换的元素类型。</typeparam>
            <param name="list">集合。</param>
            <param name="generic">是否生成泛型数据集。</param>
            <returns>数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToListSet``1(``0,System.Boolean)">
            <summary>
            将实例转换为集合数据集。
            </summary>
            <typeparam name="T">实例类型。</typeparam>
            <param name="o">实例。</param>
            <param name="generic">是否生成泛型数据集。</param>
            <returns>数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToXmlDocument``1(``0)">
            <summary>
            将可序列化实例转换为XmlDocument。
            </summary>
            <typeparam name="T">实例类型。</typeparam>
            <param name="o">实例。</param>
            <returns>XmlDocument。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ListToDataSet(System.Collections.IEnumerable,System.Type,System.Boolean)">
            <summary>
            将集合转换为数据集。
            </summary>
            <param name="list">集合。</param>
            <param name="t">转换的元素类型。</param>
            <param name="generic">是否生成泛型数据集。</param>
            <returns>转换后的数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ListToDataSet``1(System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            将集合转换为数据集。
            </summary>
            <typeparam name="T">转换的元素类型。</typeparam>
            <param name="list">集合。</param>
            <param name="generic">是否生成泛型数据集。</param>
            <returns>数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ListToDataSet(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            将集合转换为数据集。
            </summary>
            <param name="list">集合。</param>
            <param name="generic">是否转换为字符串形式。</param>
            <returns>转换后的数据集。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetData(System.Data.DataSet)">
            <summary>
            获取DataSet第一表，第一行，第一列的值。
            </summary>
            <param name="ds">DataSet数据集。</param>
            <returns>值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetData(System.Data.DataTable)">
            <summary>
            获取DataTable第一行，第一列的值。
            </summary>
            <param name="dt">DataTable数据集表。</param>
            <returns>值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetData(System.Data.DataSet,System.String)">
            <summary>
            获取DataSet第一个匹配columnName的值。
            </summary>
            <param name="ds">数据集。</param>
            <param name="columnName">列名。</param>
            <returns>值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetData(System.Data.DataTable,System.String)">
            <summary>
            获取DataTable第一个匹配columnName的值。
            </summary>
            <param name="dt">数据表。</param>
            <param name="columnName">列名。</param>
            <returns>值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToString(System.Object,System.String)">
            <summary>
            将object转换为string类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>string。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToString(System.Nullable{System.DateTime},System.String,System.String)">
            <summary>
            将DateTime?转换为string类型信息。
            </summary>
            <param name="o">DateTime?。</param>
            <param name="format">标准或自定义日期和时间格式的字符串。</param>
            <param name="t">默认值。</param>
            <returns>string。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToString(System.Nullable{System.TimeSpan},System.String,System.String)">
            <summary>
            将TimeSpan?转换为string类型信息。
            </summary>
            <param name="o">TimeSpan?。</param>
            <param name="format">标准或自定义时间格式的字符串。</param>
            <param name="t">默认值。</param>
            <returns>string。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToByte(System.Object,System.Byte)">
            <summary>
            将object转换为byte类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>byte。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToChar(System.Object,System.Char)">
            <summary>
            将object转换为char类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>char。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToInt(System.Object,System.Int32)">
            <summary>
            将object转换为int类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>int。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToDouble(System.Object,System.Double)">
            <summary>
            将object转换为double类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>double。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToDecimal(System.Object,System.Decimal)">
            <summary>
            将object转换为decimal类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>decimal。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToFloat(System.Object,System.Single)">
            <summary>
            将object转换为float类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>float。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToLong(System.Object,System.Int64)">
            <summary>
            将object转换为long类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>long。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToBool(System.Object,System.Boolean)">
            <summary>
            将object转换为bool类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>bool。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToSbyte(System.Object,System.SByte)">
            <summary>
            将object转换为sbyte类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>sbyte。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToShort(System.Object,System.Int16)">
            <summary>
            将object转换为short类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>short。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToUShort(System.Object,System.UInt16)">
            <summary>
            将object转换为ushort类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>ushort。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToULong(System.Object,System.UInt64)">
            <summary>
            将object转换为ulong类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>ulong。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToEnum``1(System.Object,``0)">
            <summary>
            将object转换为Enum[T]类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>Enum[T]。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToDateTime(System.Object,System.DateTime)">
            <summary>
            将object转换为DateTime类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>DateTime。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToTimeSpan(System.Object,System.TimeSpan)">
            <summary>
            将object转换为TimeSpan类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>TimeSpan。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToGuid(System.Object,System.Guid)">
            <summary>
            将object转换为Guid类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>Guid。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetBool(System.String)">
            <summary>
            从object中获取bool类型信息。
            </summary>
            <param name="o">object。</param>
            <returns>bool。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetDecimal(System.Object)">
            <summary>
            从object中获取decimal类型信息。
            </summary>
            <param name="o">object。</param>
            <returns>decimal。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetPositiveNumber(System.Object)">
            <summary>
            从object中获取正数信息。
            </summary>
            <param name="o">object。</param>
            <returns>decimal。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetDateTime1(System.Object)">
            <summary>
            从object中获取DateTime?类型信息。
            </summary>
            <param name="o">object。</param>
            <returns>DateTime?。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetTimeSpan(System.Object)">
            <summary>
            从object中获取TimeSpan?类型信息。
            </summary>
            <param name="o">object。</param>
            <returns>TimeSpan?。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetGuid(System.Object)">
            <summary>
            从object中获取Guid?类型信息。
            </summary>
            <param name="o">object。</param>
            <returns>Guid?。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetSqlDateTime(System.Object,System.DateTime)">
            <summary>
            将object转换为SqlServer中的DateTime?类型信息。
            </summary>
            <param name="o">object。</param>
            <param name="t">默认值。</param>
            <returns>DateTime?。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.Value(System.Xml.Linq.XElement,System.String)">
            <summary>
            读取XElement节点的文本内容。
            </summary>
            <param name="xElement">XElement节点。</param>
            <param name="t">默认值。</param>
            <returns>文本内容。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetValue``2(System.Collections.Generic.IDictionary{``0,``1},``0,``1)">
            <summary>
            获取与指定键相关的值。
            </summary>
            <typeparam name="TKey">键类型。</typeparam>
            <typeparam name="TValue">值类型。</typeparam>
            <param name="dictionary">表示键/值对象的泛型集合。</param>
            <param name="key">键。</param>
            <param name="t">默认值。</param>
            <returns>值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetFirstOrDefaultValue``2(System.Collections.Generic.IDictionary{``0,``1},``0,``1)">
            <summary>
            获取与指定键相关或者第一个的值。
            </summary>
            <typeparam name="TKey">键类型。</typeparam>
            <typeparam name="TValue">值类型。</typeparam>
            <param name="dictionary">表示键/值对象的泛型集合。</param>
            <param name="key">键。</param>
            <param name="t">默认值。</param>
            <returns>值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.Element(System.Xml.Linq.XContainer,System.Xml.Linq.XName,System.Boolean)">
            <summary>
            获取具有指定 System.Xml.Linq.XName 的第一个（按文档顺序）子元素。
            </summary>
            <param name="xContainer">XContainer。</param>
            <param name="xName">要匹配的 System.Xml.Linq.XName。</param>
            <param name="t">是否返回同名默认值。</param>
            <returns>与指定 System.Xml.Linq.XName 匹配的 System.Xml.Linq.XElement，或者为 null。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.Elements(System.Xml.Linq.XContainer,System.Boolean)">
            <summary>
            按文档顺序返回此元素或文档的子元素集合。
            </summary>
            <param name="xContainer">XContainer。</param>
            <param name="t">是否返回非空默认值。</param>
            <returns>System.Xml.Linq.XElement 的按文档顺序包含此System.Xml.Linq.XContainer 的子元素，或者非空默认值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.Elements(System.Xml.Linq.XContainer,System.Xml.Linq.XName,System.Boolean)">
            <summary>
            按文档顺序返回此元素或文档的经过筛选的子元素集合。集合中只包括具有匹配 System.Xml.Linq.XName 的元素。
            </summary>
            <param name="xContainer">XContainer。</param>
            <param name="xName">要匹配的 System.Xml.Linq.XName。</param>
            <param name="t">是否返回非空默认值。</param>
            <returns>System.Xml.Linq.XElement 的按文档顺序包含具有匹配System.Xml.Linq.XName 的 System.Xml.Linq.XContainer 的子级，或者非空默认值。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.RemoveHTMLTags(System.String)">
            <summary>
            删除html标签。
            </summary>
            <param name="html">输入的字符串。</param>
            <returns>没有html标签的字符串。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToFileName(System.String)">
            <summary>
            字符串转换为文件名。
            </summary>
            <param name="s">字符串。</param>
            <returns>文件名。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.DefaultStringIfEmpty(System.String,System.String[])">
            <summary>
            获取默认非空字符串。
            </summary>
            <param name="s">首选默认非空字符串。</param>
            <param name="args">依次非空字符串可选项。</param>
            <returns>默认非空字符串。若无可选项则返回string.Empty。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToUrlEncodeString(System.String,System.Text.RegularExpressions.Regex,System.Text.Encoding)">
            <summary>
            对 URL 字符串进行编码。
            </summary>
            <param name="s">要编码的文本。</param>
            <param name="regex">匹配要编码的文本。</param>
            <param name="encoding">指定编码方案的 System.Text.Encoding 对象。</param>
            <returns>一个已编码的字符串。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToUrlEncodeString(System.String,System.String,System.Text.Encoding)">
            <summary>
            对 URL 字符串进行编码。
            </summary>
            <param name="s">要编码的文本。</param>
            <param name="regex">匹配要编码的文本。</param>
            <param name="encoding">指定编码方案的 System.Text.Encoding 对象。</param>
            <returns>一个已编码的字符串。</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToUnixTimeStamp(System.DateTime)">
            <summary>
            将日期转换为UNIX时间戳字符串
            </summary>
            <param name="date"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsMobile(System.String)">
            <summary>
            判断当前字符串是否是移动电话号码
            </summary>
            <param name="mobile"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsEmail(System.String)">
            <summary>
            判断当前字符串是否为邮箱
            </summary>
            <param name="email"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ConvertToDateTime(System.DateTimeOffset)">
            <summary>
            将 DateTimeOffset 转换成 DateTime
            </summary>
            <param name="dateTime"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ConvertToDateTimeOffset(System.DateTime)">
            <summary>
            将 DateTime 转换成 DateTimeOffset
            </summary>
            <param name="dateTime"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsRichPrimitive(System.Type)">
            <summary>
            判断是否是富基元类型
            </summary>
            <param name="type">类型</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.AddOrUpdate``1(System.Collections.Generic.Dictionary{System.String,``0},System.Collections.Generic.Dictionary{System.String,``0})">
            <summary>
            合并两个字典
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dic">字典</param>
            <param name="newDic">新字典</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsValueTuple(System.Type)">
            <summary>
            判断是否是元组类型
            </summary>
            <param name="type">类型</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsAsync(System.Reflection.MethodInfo)">
            <summary>
            判断方法是否是异步
            </summary>
            <param name="method">方法</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsAsync(System.Type)">
            <summary>
            判断类型是否是异步类型
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.HasImplementedRawGeneric(System.Type,System.Type)">
            <summary>
            判断类型是否实现某个泛型
            </summary>
            <param name="type">类型</param>
            <param name="generic">泛型类型</param>
            <returns>bool</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.IsAnonymous(System.Object)">
            <summary>
            判断是否是匿名类型
            </summary>
            <param name="obj">对象</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetAncestorTypes(System.Type)">
            <summary>
            获取所有祖先类型
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.GetRealReturnType(System.Reflection.MethodInfo)">
            <summary>
            获取方法真实返回类型
            </summary>
            <param name="method"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ToTitleCase(System.String)">
            <summary>
            首字母大写
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ChangeType``1(System.Object)">
            <summary>
            将一个对象转换为指定类型
            </summary>
            <typeparam name="T"></typeparam>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ObjectExtension.ChangeType(System.Object,System.Type)">
            <summary>
            将一个对象转换为指定类型
            </summary>
            <param name="obj">待转换的对象</param>
            <param name="type">目标类型</param>
            <returns>转换后的对象</returns>
        </member>
        <member name="T:Infrastructure.Extensions.Flag">
            <summary>
            标记。
            </summary>
        </member>
        <member name="F:Infrastructure.Extensions.Flag.Default">
            <summary>
            默认。
            </summary>
        </member>
        <member name="F:Infrastructure.Extensions.Flag.True">
            <summary>
            真。
            </summary>
        </member>
        <member name="F:Infrastructure.Extensions.Flag.False">
            <summary>
            假。
            </summary>
        </member>
        <member name="M:Infrastructure.Extensions.SecurityEncDecryptExtensions.EncryptDES(System.String,System.String)">
            <summary>
            DES加密字符串
            </summary>
            <param name="encryptString">待加密的字符串</param>
            <param name="encryptKey">加密密钥,要求为16位</param>
            <returns>加密成功返回加密后的字符串，失败返回源串</returns>
        </member>
        <member name="M:Infrastructure.Extensions.SecurityEncDecryptExtensions.DecryptDES(System.String,System.String)">
            <summary>
            DES解密字符串
            </summary>
            <param name="decryptString">待解密的字符串</param>
            <param name="decryptKey">解密密钥,要求为16位,和加密密钥相同</param>
            <returns>解密成功返回解密后的字符串，失败返源串</returns>
        </member>
        <member name="M:Infrastructure.Extensions.ServerExtension.MapPath(System.String)">
            <summary>
            返回的路径后面不带/，拼接时需要自己加上/
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.ServerExtension.MapPath(System.String,System.Boolean)">
            <summary>
            
            </summary>
            <param name="path"></param>
            <param name="rootPath">获取wwwroot路径</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.ReplacePath(System.String)">
            <summary>
            自动调整windows/linux下面文件目录斜杠的处理
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.ToCamelCase(System.String)">
            <summary>
            把一个字符串转成驼峰规则的字符串
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GetTimeStamp(System.DateTime)">
            <summary>
            获取时间戳 
            </summary>
            <param name="dateTime"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GetTimeSpmpToDate(System.Object)">
            <summary>
            时间戳转换成日期
            </summary>
            <param name="timeStamp"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.IsPhoneNo(System.String)">
            <summary>
            判断是不是正确的手机号码
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.IsNumber(System.String,System.String)">
            <summary>
            根据传入格式判断是否为小数
            </summary>
            <param name="str"></param>
            <param name="formatString">18,5</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.IsNumber(System.String,System.Int32,System.Int32)">
            <summary>
            判断一个字符串是否为合法数字(指定整数位数和小数位数)
            </summary>
            <param name="str">字符串</param>
            <param name="precision">整数位数</param>
            <param name="scale">小数位数</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GetLong(System.Object)">
            <summary>
            获取 object 中的枚举值
            </summary>
            <param name="str"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GetFloat(System.Object)">
            <summary>
            获取 object 中的 float
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GetDecimal(System.Object)">
            <summary>
            获取 object 中的 decimal
            </summary>
            <param name="str"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GetDynamic(System.Object)">
            <summary>
            获取 object 中的 decimal
            </summary>
            <param name="str"></param>
            <returns></returns>
            <remarks></remarks>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.ReplaceWhitespace(System.String,System.String)">
            <summary>
                替换空格字符
            </summary>
            <param name="input"></param>
            <param name="replacement">替换为该字符</param>
            <returns>替换后的字符串</returns>
        </member>
        <member name="M:Infrastructure.Extensions.StringExtension.GenerateRandomNumber(System.Int32)">
            <summary>
            生成指定长度的随机数
            </summary>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="P:Infrastructure.FilterGroup.Operation">
            <summary>
            or /and
            </summary>
        </member>
        <member name="M:Infrastructure.GenerateId.GenerateLong">
            <summary>
            生成一个长整型，可以转成19字节长的字符串
            </summary>
            <returns>System.Int64.</returns>
        </member>
        <member name="M:Infrastructure.GenerateId.GenerateStr">
            <summary>
            生成16个字节长度的数据与英文组合串
            </summary>
        </member>
        <member name="M:Infrastructure.GenerateId.ShortStr">
            <summary>
            创建11位的英文与数字组合
            </summary>
            <returns>System.String.</returns>
        </member>
        <member name="M:Infrastructure.GenerateId.GenerateOrderNumber">
            <summary>
            唯一订单号生成
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.GenerateId.NextRandom(System.Int32,System.Int32)">
            <summary>
            参考：msdn上的RNGCryptoServiceProvider例子
            </summary>
            <param name="numSeeds"></param>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.GenerateId.Convert(System.Int64)">
            <summary>
            10进制转换为62进制
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Infrastructure.Helpers.CommonHelper">
            <summary>
            常用公共类
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.TimerStart">
            <summary>
            计时器开始
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.TimerEnd(System.Diagnostics.Stopwatch)">
            <summary>
            计时器结束
            </summary>
            <param name="watch"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.RemoveDup(System.String[])">
            <summary>
            删除数组中的重复项
            </summary>
            <param name="values"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.CreateNo">
            <summary>
            自动生成编号  201008251145409865
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.RndNum(System.Int32)">
            <summary>
            生成0-9随机数
            </summary>
            <param name="codeNum">生成长度</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.DelLastComma(System.String)">
            <summary>
            删除最后结尾的一个逗号
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.DelLastChar(System.String,System.String)">
            <summary>
            删除最后结尾的指定字符后的字符
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.CommonHelper.DelLastLength(System.String,System.Int32)">
            <summary>
            删除最后结尾的长度
            </summary>
            <param name="str"></param>
            <param name="Length"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.ReadPageLine(System.String,System.Int32,System.Int32,System.Boolean)">
            <summary>
            通过迭代器读取平面文件行内容(必须是带有\r\n换行的文件,百万行以上的内容读取效率存在问题,适用于100M左右文件，行100W内，超出的会有卡顿)
            </summary>
            <param name="fullPath">文件全路径</param>
            <param name="page">分页页数</param>
            <param name="pageSize">分页大小</param>
            <param name="seekEnd"> 是否最后一行向前读取,默认从前向后读取</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.GetPostfixStr(System.String)">
            <summary>
            取后缀名
            </summary>
            <param name="filename">文件名</param>
            <returns>.gif|.html格式</returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.WriteFile(System.String,System.String,System.String,System.Boolean)">
            <summary>
            
            </summary>
            <param name="path">路径 </param>
            <param name="fileName">文件名</param>
            <param name="content">写入的内容</param>
            <param name="appendToLast">是否将内容添加到未尾,默认不添加</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.FileAdd(System.String,System.String)">
            <summary>
            追加文件
            </summary>
            <param name="Path">文件路径</param>
            <param name="strings">内容</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.FileCoppy(System.String,System.String)">
            <summary>
            拷贝文件
            </summary>
            <param name="OrignFile">原始文件</param>
            <param name="NewFile">新文件路径</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.FileDel(System.String)">
            <summary>
            删除文件
            </summary>
            <param name="Path">路径</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.FileMove(System.String,System.String)">
            <summary>
            移动文件
            </summary>
            <param name="OrignFile">原始路径</param>
            <param name="NewFile">新路径</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.FolderCreate(System.String,System.String)">
            <summary>
            在当前目录下创建目录
            </summary>
            <param name="OrignFolder">当前目录</param>
            <param name="NewFloder">新目录</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.FolderCreate(System.String)">
            <summary>
            创建文件夹
            </summary>
            <param name="Path"></param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.DeleteFolder(System.String)">
            <summary>
            递归删除文件夹目录及文件
            </summary>
            <param name="dir"></param>  
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.CopyDir(System.String,System.String)">
            <summary>
            指定文件夹下面的所有内容copy到目标文件夹下面
            </summary>
            <param name="srcPath">原始路径</param>
            <param name="aimPath">目标文件夹</param>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.GetDirectoryLength(System.String)">
            <summary>
            获取文件夹大小
            </summary>
            <param name="dirPath">文件夹路径</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.GetFileAttibe(System.String)">
            <summary>
            获取指定文件详细属性
            </summary>
            <param name="filePath">文件详细路径</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.parenthesisMatch(System.String,System.Char,System.Char)">
            <summary>
            括号匹配算法
            </summary>
            <param name="dataStr">原始字符串</param>
            <param name="leftCode">左匹配符号</param>
            <param name="rightCode">右匹配符号</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.FileHelper.RegxAddContentByParenthesis(System.String,System.String)">
            <summary>
            替换内容（正则 + 括号匹配算法）
            </summary>
            <param name="path">文件路径</param>
            <param name="addStr">追加内容</param>
        </member>
        <member name="T:Infrastructure.Helpers.GenericHelpers">
            <summary>
            List转成Tree
            <para>wzw 2016-10-09 19:54:07</para>
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.GenericHelpers.GenerateTree``2(System.Collections.Generic.IEnumerable{``0},System.Func{``0,``1},System.Func{``0,``1},``1)">
            <summary>
            Generates tree of items from item list
            </summary>
            
            <typeparam name="T">Type of item in collection</typeparam>
            <typeparam name="K">Type of parent_id</typeparam>
            
            <param name="collection">Collection of items</param>
            <param name="idSelector">Function extracting item's id</param>
            <param name="parentIdSelector">Function extracting item's parent_id</param>
            <param name="rootId">Root element id</param>
            
            <returns>Tree of items</returns>
        </member>
        <member name="M:Infrastructure.Helpers.GenericHelpers.ArrayToString(System.Object,System.String)">
            <summary>
            把数组转为逗号连接的字符串
            </summary>
            <param name="data"></param>
            <param name="Str"></param>
            <returns></returns>
        </member>
        <member name="T:Infrastructure.Helpers.HttpHelper">
            <summary>
            http请求类
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.#ctor(System.String)">
            <param name="ipaddress">请求的基础IP，例如：http://************:8080/ </param>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.#ctor(System.String,System.String,System.String)">
            <summary>
            创建带用户信息的请求客户端
            </summary>
            <param name="userName">用户账号</param>
            <param name="pwd">用户密码，当WebApi端不要求密码验证时，可传空串</param>
            <param name="uriString">The URI string.</param>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.Get(System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Get请求数据
              /// <para>最终以url参数的方式提交</para>
            <para>wzw 2016-3-3 重构与post同样异步调用</para>
            </summary>
            <param name="parameters">参数字典,可为空</param>
            <param name="requestUri">例如/api/Files/UploadFile</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.Get``1(System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Get请求数据
            <para>最终以url参数的方式提交</para>
            </summary>
            <param name="parameters">参数字典</param>
            <param name="requestUri">例如/api/Files/UploadFile</param>
            <returns>实体对象</returns>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.Post(System.Object,System.String)">
            <summary>
            以json的方式Post数据 返回string类型
            <para>最终以json的方式放置在http体中</para>
            </summary>
            <param name="entity">实体</param>
            <param name="requestUri">例如/api/Files/UploadFile</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.PostDicObj(System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            提交字典类型的数据
            <para>最终以formurlencode的方式放置在http体中</para>
            <para>wzw 2016-07-20 19:01:59</para>
            </summary>
            <returns>System.String.</returns>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.PostDic(System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Post Dic数据
            <para>最终以formurlencode的方式放置在http体中</para>
            <para>wzw 2016-07-15 15:28:41</para>
            </summary>
            <returns>System.String.</returns>
        </member>
        <member name="M:Infrastructure.Helpers.HttpHelper.ConcatURL(System.String)">
            <summary>
            把请求的URL相对路径组合成绝对路径
            <para>wzw 2016-07-21 9:54:07</para>
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.ImgHelper.MakeThumbnail(System.String,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            根据已有图片生成缩略图
            <para>用法：MakeThumbnail(path, tpath, 120, 90, "H");</para>
            </summary>
            <param name="originalImagePath">源图片路径</param>
            <param name="thumbnailPath">缩略图保存路径</param>
            <param name="width">缩略图的宽度</param>
            <param name="height">缩略图高度</param>
            <param name="mode">缩略模式：H:指定高度，宽度按比例处理；W：指定宽度，高度按比例处理；HW按参数指定的高度和宽度</param>
        </member>
        <member name="T:Infrastructure.Helpers.XmlParameter">
            <summary>
            XMLHelper参数
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.XmlParameter.Name">
            <summary>
            节点名称
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.XmlParameter.InnerText">
            <summary>
            节点文本
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.XmlParameter.NamespaceOfPrefix">
            <summary>
            节点前缀xmlns声明(命名空间URI)
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.XmlParameter.Attributes">
            <summary>
            节点属性集
            </summary>
        </member>
        <member name="T:Infrastructure.Helpers.AttributeParameter">
            <summary>
            节点属性参数
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.AttributeParameter.Name">
            <summary>
            属性名称
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.AttributeParameter.Value">
            <summary>
            属性值
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.XMLHelper.XmlPath">
            <summary>
            xml文件路径
            </summary>
        </member>
        <member name="P:Infrastructure.Helpers.XMLHelper.ConfigName">
            <summary>
            配置文件节点名称，请设置在AppSettings节点下
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetConfig">
            <summary>
            从配置文件读取xml路径
            </summary>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AppendChild(System.Xml.XmlDocument,System.Xml.XmlNode,Infrastructure.Helpers.XmlParameter[])">
            <summary>
            添加一个子节点
            </summary>
            <param name="xDoc">XmlDocument对象</param>
            <param name="parentNode">父节点</param>
            <param name="xlParameter">Xml参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetXmlDom">
            <summary>
            获得一个XmlDocument对象
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.CreateXmlFile(System.String,System.String,System.String,Infrastructure.Helpers.XmlParameter[])">
            <summary>
            创建一个XML文档，成功创建后操作路径将直接指向该文件
            </summary>
            <param name="fileName">文件物理路径名</param>
            <param name="rootNode">根结点名称</param>
            <param name="elementName">元素节点名称</param>
            <param name="xmlParameter">XML参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.CreateXmlFile(System.String,System.String)">
            <summary>
            创建一个XML文档，成功创建后操作路径将直接指向该文件
            </summary>
            <param name="fileName">文件物理路径名</param>
            <param name="xmlString">xml字符串</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AddNewNode(System.Xml.XmlNode,Infrastructure.Helpers.XmlParameter[])">
            <summary>
            添加新节点
            </summary>
            <param name="parentNode">新节点的父节点对象</param>
            <param name="xmlParameter">Xml参数对象</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AddNewNode(System.String,Infrastructure.Helpers.XmlParameter[])">
            <summary>
            添加新节点
            </summary>
            <param name="xDoc">XmlDocument对象</param>
            <param name="parentName">新节点的父节点名称</param>
            <param name="xmlParameter">XML参数对象</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AddAttribute(System.Xml.XmlNode,System.String,System.String,System.String)">
            <summary>
            添加节点属性
            </summary>
            <param name="node">节点对象</param>
            <param name="namespaceOfPrefix">该节点的命名空间URI</param>
            <param name="attributeName">新属性名称</param>
            <param name="attributeValue">属性值</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AddAttribute(System.Xml.XmlNode,System.String,Infrastructure.Helpers.AttributeParameter[])">
            <summary>
            添加节点属性
            </summary>
            <param name="node">节点对象</param>
            <param name="namespaceOfPrefix">该节点的命名空间URI</param>
            <param name="attributeParameters">节点属性参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AddAttribute(System.String,System.String,System.String,System.String)">
            <summary>
            添加节点属性
            </summary>
            <param name="nodeName">节点名称</param>
            <param name="namespaceOfPrefix">该节点的命名空间URI</param>
            <param name="attributeName">新属性名称</param>
            <param name="attributeValue">属性值</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.AddAttribute(System.String,System.String,Infrastructure.Helpers.AttributeParameter[])">
            <summary>
            添加节点属性
            </summary>
            <param name="nodeName">节点名称</param>
            <param name="namespaceOfPrefix">该节点的命名空间URI</param>
            <param name="attributeParameters">节点属性参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(System.String)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="nodeName">节点名称</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(System.Xml.XmlNode,System.String)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="node">节点对象</param>
            <param name="nodeName">节点名称</param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(System.Int32,System.String)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="index">节点索引</param>
            <param name="nodeName">节点名称</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(System.Xml.XmlNode,System.String,System.String)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="node">节点对象</param>
            <param name="nodeName">节点名称</param>
            <param name="innerText">节点内容</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(System.String,System.String)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="nodeName">节点名称</param>
            <param name="innerText">节点内容</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(Infrastructure.Helpers.XmlParameter)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="xmlParameter">XML参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.GetNode(System.Xml.XmlNode,Infrastructure.Helpers.XmlParameter)">
            <summary>
            获取指定节点名称的节点对象
            </summary>
            <param name="node">节点对象</param>
            <param name="xmlParameter">XML参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.UpdateNode(System.Int32,Infrastructure.Helpers.XmlParameter)">
            <summary>
            修改节点的内容
            </summary>
            <param name="index">节点索引</param>
            <param name="xmlParameter">XML参数对象</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.UpdateNode(System.Int32,System.String,System.String)">
            <summary>
            修改节点的内容
            </summary>
            <param name="index">节点索引</param>
            <param name="nodeName">节点名称</param>
            <param name="newInnerText">修改后的内容</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.UpdateNode(Infrastructure.Helpers.XmlParameter,System.String,Infrastructure.Helpers.AttributeParameter[])">
            <summary>
            修改节点的内容
            </summary>
            <param name="xmlParameter">XmlParameter对象</param>
            <param name="innerText">修改后的内容</param>
            <param name="attributeParameters">需要修改的属性</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.DeleteNode(System.Int32)">
            <summary>
            删除节点
            </summary>
            <param name="index">节点索引</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.DeleteNode(System.Xml.XmlNode[])">
            <summary>
            删除节点
            </summary>
            <param name="nodeList">需要删除的节点对象</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.DeleteNode(System.String,System.String)">
            <summary>
            删除节点
            </summary>
            <param name="xDoc">XmlDocument对象</param>
            <param name="nodeName">节点名称</param>
            <param name="nodeText">节点内容</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.SetAttribute(System.Xml.XmlElement,Infrastructure.Helpers.AttributeParameter[])">
            <summary>
            修改属性值
            </summary>
            <param name="elem">元素对象</param>
            <param name="attps">属性参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.SetAttribute(Infrastructure.Helpers.XmlParameter,Infrastructure.Helpers.AttributeParameter[])">
            <summary>
            修改属性值
            </summary>
            <param name="xmlParameter">XML参数</param>
            <param name="attributeParameters">属性参数</param>
        </member>
        <member name="M:Infrastructure.Helpers.XMLHelper.SetAttribute(Infrastructure.Helpers.XmlParameter,System.String,System.String)">
            <summary>
            修改属性值
            </summary>
            <param name="xmlParameter">XML参数</param>
            <param name="attributeValue">新属性值</param>
        </member>
        <member name="T:Infrastructure.Json">
            <summary>
            Json操作
            </summary>
        </member>
        <member name="P:Infrastructure.KeyDescription.Key">
            <summary>
            键值
            </summary>
        </member>
        <member name="P:Infrastructure.KeyDescription.Description">
            <summary>
            键的描述
            </summary>
        </member>
        <member name="P:Infrastructure.KeyDescription.Browsable">
            <summary>
            前端是否显示
            </summary>
        </member>
        <member name="P:Infrastructure.KeyDescription.Type">
            <summary>
            字段类型
            </summary>
        </member>
        <member name="T:Infrastructure.Middleware.RequestResponseLoggingMiddleware">
            <summary>
            请求与返回中间件
            </summary>
        </member>
        <member name="M:Infrastructure.Middleware.RequestResponseLoggingMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Logging.ILogger{Infrastructure.Middleware.RequestResponseLoggingMiddleware})">
            <summary>
            
            </summary> 
        </member>
        <member name="M:Infrastructure.Middleware.RequestResponseLoggingMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Middleware.RequestResponseLoggingMiddleware.GetResponse(Microsoft.AspNetCore.Http.HttpResponse)">
            <summary>
            获取响应内容
            </summary>
            <param name="response"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Provider.PathProvider.MapPath(System.String)">
            <summary>
            获取服务器文件路径
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Provider.PathProvider.MapPath(System.String,System.Boolean)">
            <summary>
            获取wwwroot路径
            </summary>
            <param name="path"></param>
            <param name="rootPath">是否获取wwwroot路径</param>
            <returns></returns>
        </member>
        <member name="P:Infrastructure.Response.Message">
            <summary>
            操作消息【当Status不为 200时，显示详细的错误信息】
            </summary>
        </member>
        <member name="P:Infrastructure.Response.Code">
            <summary>
            操作状态码，200为正常
            </summary>
        </member>
        <member name="T:Infrastructure.Response`1">
            <summary>
            WEBAPI通用返回泛型基类
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:Infrastructure.Response`1.Result">
            <summary>
            回传的结果
            </summary>
        </member>
        <member name="T:Infrastructure.Utilities.DynamicPropertyBag">
            <summary>
            动态属性Bag
            </summary>
        </member>
        <member name="M:Infrastructure.Utilities.HttpContextUtil.GetTenantId(Microsoft.AspNetCore.Http.IHttpContextAccessor)">
            <summary>
            获取租户ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Utilities.ProjectPath.GetProjectDirectoryInfo">
            <summary>
            获取web父目录所在位置
            </summary>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Utilities.ProjectPath.GetLastIndexOfDirectoryName(System.String)">
            <summary>
            获取指定结尾的项目名称 
            </summary>
            <param name="lastIndexOfName"></param>
            <returns></returns>
        </member>
        <member name="M:Infrastructure.Utilities.ProjectPath.GetProjectDirectoryInfo(System.IO.DirectoryInfo,System.Int32)">
            <summary>
            获取项目所在路径
            </summary>
            <param name="directoryInfo"></param>
            <returns></returns>
        </member>
        <member name="T:Infrastructure.Utilities.UriUtil">
            <summary>
            URl帮助类
            </summary>
        </member>
        <member name="M:Infrastructure.Utilities.UriUtil.GetAppendedQueryString(System.String,System.String,System.String)">
            <summary>
            在URL后面追加参数
            </summary>
            <param name="url"></param>
            <param name="key"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.Method">
            <summary>
            雪花计算方法
            （1-漂移算法|2-传统算法），默认1
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.BaseTime">
            <summary>
            基础时间（UTC格式）
            不能超过当前系统时间
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.WorkerId">
            <summary>
            机器码
            与 WorkerIdBitLength 有关系
            （ushort类型，最大值65535，如果有更高要求，请修改数据类型，或联系作者)
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.WorkerIdBitLength">
            <summary>
            机器码位长
            范围：1-21（要求：序列数位长+机器码位长不超过22）。
            建议范围：6-12。
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.SeqBitLength">
            <summary>
            序列数位长
            范围：2-21（要求：序列数位长+机器码位长不超过22）。
            建议范围：6-14。
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.MaxSeqNumber">
            <summary>
            最大序列数（含）
            （由SeqBitLength计算的最大值）
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.MinSeqNumber">
            <summary>
            最小序列数（含）
            默认5，不小于1，不大于MaxSeqNumber
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IdGeneratorOptions.TopOverCostCount">
            <summary>
            最大漂移次数（含），
            默认2000，推荐范围500-10000（与计算能力有关）
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.IIdGenerator.GenIdActionAsync">
            <summary>
            生成过程中产生的事件
            </summary>
        </member>
        <member name="M:Yitter.IdGenerator.IIdGenerator.NewLong">
            <summary>
            生成新的long型Id
            </summary>
            <returns></returns>
        </member>
        <member name="T:Yitter.IdGenerator.OverCostActionArg">
            <summary>
            Id生成时回调参数
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.OverCostActionArg.ActionType">
            <summary>
            事件类型
            1-开始，2-结束，8-漂移
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.OverCostActionArg.TimeTick">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.OverCostActionArg.WorkerId">
            <summary>
            机器码
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.OverCostActionArg.OverCostCountInOneTerm">
            <summary>
            漂移计算次数
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.OverCostActionArg.GenCountInOneTerm">
            <summary>
            漂移期间生产ID个数
            </summary>
        </member>
        <member name="P:Yitter.IdGenerator.OverCostActionArg.TermIndex">
            <summary>
            漂移周期
            </summary>
        </member>
        <member name="T:Yitter.IdGenerator.SnowWorkerM1">
            <summary>
            雪花漂移算法
            </summary> 
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.BaseTime">
            <summary>
            基础时间
            </summary>
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.WorkerId">
            <summary>
            机器码
            </summary>
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.WorkerIdBitLength">
            <summary>
            机器码位长
            </summary>
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.SeqBitLength">
            <summary>
            自增序列数位长
            </summary>
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.MaxSeqNumber">
            <summary>
            最大序列数（含）
            </summary>
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.MinSeqNumber">
            <summary>
            最小序列数（含）
            </summary>
        </member>
        <member name="F:Yitter.IdGenerator.SnowWorkerM1.TopOverCostCount">
            <summary>
            最大漂移次数
            </summary>
        </member>
        <member name="T:Yitter.IdGenerator.SnowWorkerM2">
            <summary>
            常规雪花算法
            </summary>
        </member>
        <member name="T:Yitter.IdGenerator.DefaultIdGenerator">
            <summary>
            默认实现
            </summary>
        </member>
        <member name="T:Yitter.IdGenerator.YitIdHelper">
            <summary>
            这是一个调用的例子，默认情况下，单机集成者可以直接使用 NextId()。
            </summary>
        </member>
        <member name="M:Yitter.IdGenerator.YitIdHelper.SetIdGenerator(Yitter.IdGenerator.IdGeneratorOptions)">
            <summary>
            设置参数，建议程序初始化时执行一次
            </summary>
            <param name="options"></param>
        </member>
        <member name="M:Yitter.IdGenerator.YitIdHelper.NextId">
            <summary>
            生成新的Id
            调用本方法前，请确保调用了 SetIdGenerator 方法做初始化。
            否则将会初始化一个WorkerId为1的对象。
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
