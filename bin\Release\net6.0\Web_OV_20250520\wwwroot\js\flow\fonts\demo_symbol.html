
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <script src="iconfont.js"></script>

    <style type="text/css">
        .icon {
          /* 通过设置 font-size 来改变图标大小 */
          width: 1em; height: 1em;
          /* 图标和文字相邻时，垂直对齐 */
          vertical-align: -0.15em;
          /* 通过设置 color 来改变 SVG 的颜色/fill */
          fill: currentColor;
          /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
             normalize.css 中也包含这行 */
          overflow: hidden;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-zhizhen1"></use>
                    </svg>
                    <div class="name">指针</div>
                    <div class="fontclass">#i-zhizhen1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-msnui-close-fat"></use>
                    </svg>
                    <div class="name">关闭</div>
                    <div class="fontclass">#i-msnui-close-fat</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-close"></use>
                    </svg>
                    <div class="name">close</div>
                    <div class="fontclass">#i-close</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-wenjiantianjia"></use>
                    </svg>
                    <div class="name">文件添加</div>
                    <div class="fontclass">#i-wenjiantianjia</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-tag"></use>
                    </svg>
                    <div class="name">tag</div>
                    <div class="fontclass">#i-tag</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-conowredo"></use>
                    </svg>
                    <div class="name">conow-redo</div>
                    <div class="fontclass">#i-conowredo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-conowrevoke"></use>
                    </svg>
                    <div class="name">conow-revoke</div>
                    <div class="fontclass">#i-conowrevoke</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-baocun"></use>
                    </svg>
                    <div class="name">保存</div>
                    <div class="fontclass">#i-baocun</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-refresh"></use>
                    </svg>
                    <div class="name">Refresh</div>
                    <div class="fontclass">#i-refresh</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-youxiashixin"></use>
                    </svg>
                    <div class="name">右下-实心</div>
                    <div class="fontclass">#i-youxiashixin</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-paizhaoanniu"></use>
                    </svg>
                    <div class="name">拍照按钮</div>
                    <div class="fontclass">#i-paizhaoanniu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-huizhang"></use>
                    </svg>
                    <div class="name">徽章</div>
                    <div class="fontclass">#i-huizhang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-hrgongzuotai"></use>
                    </svg>
                    <div class="name">hr工作台</div>
                    <div class="fontclass">#i-hrgongzuotai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-tingzhi-copy"></use>
                    </svg>
                    <div class="name">停止</div>
                    <div class="fontclass">#i-tingzhi-copy</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-wenjianjia"></use>
                    </svg>
                    <div class="name">文件夹</div>
                    <div class="fontclass">#i-wenjianjia</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-webtubiaoku08"></use>
                    </svg>
                    <div class="name">声音</div>
                    <div class="fontclass">#i-webtubiaoku08</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-database"></use>
                    </svg>
                    <div class="name">数据库</div>
                    <div class="fontclass">#i-database</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-chajian1"></use>
                    </svg>
                    <div class="name">插件 (1)</div>
                    <div class="fontclass">#i-chajian1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-caidan"></use>
                    </svg>
                    <div class="name">菜单</div>
                    <div class="fontclass">#i-caidan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-wangluo"></use>
                    </svg>
                    <div class="name">网络</div>
                    <div class="fontclass">#i-wangluo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-dayin"></use>
                    </svg>
                    <div class="name">打印</div>
                    <div class="fontclass">#i-dayin</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-user"></use>
                    </svg>
                    <div class="name">用户</div>
                    <div class="fontclass">#i-user</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-kaishi"></use>
                    </svg>
                    <div class="name">开始</div>
                    <div class="fontclass">#i-kaishi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-hebing"></use>
                    </svg>
                    <div class="name">合并</div>
                    <div class="fontclass">#i-hebing</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-liaotian"></use>
                    </svg>
                    <div class="name">聊天</div>
                    <div class="fontclass">#i-liaotian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-dingshi"></use>
                    </svg>
                    <div class="name">定时</div>
                    <div class="fontclass">#i-dingshi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-xiejiantou"></use>
                    </svg>
                    <div class="name">斜箭头</div>
                    <div class="fontclass">#i-xiejiantou</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-peizhi"></use>
                    </svg>
                    <div class="name">配置</div>
                    <div class="fontclass">#i-peizhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-xiangmuzuhe"></use>
                    </svg>
                    <div class="name">项目组合</div>
                    <div class="fontclass">#i-xiangmuzuhe</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-xitongcanshupeizhi"></use>
                    </svg>
                    <div class="name">系统参数配置</div>
                    <div class="fontclass">#i-xitongcanshupeizhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-qukuai"></use>
                    </svg>
                    <div class="name">区块</div>
                    <div class="fontclass">#i-qukuai</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-fenzhi"></use>
                    </svg>
                    <div class="name">分支</div>
                    <div class="fontclass">#i-fenzhi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-icon-test"></use>
                    </svg>
                    <div class="name">01</div>
                    <div class="fontclass">#i-icon-test</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-icon-test1"></use>
                    </svg>
                    <div class="name">02</div>
                    <div class="fontclass">#i-icon-test1</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-xiaoxuxian"></use>
                    </svg>
                    <div class="name">小虚线</div>
                    <div class="fontclass">#i-xiaoxuxian</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#i-jianchagongjuzhixian"></use>
                    </svg>
                    <div class="name">检查工具  直线</div>
                    <div class="fontclass">#i-jianchagongjuzhixian</div>
                </li>
            
        </ul>


        <h2 id="symbol-">symbol引用</h2>
        <hr>

        <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
        这种用法其实是做了一个svg的集合，与另外两种相比具有如下特点：</p>
        <ul>
          <li>支持多色图标了，不再受单色限制。</li>
          <li>通过一些技巧，支持像字体那样，通过<code>font-size</code>,<code>color</code>来调整样式。</li>
          <li>兼容性较差，支持 ie9+,及现代浏览器。</li>
          <li>浏览器渲染svg的性能一般，还不如png。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-symbol-">第一步：引入项目下面生成的symbol代码：</h3>
        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;</span></code></pre>
        <h3 id="-css-">第二步：加入通用css代码（引入一次就行）：</h3>
        <pre><code class="lang-js hljs javascript">&lt;style type=<span class="hljs-string">"text/css"</span>&gt;
.icon {
   width: <span class="hljs-number">1</span>em; height: <span class="hljs-number">1</span>em;
   vertical-align: <span class="hljs-number">-0.15</span>em;
   fill: currentColor;
   overflow: hidden;
}
&lt;<span class="hljs-regexp">/style&gt;</span></code></pre>
        <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-js hljs javascript">&lt;svg <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"icon"</span> aria-hidden=<span class="hljs-string">"true"</span>&gt;<span class="xml"><span class="hljs-tag">
  &lt;<span class="hljs-name">use</span> <span class="hljs-attr">xlink:href</span>=<span class="hljs-string">"#i-xxx"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">use</span>&gt;</span>
</span>&lt;<span class="hljs-regexp">/svg&gt;
        </span></code></pre>
    </div>
</body>
</html>
