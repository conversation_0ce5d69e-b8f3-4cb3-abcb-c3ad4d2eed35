{
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "AllowedHosts": "*",
  "DataProtection": "temp-keys/",
  "ConnectionStrings": {
    
  },
  "AppSetting": {
    "IdentityServerUrl": "", //IdentityServer服务器地址。如果为空，则不启用OAuth认证
    "Version": "v1.0.9", //如果为demo，则可以防止post提交
    "DbTypes": {
      "OpenAuthDBContext": "SqlServer" //数据库类型：SqlServer、MySql、Oracle、PostgreSQL
    },
    //"RedisConf": "redistest.cq-p.com.cn:8001,password=share_redis@123",  //redis配置
    "HttpHost": "http://*:1803" //启动绑定地址及端口
  }
}




