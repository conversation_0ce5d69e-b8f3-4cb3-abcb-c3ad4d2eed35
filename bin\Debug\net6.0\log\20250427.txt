2025-04-27 08:56:45,841 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (58ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-04-27 08:56:45,945 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-04-27 08:56:46,340 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:56:46,342 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:56:46,342 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:57:21,602 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:57:21,603 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:57:27,843 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:57:27,844 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:13,580 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:13,580 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:13,594 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:14,325 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:14,325 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:33,802 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:33,803 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:33,802 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:34,511 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:58:34,511 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:00,986 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:00,986 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:00,987 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:01,806 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:01,806 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:27,479 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:27,481 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:30,642 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:30,651 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:32,966 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 08:59:32,971 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:01:35,944 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:01:35,944 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:01:35,944 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:01:36,154 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:01:36,154 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:02:50,544 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:02:50,544 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:02:50,544 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:02:50,899 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-27 09:02:50,899 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
